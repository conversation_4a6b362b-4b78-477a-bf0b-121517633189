<?php
/**
 * 管理员登录API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../config/database.php';


try {
    // 只允许POST请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        jsonResponse(false, '只允许POST请求');
    }

    // 获取输入数据
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    if (!$input) {
        error_log("Invalid JSON input: " . $rawInput);
        jsonResponse(false, '无效的JSON数据');
    }

    $action = $input['action'] ?? '';

    if (empty($action)) {
        jsonResponse(false, '缺少操作参数');
    }

    // 连接数据库
    if (!class_exists('Database')) {
        error_log("Database class not found");
        jsonResponse(false, '数据库配置错误');
    }

    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        error_log("Database connection failed");
        jsonResponse(false, '数据库连接失败');
    }

    switch ($action) {
        case 'login':
            adminLogin($db, $input);
            break;
        case 'logout':
            adminLogout($db, $input);
            break;
        case 'check_session':
            checkAdminSession($db, $input);
            break;
        default:
            jsonResponse(false, '无效的操作: ' . $action);
    }

} catch (Exception $e) {
    error_log("Admin login API error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    jsonResponse(false, '服务器内部错误: ' . $e->getMessage());
}

// 管理员登录
function adminLogin($db, $input) {
    try {
        $username = trim($input['username'] ?? '');
        $password = $input['password'] ?? '';

        // 验证输入
        if (empty($username) || empty($password)) {
            jsonResponse(false, '请输入用户名和密码');
        }

        // 查找管理员账号
        $stmt = $db->prepare("SELECT * FROM admin_accounts WHERE username = ? AND status = 'active'");
        $stmt->execute([$username]);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$admin) {
            // 记录失败的登录尝试
            logOperation($db, 'admin', 0, 'login_failed', "登录失败：用户名 {$username} 不存在或已禁用");
            jsonResponse(false, '用户名或密码错误');
        }

        // 验证密码
        if (!password_verify($password, $admin['password'])) {
            // 记录失败的登录尝试
            logOperation($db, 'admin', $admin['id'], 'login_failed', "登录失败：密码错误");
            jsonResponse(false, '用户名或密码错误');
        }

        // 生成会话令牌
        $sessionToken = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', time() + 24 * 60 * 60); // 24小时后过期

        // 创建会话记录
        $stmt = $db->prepare("
            INSERT INTO admin_sessions (admin_id, session_token, ip_address, user_agent, expires_at)
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $admin['id'],
            $sessionToken,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? '',
            $expiresAt
        ]);

        // 更新管理员登录信息
        $stmt = $db->prepare("
            UPDATE admin_accounts 
            SET last_login = NOW(), login_count = login_count + 1 
            WHERE id = ?
        ");
        $stmt->execute([$admin['id']]);

        // 启动会话
        session_start();
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['admin_username'] = $admin['username'];
        $_SESSION['admin_role'] = $admin['role'];
        $_SESSION['session_token'] = $sessionToken;

        // 记录成功的登录
        logOperation($db, 'admin', $admin['id'], 'login', "管理员登录：{$admin['username']}");

        jsonResponse(true, '登录成功', [
            'session_token' => $sessionToken,
            'admin_info' => [
                'id' => $admin['id'],
                'username' => $admin['username'],
                'email' => $admin['email'],
                'full_name' => $admin['full_name'],
                'role' => $admin['role']
            ]
        ]);

    } catch (Exception $e) {
        error_log("Admin login error: " . $e->getMessage());
        jsonResponse(false, '登录失败');
    }
}

// 管理员登出
function adminLogout($db, $input) {
    try {
        $sessionToken = $input['session_token'] ?? '';

        if (empty($sessionToken)) {
            jsonResponse(false, '无效的会话令牌');
        }

        // 查找会话
        $stmt = $db->prepare("
            SELECT s.*, a.username 
            FROM admin_sessions s 
            JOIN admin_accounts a ON s.admin_id = a.id 
            WHERE s.session_token = ? AND s.status = 'active'
        ");
        $stmt->execute([$sessionToken]);
        $session = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($session) {
            // 标记会话为已登出
            $stmt = $db->prepare("UPDATE admin_sessions SET status = 'logout' WHERE session_token = ?");
            $stmt->execute([$sessionToken]);

            // 记录登出操作
            logOperation($db, 'admin', $session['admin_id'], 'logout', "管理员登出：{$session['username']}");
        }

        // 清除PHP会话
        session_start();
        session_destroy();

        jsonResponse(true, '登出成功');

    } catch (Exception $e) {
        error_log("Admin logout error: " . $e->getMessage());
        jsonResponse(false, '登出失败');
    }
}

// 检查管理员会话
function checkAdminSession($db, $input) {
    try {
        $sessionToken = $input['session_token'] ?? '';

        if (empty($sessionToken)) {
            jsonResponse(false, '无效的会话令牌');
        }

        // 查找有效会话
        $stmt = $db->prepare("
            SELECT s.*, a.username, a.email, a.full_name, a.role 
            FROM admin_sessions s 
            JOIN admin_accounts a ON s.admin_id = a.id 
            WHERE s.session_token = ? AND s.status = 'active' AND s.expires_at > NOW()
        ");
        $stmt->execute([$sessionToken]);
        $session = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$session) {
            jsonResponse(false, '会话已过期或无效');
        }

        jsonResponse(true, '会话有效', [
            'admin_info' => [
                'id' => $session['admin_id'],
                'username' => $session['username'],
                'email' => $session['email'],
                'full_name' => $session['full_name'],
                'role' => $session['role']
            ]
        ]);

    } catch (Exception $e) {
        error_log("Check admin session error: " . $e->getMessage());
        jsonResponse(false, '检查会话失败');
    }
}
?>
