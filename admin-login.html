<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三国杀账号管理系统 - 管理员登录</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>👨‍💼</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON>l, sans-serif;
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 48px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 450px;
            width: 100%;
            margin: 20px;
        }
        
        .logo-section {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .logo-icon {
            font-size: 4rem;
            margin-bottom: 16px;
            display: block;
        }
        
        .logo-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 8px;
        }
        
        .logo-subtitle {
            color: #718096;
            font-size: 0.95rem;
        }
        
        .form-floating {
            margin-bottom: 20px;
        }
        
        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #764ba2;
            box-shadow: 0 0 0 3px rgba(118, 75, 162, 0.1);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #764ba2, #667eea);
            border: none;
            border-radius: 12px;
            padding: 16px;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            margin-top: 20px;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(118, 75, 162, 0.3);
            color: white;
        }
        
        .btn-login:disabled {
            opacity: 0.7;
            transform: none;
            box-shadow: none;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            padding: 16px;
            margin-bottom: 20px;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #fed7d7, #feb2b2);
            color: #742a2a;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #c6f6d5, #9ae6b4);
            color: #22543d;
        }
        
        .security-notice {
            background: linear-gradient(135deg, #e6fffa, #b2f5ea);
            color: #234e52;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 24px;
            border-left: 4px solid #38b2ac;
        }
        
        .security-notice h6 {
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .security-notice ul {
            margin: 0;
            padding-left: 20px;
            font-size: 0.9rem;
        }
        
        .footer-links {
            text-align: center;
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #e2e8f0;
        }
        
        .footer-links a {
            color: #764ba2;
            text-decoration: none;
            font-size: 0.9rem;
            margin: 0 12px;
        }
        
        .footer-links a:hover {
            text-decoration: underline;
        }
        
        .admin-features {
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e2e8f0;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            color: #4a5568;
            font-size: 0.9rem;
        }
        
        .feature-icon {
            color: #764ba2;
            margin-right: 12px;
            font-size: 1.1rem;
            width: 20px;
        }
        
        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #718096;
            cursor: pointer;
            z-index: 10;
        }
        
        .password-toggle:hover {
            color: #764ba2;
        }
        
        .form-floating.password-field {
            position: relative;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo-section">
            <span class="logo-icon">👨‍💼</span>
            <h1 class="logo-title">管理后台登录</h1>
            <p class="logo-subtitle">三国杀账号管理系统</p>
        </div>
        
        <div class="security-notice">
            <h6><i class="bi bi-shield-check me-2"></i>安全提醒</h6>
            <ul>
                <li>请使用管理员账号登录</li>
                <li>登录后请及时退出系统</li>
                <li>不要在公共设备上保存密码</li>
            </ul>
        </div>
        
        <div id="alertContainer"></div>
        
        <form id="loginForm">
            <div class="form-floating">
                <input type="text" class="form-control" id="username" placeholder="管理员账号" required>
                <label for="username">
                    <i class="bi bi-person me-2"></i>管理员账号
                </label>
            </div>
            
            <div class="form-floating password-field">
                <input type="password" class="form-control" id="password" placeholder="密码" required>
                <label for="password">
                    <i class="bi bi-lock me-2"></i>密码
                </label>
                <button type="button" class="password-toggle" onclick="togglePassword()">
                    <i class="bi bi-eye" id="passwordToggleIcon"></i>
                </button>
            </div>
            
            <div class="form-check mb-3">
                <input class="form-check-input" type="checkbox" id="rememberMe">
                <label class="form-check-label" for="rememberMe">
                    记住登录状态（7天）
                </label>
            </div>
            
            <button type="submit" class="btn btn-login" id="loginBtn">
                <i class="bi bi-shield-lock me-2"></i>登录管理后台
            </button>
        </form>
        
        <div class="admin-features">
            <h6 class="text-center mb-3">管理功能</h6>
            <div class="feature-item">
                <i class="bi bi-speedometer2 feature-icon"></i>
                <span>实时数据统计和监控</span>
            </div>
            <div class="feature-item">
                <i class="bi bi-people feature-icon"></i>
                <span>客户账号管理和封禁</span>
            </div>
            <div class="feature-item">
                <i class="bi bi-controller feature-icon"></i>
                <span>游戏账号管理</span>
            </div>
            <div class="feature-item">
                <i class="bi bi-journal-text feature-icon"></i>
                <span>操作日志查看</span>
            </div>
            <div class="feature-item">
                <i class="bi bi-key feature-icon"></i>
                <span>密码修改请求处理</span>
            </div>
        </div>
        
        <div class="footer-links">
            <a href="/index.html">返回首页</a>
            <a href="/customer-login.html">客户登录</a>
            <a href="#" onclick="showHelp()">使用帮助</a>
        </div>
    </div>

    <!-- 帮助模态框 -->
    <div class="modal fade" id="helpModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-question-circle me-2"></i>管理员登录帮助
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6><i class="bi bi-info-circle me-2"></i>登录说明</h6>
                    <p>请使用管理员账号和密码登录系统。如果忘记密码，请联系系统管理员。</p>
                    
                    <h6><i class="bi bi-shield me-2"></i>安全建议</h6>
                    <ul>
                        <li>定期更换管理员密码</li>
                        <li>不要在公共场所登录</li>
                        <li>使用完毕后及时退出</li>
                        <li>发现异常及时报告</li>
                    </ul>
                    
                    <h6><i class="bi bi-gear me-2"></i>默认账号</h6>
                    <div class="alert alert-warning">
                        <small>
                            <strong>首次使用：</strong><br>
                            账号：admin<br>
                            密码：admin123<br>
                            <em>请登录后立即修改密码</em>
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeAdminLogin();
        });

        /**
         * 初始化管理员登录页面
         */
        function initializeAdminLogin() {
            const loginForm = document.getElementById('loginForm');
            loginForm.addEventListener('submit', handleAdminLogin);
            
            // 检查是否已经登录
            const adminSession = localStorage.getItem('admin_session');
            if (adminSession) {
                // 验证会话是否有效
                validateAdminSession();
            }
        }

        /**
         * 处理管理员登录
         */
        async function handleAdminLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const rememberMe = document.getElementById('rememberMe').checked;
            const loginBtn = document.getElementById('loginBtn');
            
            if (!username || !password) {
                showAlert('danger', '请输入用户名和密码');
                return;
            }
            
            // 显示加载状态
            loginBtn.disabled = true;
            loginBtn.innerHTML = '<span class="loading-spinner"></span>登录中...';
            
            try {
                // 调用管理员登录API（正式）
                const response = await fetch('api/admin_login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'login',
                        username: username,
                        password: password
                    })
                });

                const result = await response.json();

                if (result.success && result.data && result.data.session_token) {
                    // 保存会话信息
                    const sessionData = {
                        token: result.data.session_token,
                        admin_info: result.data.admin_info,
                        login_time: new Date().toISOString(),
                        remember: rememberMe
                    };

                    if (rememberMe) {
                        localStorage.setItem('admin_session', JSON.stringify(sessionData));
                    } else {
                        sessionStorage.setItem('admin_session', JSON.stringify(sessionData));
                    }

                    showAlert('success', '登录成功，正在跳转...');

                    // 跳转到管理后台
                    setTimeout(() => {
                        window.location.href = '/dashboard.html';
                    }, 1500);
                } else {
                    const errorMessage = result.login_error || result.message || '登录失败';
                    showAlert('danger', errorMessage);
                }
            } catch (error) {
                console.error('登录失败:', error);
                showAlert('danger', '网络错误，请稍后重试');
            } finally {
                // 恢复按钮状态
                loginBtn.disabled = false;
                loginBtn.innerHTML = '<i class="bi bi-shield-lock me-2"></i>登录管理后台';
            }
        }

        /**
         * 模拟管理员登录（实际项目中应该替换为真实的API调用）
         */
        async function simulateAdminLogin(username, password) {
            // 模拟网络延迟
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 简单的验证逻辑（实际项目中应该调用后端API）
            if (username === 'admin' && password === 'admin123') {
                return {
                    success: true,
                    admin_id: 1,
                    username: 'admin',
                    message: '登录成功'
                };
            } else {
                return {
                    success: false,
                    message: '用户名或密码错误'
                };
            }
        }

        /**
         * 验证管理员会话
         */
        function validateAdminSession() {
            const sessionData = localStorage.getItem('admin_session') || sessionStorage.getItem('admin_session');
            
            if (sessionData) {
                try {
                    const session = JSON.parse(sessionData);
                    const loginTime = new Date(session.login_time);
                    const now = new Date();
                    const hoursDiff = (now - loginTime) / (1000 * 60 * 60);
                    
                    // 检查会话是否过期（7天）
                    if (hoursDiff < 168) {
                        // 会话有效，直接跳转
                        window.location.href = '/dashboard.html';
                    } else {
                        // 会话过期，清除
                        localStorage.removeItem('admin_session');
                        sessionStorage.removeItem('admin_session');
                    }
                } catch (error) {
                    console.error('会话验证失败:', error);
                    localStorage.removeItem('admin_session');
                    sessionStorage.removeItem('admin_session');
                }
            }
        }

        /**
         * 切换密码显示
         */
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'bi bi-eye';
            }
        }

        /**
         * 显示帮助
         */
        function showHelp() {
            new bootstrap.Modal(document.getElementById('helpModal')).show();
        }

        /**
         * 显示提示信息
         */
        function showAlert(type, message) {
            const alertContainer = document.getElementById('alertContainer');
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            alertContainer.innerHTML = '';
            alertContainer.appendChild(alertDiv);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 3000);
        }
    </script>
</body>
</html>
