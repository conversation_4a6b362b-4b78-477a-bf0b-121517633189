<?php
// 快速检查operation_logs表数据
require_once __DIR__ . '/../config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>Operation Logs 数据检查</h2>";
    
    // 检查表是否存在
    $stmt = $db->query("SHOW TABLES LIKE 'operation_logs'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color:red'>❌ operation_logs 表不存在</p>";
        exit;
    }
    echo "<p style='color:green'>✅ operation_logs 表存在</p>";
    
    // 检查表结构
    echo "<h3>表结构</h3>";
    $stmt = $db->query("DESCRIBE operation_logs");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<table border='1'><tr><th>字段</th><th>类型</th><th>空值</th><th>键</th><th>默认值</th></tr>";
    foreach ($columns as $col) {
        echo "<tr><td>{$col['Field']}</td><td>{$col['Type']}</td><td>{$col['Null']}</td><td>{$col['Key']}</td><td>{$col['Default']}</td></tr>";
    }
    echo "</table>";
    
    // 检查总记录数
    $stmt = $db->query("SELECT COUNT(*) as total FROM operation_logs");
    $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    echo "<h3>总记录数: {$total}</h3>";
    
    if ($total > 0) {
        // 显示最近10条记录
        echo "<h3>最近10条记录</h3>";
        $stmt = $db->query("SELECT * FROM operation_logs ORDER BY created_at DESC LIMIT 10");
        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1'><tr>";
        foreach (array_keys($logs[0]) as $key) {
            echo "<th>{$key}</th>";
        }
        echo "</tr>";
        
        foreach ($logs as $log) {
            echo "<tr>";
            foreach ($log as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        
        // 按日期统计
        echo "<h3>按日期统计</h3>";
        $stmt = $db->query("SELECT DATE(created_at) as date, COUNT(*) as count FROM operation_logs GROUP BY DATE(created_at) ORDER BY date DESC LIMIT 7");
        $dateStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<table border='1'><tr><th>日期</th><th>记录数</th></tr>";
        foreach ($dateStats as $stat) {
            echo "<tr><td>{$stat['date']}</td><td>{$stat['count']}</td></tr>";
        }
        echo "</table>";
        
        // 按用户类型统计
        echo "<h3>按用户类型统计</h3>";
        $stmt = $db->query("SELECT user_type, COUNT(*) as count FROM operation_logs GROUP BY user_type");
        $typeStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<table border='1'><tr><th>用户类型</th><th>记录数</th></tr>";
        foreach ($typeStats as $stat) {
            echo "<tr><td>{$stat['user_type']}</td><td>{$stat['count']}</td></tr>";
        }
        echo "</table>";
    }
    
    // 测试后台接口
    echo "<h3>测试后台接口</h3>";
    
    // 测试get_operation_logs
    $testData = json_encode(['action' => 'get_operation_logs', 'page' => 1, 'limit' => 5]);
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/api/admin_stats.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $testData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<h4>get_operation_logs 接口测试</h4>";
    echo "<p>HTTP状态码: {$httpCode}</p>";
    echo "<p>响应内容:</p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    
    // 测试get_operation_logs_raw
    $testData = json_encode(['action' => 'get_operation_logs_raw', 'page' => 1, 'limit' => 5]);
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/api/admin_stats.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $testData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<h4>get_operation_logs_raw 接口测试</h4>";
    echo "<p>HTTP状态码: {$httpCode}</p>";
    echo "<p>响应内容:</p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color:red'>错误: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
