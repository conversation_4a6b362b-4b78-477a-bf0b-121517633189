-- 添加密码修改记录表
CREATE TABLE IF NOT EXISTS password_change_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    game_account_id INT NOT NULL,
    customer_id INT NOT NULL,
    old_password VARCHAR(255) NOT NULL COMMENT '旧密码',
    reason ENUM('account_switch', 'security', 'other') DEFAULT 'account_switch' COMMENT '修改原因',
    status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '处理状态',
    admin_id INT DEFAULT NULL COMMENT '处理的管理员ID',
    processed_at DATETIME DEFAULT NULL COMMENT '处理时间',
    notes TEXT DEFAULT NULL COMMENT '管理员备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_game_account_id (game_account_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) COMMENT='密码修改请求记录表';
