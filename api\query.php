<?php
// 启用错误报告但不显示到输出
error_reporting(E_ALL);
ini_set('display_errors', 0);  // 不显示错误到输出
ini_set('log_errors', 1);      // 记录错误到日志

require_once '../config/database.php';

// 开始会话
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

try {
    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        jsonResponse(false, '数据库连接失败', null, 500);
        exit;
    }
} catch (Exception $e) {
    error_log("Database connection error: " . $e->getMessage());
    jsonResponse(false, '数据库连接错误：' . $e->getMessage(), null, 500);
    exit;
}





// 处理请求
$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'POST') {
    try {
        $rawInput = file_get_contents('php://input');
        if (empty($rawInput)) {
            jsonResponse(false, '请求体为空');
            exit;
        }

        $input = json_decode($rawInput, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            jsonResponse(false, 'JSON解析错误：' . json_last_error_msg());
            exit;
        }

        if (!$input) {
            jsonResponse(false, '无效的请求数据');
            exit;
        }

        $action = $input['action'] ?? '';

        switch ($action) {
            case 'query_accounts':
                queryAccounts($db, $input);
                break;
            case 'change_password':
                changePassword($db, $input);
                break;
            default:
                jsonResponse(false, '未知的操作：' . $action);
                break;
        }
    } catch (Exception $e) {
        error_log("Request processing error: " . $e->getMessage());
        jsonResponse(false, '请求处理错误：' . $e->getMessage(), null, 500);
    }
} else {
    jsonResponse(false, '不支持的请求方法：' . $method);
}

/**
 * 查询游戏账号
 */
function queryAccounts($db, $input) {
    $customerAccount = trim($input['customer_account'] ?? '');
    
    if (empty($customerAccount)) {
        jsonResponse(false, '请输入客户账号');
        return;
    }
    
    try {
        // 查询客户账号信息
        $stmt = $db->prepare("SELECT * FROM customer_accounts WHERE account_number = ? AND status = 'active'");
        $stmt->execute([$customerAccount]);
        $customer = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$customer) {
            jsonResponse(false, '客户账号不存在或已过期');
            return;
        }
        
        // 检查账号是否过期和计算剩余时间
        $now = new DateTime();
        $remainingHours = 0;
        $expiresAt = null;

        if (isset($customer['expires_at']) && !empty($customer['expires_at'])) {
            // 如果有expires_at字段，使用它
            $expiresAt = new DateTime($customer['expires_at']);
            if ($now > $expiresAt) {
                jsonResponse(false, '客户账号已过期');
                return;
            }
            $remainingHours = max(0, ($expiresAt->getTimestamp() - $now->getTimestamp()) / 3600);
        } else {
            // 如果没有expires_at字段，使用duration_hours和first_login_time计算
            if (!empty($customer['first_login_time'])) {
                $firstLogin = new DateTime($customer['first_login_time']);
                $elapsed = ($now->getTimestamp() - $firstLogin->getTimestamp()) / 3600;
                $remainingHours = max(0, $customer['duration_hours'] - $elapsed);
            } else {
                // 如果没有首次登录时间，使用完整时长
                $remainingHours = $customer['duration_hours'];
            }

            // 计算过期时间
            if (!empty($customer['first_login_time'])) {
                $firstLogin = new DateTime($customer['first_login_time']);
                $expiresAt = clone $firstLogin;
                $expiresAt->add(new DateInterval('PT' . intval($customer['duration_hours']) . 'H'));
            } else {
                // 如果没有首次登录，从创建时间开始计算
                $createdAt = new DateTime($customer['created_at']);
                $expiresAt = clone $createdAt;
                $expiresAt->add(new DateInterval('PT' . intval($customer['duration_hours']) . 'H'));
            }

            // 检查是否过期
            if ($now > $expiresAt) {
                jsonResponse(false, '客户账号已过期');
                return;
            }
        }
        
        // 查询可用的游戏账号
        $accountType = $customer['account_type'];
        $stmt = $db->prepare("
            SELECT * FROM game_accounts 
            WHERE account_type = ? AND status = 'available' 
            ORDER BY created_at ASC
        ");
        $stmt->execute([$accountType]);
        $gameAccounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 记录查询操作
        logOperation($db, 'customer', $customer['id'], 'query_accounts', "查询游戏账号，客户账号：{$customerAccount}");
        
        // 返回结果
        jsonResponse(true, '查询成功', [
            'customer_info' => [
                'account_number' => $customer['account_number'],
                'account_type' => $customer['account_type'],
                'duration_hours' => $customer['duration_hours'],
                'remaining_hours' => $remainingHours,
                'status' => $customer['status'],
                'expires_at' => $expiresAt ? $expiresAt->format('Y-m-d H:i:s') : null
            ],
            'accounts' => $gameAccounts
        ]);
        
    } catch (PDOException $e) {
        error_log("Database query error: " . $e->getMessage());
        jsonResponse(false, '数据库查询错误，请稍后重试', null, 500);
    } catch (Exception $e) {
        error_log("Query accounts error: " . $e->getMessage());
        jsonResponse(false, '查询失败：' . $e->getMessage(), null, 500);
    }
}

/**
 * 修改游戏账号密码
 */
function changePassword($db, $input) {
    $customerAccount = trim($input['customer_account'] ?? '');
    $gameAccountId = intval($input['game_account_id'] ?? 0);
    $newPassword = trim($input['new_password'] ?? '');
    
    if (empty($customerAccount) || $gameAccountId <= 0 || empty($newPassword)) {
        jsonResponse(false, '参数不完整');
        return;
    }
    
    if (strlen($newPassword) < 6) {
        jsonResponse(false, '密码长度至少6位');
        return;
    }
    
    try {
        // 验证客户账号
        $stmt = $db->prepare("SELECT * FROM customer_accounts WHERE account_number = ? AND status = 'active'");
        $stmt->execute([$customerAccount]);
        $customer = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$customer) {
            jsonResponse(false, '客户账号不存在或已过期');
            return;
        }
        
        // 检查账号是否过期
        $now = new DateTime();
        $expiresAt = new DateTime($customer['expires_at']);
        
        if ($now > $expiresAt) {
            jsonResponse(false, '客户账号已过期，无法修改密码');
            return;
        }
        
        // 验证游戏账号
        $stmt = $db->prepare("SELECT * FROM game_accounts WHERE id = ? AND account_type = ?");
        $stmt->execute([$gameAccountId, $customer['account_type']]);
        $gameAccount = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$gameAccount) {
            jsonResponse(false, '游戏账号不存在或类型不匹配');
            return;
        }
        
        // 检查密码修改限制（改用 customer_id 以匹配现有表结构）
        $stmt = $db->prepare("
            SELECT COUNT(*) as count FROM password_change_records
            WHERE customer_id = ? AND game_account_id = ?
            AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ");
        $stmt->execute([$customer['id'], $gameAccountId]);
        $recentChanges = $stmt->fetch(PDO::FETCH_ASSOC);

        if (($recentChanges['count'] ?? 0) >= 3) {
            jsonResponse(false, '密码修改过于频繁，请1小时后再试');
            return;
        }

        // 更新游戏账号密码
        $stmt = $db->prepare("UPDATE game_accounts SET password = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$newPassword, $gameAccountId]);

        // 记录密码修改
        $stmt = $db->prepare("
            INSERT INTO password_change_records
            (customer_id, game_account_id, old_password, new_password, status, created_at)
            VALUES (?, ?, ?, ?, 'completed', NOW())
        ");
        $stmt->execute([$customer['id'], $gameAccountId, $gameAccount['password'], $newPassword]);
        
        // 记录操作日志
        logOperation($db, 'customer', $customer['id'], 'change_password', "修改游戏账号密码，游戏账号：{$gameAccount['account']}");
        
        jsonResponse(true, '密码修改成功');
        
    } catch (Exception $e) {
        jsonResponse(false, '密码修改失败：' . $e->getMessage());
    }
}
?>
