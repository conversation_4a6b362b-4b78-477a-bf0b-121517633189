<?php
/**
 * 数据库配置文件
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'sanguosha';
    private $username = 'sanguosha';
    private $password = '983315523';
    private $charset = 'utf8mb4';
    public $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $exception) {
            error_log("Database connection failed: " . $exception->getMessage());
            // 不要直接输出错误信息，这会破坏JSON响应
            $this->conn = null;
        }
        
        return $this->conn;
    }
}

/**
 * 通用响应函数
 */
function jsonResponse($success, $message = '', $data = null, $code = 200) {
    http_response_code($code);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 记录操作日志
 */
function logOperation($db, $userType, $userId, $action, $description = '', $ipAddress = null) {
    try {
        if (!$ipAddress) {
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
        }

        // 优先写入包含 details 字段的版本
        try {
            $sql = "INSERT INTO operation_logs (user_type, user_id, action, description, details, ip_address, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, NOW())";
            $stmt = $db->prepare($sql);
            $stmt->execute([$userType, $userId, $action, $description, $description, $ipAddress]);
        } catch (Exception $e1) {
            // 回退到不含 details 的老版本表结构
            try {
                $sql = "INSERT INTO operation_logs (user_type, user_id, action, description, ip_address, created_at)
                        VALUES (?, ?, ?, ?, ?, NOW())";
                $stmt = $db->prepare($sql);
                $stmt->execute([$userType, $userId, $action, $description, $ipAddress]);
            } catch (Exception $e2) {
                // 最后回退到旧日志表 logs（如果存在）
                try {
                    $sql = "INSERT INTO logs (actor_type, actor_id, action, details, ip, created_at)
                            VALUES (?, ?, ?, ?, ?, NOW())";
                    $stmt = $db->prepare($sql);
                    $stmt->execute([$userType, $userId, $action, $description, $ipAddress]);
                } catch (Exception $e3) {
                    error_log("Log operation failed (all fallbacks): " . $e3->getMessage());
                }
            }
        }
    } catch (Exception $e) {
        // 日志记录失败不影响主要功能
        error_log("Log operation failed: " . $e->getMessage());
    }
}



/**
 * 验证客户登录状态
 */
function checkCustomerAuth() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    if (!isset($_SESSION['customer_id'])) {
        jsonResponse(false, '请先登录', null, 401);
    }
    return $_SESSION['customer_id'];
}

/**
 * 生成8位数字账号
 */
function generateAccountNumber($db) {
    do {
        $accountNumber = str_pad(mt_rand(********, ********), 8, '0', STR_PAD_LEFT);
        $sql = "SELECT COUNT(*) FROM customer_accounts WHERE account_number = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$accountNumber]);
        $exists = $stmt->fetchColumn() > 0;
    } while ($exists);
    
    return $accountNumber;
}

/**
 * 更新客户剩余时长
 */
function updateCustomerRemainingTime($db, $customerId) {
    $sql = "SELECT first_login_time, duration_hours FROM customer_accounts WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$customerId]);
    $customer = $stmt->fetch();
    
    if ($customer && $customer['first_login_time']) {
        $firstLogin = new DateTime($customer['first_login_time']);
        $now = new DateTime();
        $elapsed = $now->diff($firstLogin);
        $elapsedHours = $elapsed->days * 24 + $elapsed->h + $elapsed->i / 60 + $elapsed->s / 3600;
        
        $remainingHours = max(0, $customer['duration_hours'] - $elapsedHours);
        
        $status = $remainingHours <= 0 ? 'expired' : 'active';
        
        $updateSql = "UPDATE customer_accounts SET remaining_hours = ?, status = ? WHERE id = ?";
        $updateStmt = $db->prepare($updateSql);
        $updateStmt->execute([$remainingHours, $status, $customerId]);
        
        return $remainingHours;
    }
    
    return null;
}

/**
 * 检查时长警告
 */
function checkTimeWarning($remainingHours) {
    $remainingMinutes = $remainingHours * 60;
    
    if ($remainingMinutes <= 10) {
        return '剩余时长不足10分钟，如需延时请联系客服';
    } elseif ($remainingMinutes <= 20) {
        return '剩余时长不足20分钟，如需延时请联系客服';
    } elseif ($remainingMinutes <= 30) {
        return '剩余时长不足30分钟，如需延时请联系客服';
    }
    
    return null;
}

/**
 * 处理CORS
 */
function handleCORS() {
    header("Access-Control-Allow-Origin: *");
    header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type, Authorization");
    
    if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
        exit(0);
    }
}

// 处理CORS
handleCORS();

// 设置时区
date_default_timezone_set('Asia/Shanghai');
?>
