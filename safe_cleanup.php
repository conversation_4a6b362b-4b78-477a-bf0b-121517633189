<?php
/**
 * 🧹 安全项目文件清理工具
 * 只删除测试文件、文档文件和临时文件，保留所有核心功能文件
 * 确保系统正常运行不受影响
 */

// 核心系统文件列表（绝对不会删除的文件）
$coreFiles = [
    // 主要页面
    'index.html',
    'dashboard.html',
    'customer-dashboard.html',
    'customer-login.html',
    'admin-login.html',
    
    // 配置文件
    'config.php',
    'config/database.php',
    '.user.ini',
    
    // 核心API
    'api/customer_new.php',
    'api/admin_login.php',
    'api/admin_stats.php',
    'api/password_change.php',
    'api/query.php',
    'api/clear_operation_logs.php',
    'api/clear_search_keywords.php',
    'api/change_password_simple.php',
    'api/simple_change_password.php',
    
    // 管理后台
    'admin/games.php',
    'admin/games_new.php',
    'admin/games_optimized.php',
    'admin/customers.php',
    'admin/change_password.php',
    'admin/pending_passwords.php',
    'admin/check_logs.php',
    
    // 样式和脚本
    'css/customer-dashboard.css',
    'js/customer-dashboard.js',
    'js/dashboard.js',
    
    // 定时任务
    'cron/update_remaining_time.php',
    'cron/check_expiry.php',
    'cron_update_time.php',
    'update_remaining_time.php',
    
    // 数据库相关
    'database/add_password_change_table.sql',
    'database/update_password_change_records_reason.sql',
    'install.sql',
    '简化迁移脚本.sql',
    
    // 其他核心文件
    'inc/db.php',
    'favicon.ico',
    'install.lock',
    
    // 安装和管理文件
    'one_click_install.php',
    'install.php',
    'clean_project.php',
    'enhanced-customer-api.php',
    'safe_cleanup.php'
];

// 要删除的具体文件列表（明确指定，避免误删）
$deleteFiles = [
    // 测试文件
    'test_admin_login.html',
    'test_count_display.html', 
    'test_data_parsing.html',
    'test_password_change.html',
    'test_tag_styles.html',
    'api/test_api.php',
    
    // 文档和说明文件
    'mobile_copy_optimization_summary.md',
    'mobile_image2_effect_summary.md',
    'mobile_image_effect_summary.md',
    'mobile_issue_solution.md',
    'mobile_optimization_summary.md',
    'optimization_summary.md',
    'desktop_style_mobile_summary.md',
    'details_optimization_summary.md',
    'final_fix_summary.md',
    'password_change_fix_summary.md',
    'password_fix_update.md',
    'rental_terms_feature_summary.md',
    'ultra_compact_optimization_summary.md',
    'universal_copy_solution.md',
    
    // 测试和演示页面
    'mobile_copy_test.html',
    'mobile_image_effect_test.html',
    'mobile_optimization_test.html',
    'mobile_ultra_compact_test.html',
    'ultra_compact_mobile_test.html',
    'desktop_style_mobile_test.html',
    'details_optimization_test.html',
    'optimization_test.html',
    'rental_terms_test.html',
    'universal_copy_test.html',
    'tag_comparison.html',
    'final_tag_test.html',
    'admin_input_guide.html',
    
    // 压缩包和备份文件
    'sanguosha.lanjuan.xin_XYk8p.zip'
];

// 可选删除的空目录
$optionalCleanDirs = [
    'logs'
];

// 开始输出HTML
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧹 安全项目文件清理工具</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            max-width: 1000px; 
            margin: 30px auto; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { 
            background: white; 
            padding: 40px; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 { 
            color: #333; 
            border-bottom: 3px solid #007bff; 
            padding-bottom: 15px; 
            font-size: 2rem;
            text-align: center;
        }
        .success { 
            background: linear-gradient(135deg, #d4edda, #c3e6cb); 
            color: #155724; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 15px 0; 
            border-left: 5px solid #28a745;
            box-shadow: 0 2px 10px rgba(40, 167, 69, 0.2);
        }
        .warning { 
            background: linear-gradient(135deg, #fff3cd, #ffeaa7); 
            color: #856404; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 15px 0; 
            border-left: 5px solid #ffc107;
            box-shadow: 0 2px 10px rgba(255, 193, 7, 0.2);
        }
        .error { 
            background: linear-gradient(135deg, #f8d7da, #fab1a0); 
            color: #721c24; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 15px 0; 
            border-left: 5px solid #dc3545;
            box-shadow: 0 2px 10px rgba(220, 53, 69, 0.2);
        }
        .info { 
            background: linear-gradient(135deg, #d1ecf1, #74b9ff); 
            color: #0c5460; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 15px 0; 
            border-left: 5px solid #17a2b8;
            box-shadow: 0 2px 10px rgba(23, 162, 184, 0.2);
        }
        .file-list { 
            max-height: 400px; 
            overflow-y: auto; 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 15px 0;
            border: 1px solid #dee2e6;
            font-family: monospace;
            font-size: 0.9rem;
        }
        .btn { 
            background: linear-gradient(135deg, #007bff, #0056b3); 
            color: white; 
            padding: 12px 25px; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            margin: 8px; 
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }
        .btn:hover { 
            background: linear-gradient(135deg, #0056b3, #004085); 
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }
        .btn.danger { 
            background: linear-gradient(135deg, #dc3545, #c82333);
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }
        .btn.danger:hover { 
            background: linear-gradient(135deg, #c82333, #a71e2a);
            box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
        }
        .stats { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 20px; 
            margin: 20px 0; 
        }
        .stat-card { 
            background: linear-gradient(135deg, #f8f9fa, #e9ecef); 
            padding: 20px; 
            border-radius: 10px; 
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .stat-number { 
            font-size: 2rem; 
            font-weight: bold; 
            color: #007bff; 
        }
        .stat-label { 
            color: #6c757d; 
            font-size: 0.9rem; 
        }
        .preview-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .file-item {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .file-item:last-child {
            border-bottom: none;
        }
        .file-exists {
            color: #dc3545;
            font-weight: bold;
        }
        .file-missing {
            color: #6c757d;
            text-decoration: line-through;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 安全项目文件清理工具</h1>
        <p style="text-align: center; font-size: 1.1rem; color: #666; margin-bottom: 30px;">
            安全清理测试文件、文档文件和临时文件，保留所有核心功能文件
        </p>

<?php
if (isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action === 'clean') {
        echo "<h3>🚀 开始清理文件...</h3>";
        
        $deletedFiles = [];
        $errors = [];
        $skippedFiles = [];
        
        // 删除指定的文件
        foreach ($deleteFiles as $file) {
            if (file_exists($file)) {
                // 双重检查：确保不是核心文件
                if (in_array($file, $coreFiles)) {
                    $skippedFiles[] = $file . " (核心文件，已跳过)";
                    continue;
                }
                
                if (unlink($file)) {
                    $deletedFiles[] = $file;
                } else {
                    $errors[] = "无法删除: $file";
                }
            }
        }
        
        // 清理空的日志目录
        foreach ($optionalCleanDirs as $dir) {
            if (is_dir($dir)) {
                $files = scandir($dir);
                $files = array_diff($files, array('.', '..'));
                if (empty($files)) {
                    if (rmdir($dir)) {
                        $deletedFiles[] = $dir . "/ (空目录)";
                    }
                }
            }
        }
        
        // 显示统计信息
        echo "<div class='stats'>";
        echo "<div class='stat-card'>";
        echo "<div class='stat-number'>" . count($deletedFiles) . "</div>";
        echo "<div class='stat-label'>已删除文件</div>";
        echo "</div>";
        echo "<div class='stat-card'>";
        echo "<div class='stat-number'>" . count($errors) . "</div>";
        echo "<div class='stat-label'>删除失败</div>";
        echo "</div>";
        echo "<div class='stat-card'>";
        echo "<div class='stat-number'>" . count($skippedFiles) . "</div>";
        echo "<div class='stat-label'>跳过文件</div>";
        echo "</div>";
        echo "</div>";
        
        // 显示删除成功的文件
        if (!empty($deletedFiles)) {
            echo "<div class='success'>";
            echo "<h4>✅ 成功删除的文件 (" . count($deletedFiles) . " 个):</h4>";
            echo "<div class='file-list'>";
            foreach ($deletedFiles as $file) {
                echo "✓ " . htmlspecialchars($file) . "<br>";
            }
            echo "</div>";
            echo "</div>";
        }
        
        // 显示跳过的文件
        if (!empty($skippedFiles)) {
            echo "<div class='warning'>";
            echo "<h4>⚠️ 跳过的文件 (" . count($skippedFiles) . " 个):</h4>";
            echo "<div class='file-list'>";
            foreach ($skippedFiles as $file) {
                echo "⚠ " . htmlspecialchars($file) . "<br>";
            }
            echo "</div>";
            echo "</div>";
        }
        
        // 显示错误
        if (!empty($errors)) {
            echo "<div class='error'>";
            echo "<h4>❌ 删除失败的文件 (" . count($errors) . " 个):</h4>";
            echo "<div class='file-list'>";
            foreach ($errors as $error) {
                echo "✗ " . htmlspecialchars($error) . "<br>";
            }
            echo "</div>";
            echo "</div>";
        }
        
        if (empty($deletedFiles) && empty($errors)) {
            echo "<div class='info'>";
            echo "<h4>ℹ️ 没有找到需要清理的文件</h4>";
            echo "<p>所有指定的文件都已经不存在，或者项目已经很干净了！</p>";
            echo "</div>";
        }
        
        echo "<div class='success'>";
        echo "<h4>🎉 清理完成！</h4>";
        echo "<p>系统核心文件已受到保护，功能不会受到影响。</p>";
        echo "</div>";
        
    } elseif ($action === 'preview') {
        echo "<h3>📋 预览将要删除的文件</h3>";
        
        $existingFiles = [];
        $missingFiles = [];
        
        foreach ($deleteFiles as $file) {
            if (file_exists($file)) {
                $existingFiles[] = $file;
            } else {
                $missingFiles[] = $file;
            }
        }
        
        echo "<div class='stats'>";
        echo "<div class='stat-card'>";
        echo "<div class='stat-number'>" . count($existingFiles) . "</div>";
        echo "<div class='stat-label'>存在的文件</div>";
        echo "</div>";
        echo "<div class='stat-card'>";
        echo "<div class='stat-number'>" . count($missingFiles) . "</div>";
        echo "<div class='stat-label'>不存在的文件</div>";
        echo "</div>";
        echo "<div class='stat-card'>";
        echo "<div class='stat-number'>" . count($coreFiles) . "</div>";
        echo "<div class='stat-label'>受保护的核心文件</div>";
        echo "</div>";
        echo "</div>";
        
        if (!empty($existingFiles)) {
            echo "<div class='warning'>";
            echo "<h4>⚠️ 将要删除的文件 (" . count($existingFiles) . " 个):</h4>";
            echo "<div class='file-list'>";
            foreach ($existingFiles as $file) {
                echo "<div class='file-item file-exists'>🗑️ " . htmlspecialchars($file) . "</div>";
            }
            echo "</div>";
            echo "</div>";
        }
        
        if (!empty($missingFiles)) {
            echo "<div class='info'>";
            echo "<h4>ℹ️ 不存在的文件 (" . count($missingFiles) . " 个):</h4>";
            echo "<div class='file-list'>";
            foreach ($missingFiles as $file) {
                echo "<div class='file-item file-missing'>❌ " . htmlspecialchars($file) . "</div>";
            }
            echo "</div>";
            echo "</div>";
        }
    }
} else {
    // 显示主界面
    echo "<div class='info'>";
    echo "<h3>🛡️ 安全保护机制</h3>";
    echo "<ul>";
    echo "<li><strong>核心文件保护：</strong>所有系统核心文件都在保护列表中，绝不会被删除</li>";
    echo "<li><strong>明确删除列表：</strong>只删除明确指定的测试文件和文档文件</li>";
    echo "<li><strong>预览功能：</strong>可以先预览将要删除的文件，确认后再执行</li>";
    echo "<li><strong>详细日志：</strong>显示删除结果的详细信息</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='warning'>";
    echo "<h3>⚠️ 将要清理的文件类型</h3>";
    echo "<ul>";
    echo "<li><strong>测试文件：</strong>test_*.html, test_*.php 等测试页面</li>";
    echo "<li><strong>文档文件：</strong>*.md 说明文档和总结文件</li>";
    echo "<li><strong>演示页面：</strong>各种功能测试和演示页面</li>";
    echo "<li><strong>临时文件：</strong>压缩包、备份文件等</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<form method='post' style='display: inline-block; margin: 0 10px;'>";
    echo "<input type='hidden' name='action' value='preview'>";
    echo "<button type='submit' class='btn'>📋 预览要删除的文件</button>";
    echo "</form>";
    
    echo "<form method='post' style='display: inline-block; margin: 0 10px;'>";
    echo "<input type='hidden' name='action' value='clean'>";
    echo "<button type='submit' class='btn danger' onclick='return confirm(\"确定要执行清理操作吗？这个操作不可撤销！\")'>🧹 开始清理</button>";
    echo "</form>";
    echo "</div>";
}
?>

    </div>
</body>
</html>
