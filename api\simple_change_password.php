<?php
/**
 * 管理员密码修改API
 * 专门处理管理员密码修改功能
 */

// 开启输出缓冲
ob_start();

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// 注册错误处理函数
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        ob_clean();
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'success' => false,
            'message' => 'PHP致命错误：' . $error['message'],
            'data' => [
                'file' => $error['file'],
                'line' => $error['line'],
                'type' => $error['type']
            ],
            'timestamp' => time()
        ], JSON_UNESCAPED_UNICODE);
    }
});

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入数据库配置
try {
    require_once '../config/database.php';
} catch (Exception $e) {
    error_log("Database config include error: " . $e->getMessage());
    jsonResponse(false, '数据库配置文件加载失败', null, 500);
}

// 开始会话
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * 返回JSON响应
 */
function jsonResponse($success, $message, $data = null, $code = 200) {
    // 清理输出缓冲
    if (ob_get_level()) {
        ob_clean();
    }

    // 设置响应头
    http_response_code($code);
    header('Content-Type: application/json; charset=utf-8');
    header('Cache-Control: no-cache, must-revalidate');

    // 构建响应数据
    $response = [
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => time(),
        'datetime' => date('Y-m-d H:i:s')
    ];

    // 输出JSON
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

    // 刷新输出
    if (ob_get_level()) {
        ob_end_flush();
    }
    flush();
    exit;
}

/**
 * 记录操作日志
 */
function logOperation($db, $userType, $userId, $action, $description) {
    try {
        $stmt = $db->prepare("
            INSERT INTO operation_logs (user_type, user_id, action, description, ip_address, created_at) 
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([$userType, $userId, $action, $description, $_SERVER['REMOTE_ADDR'] ?? '']);
    } catch (Exception $e) {
        error_log("Log operation error: " . $e->getMessage());
    }
}

/**
 * 检测管理员表名
 */
function getAdminTableName($db) {
    try {
        // 首先检查 admin_accounts 表
        $stmt = $db->query("SHOW TABLES LIKE 'admin_accounts'");
        if ($stmt && $stmt->rowCount() > 0) {
            return 'admin_accounts';
        }
        
        // 然后检查 admin 表
        $stmt = $db->query("SHOW TABLES LIKE 'admin'");
        if ($stmt && $stmt->rowCount() > 0) {
            return 'admin';
        }
        
        return null;
    } catch (Exception $e) {
        error_log("Get admin table name error: " . $e->getMessage());
        return null;
    }
}

/**
 * 获取密码字段名
 */
function getPasswordFieldName($db, $tableName) {
    try {
        $stmt = $db->query("DESCRIBE $tableName");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (in_array('password', $columns)) {
            return 'password';
        } elseif (in_array('password_hash', $columns)) {
            return 'password_hash';
        }
        
        return null;
    } catch (Exception $e) {
        error_log("Get password field name error: " . $e->getMessage());
        return null;
    }
}

// 处理请求
try {
    // 连接数据库
    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        jsonResponse(false, '数据库连接失败', null, 500);
    }

    // 处理GET请求（用于测试）
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        jsonResponse(true, 'API服务正常', ['status' => 'ok', 'method' => 'GET']);
    }

    // 只处理POST请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        jsonResponse(false, '只支持POST请求', null, 405);
    }
    
    // 获取请求数据
    $rawInput = file_get_contents('php://input');
    if (empty($rawInput)) {
        jsonResponse(false, '请求体为空');
    }

    $input = json_decode($rawInput, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        jsonResponse(false, 'JSON解析错误：' . json_last_error_msg());
    }

    if (!$input) {
        jsonResponse(false, '无效的请求数据');
    }
    
    // 验证管理员会话
    if (!isset($_SESSION['admin_id']) || $_SESSION['admin_id'] <= 0) {
        jsonResponse(false, '需要管理员权限，请先登录', null, 401);
    }
    
    // 获取输入参数
    $currentPassword = $input['current_password'] ?? '';
    $newPassword = $input['new_password'] ?? '';
    $confirmPassword = $input['confirm_password'] ?? '';
    
    // 验证输入
    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        jsonResponse(false, '请填写所有密码字段');
    }
    
    if ($newPassword !== $confirmPassword) {
        jsonResponse(false, '新密码和确认密码不匹配');
    }
    
    if (strlen($newPassword) < 6) {
        jsonResponse(false, '新密码长度至少6位');
    }
    
    if ($newPassword === $currentPassword) {
        jsonResponse(false, '新密码不能与当前密码相同');
    }
    
    // 获取管理员ID
    $adminId = $_SESSION['admin_id'];
    
    // 根据测试结果，优先使用admin_accounts表
    $tableName = 'admin_accounts';
    $passwordField = 'password';

    // 检查表是否存在
    try {
        $stmt = $db->query("SELECT 1 FROM $tableName LIMIT 1");
    } catch (Exception $e) {
        // 如果admin_accounts不存在，尝试admin表
        $tableName = 'admin';
        $passwordField = 'password';
        try {
            $stmt = $db->query("SELECT 1 FROM $tableName LIMIT 1");
        } catch (Exception $e2) {
            jsonResponse(false, '管理员表不存在');
        }
    }
    
    // 查询管理员信息
    $stmt = $db->prepare("SELECT * FROM $tableName WHERE id = ?");
    $stmt->execute([$adminId]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        jsonResponse(false, '管理员账号不存在');
    }
    
    // 验证当前密码
    if (!password_verify($currentPassword, $admin[$passwordField])) {
        jsonResponse(false, '当前密码错误');
    }
    
    // 更新密码
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    // 构建更新SQL
    $updateSql = "UPDATE $tableName SET $passwordField = ?";
    $updateParams = [$hashedPassword];
    
    // 如果表有 updated_at 字段，则更新它
    $stmt = $db->query("DESCRIBE $tableName");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    if (in_array('updated_at', $columns)) {
        $updateSql .= ", updated_at = NOW()";
    }
    
    $updateSql .= " WHERE id = ?";
    $updateParams[] = $adminId;
    
    $stmt = $db->prepare($updateSql);
    $stmt->execute($updateParams);
    
    if ($stmt->rowCount() > 0) {
        // 记录操作日志
        $username = $admin['username'] ?? 'unknown';
        logOperation($db, 'admin', $adminId, 'change_password', "管理员 {$username} 修改密码");
        
        jsonResponse(true, '密码修改成功');
    } else {
        jsonResponse(false, '密码修改失败，请稍后重试');
    }
    
} catch (PDOException $e) {
    error_log("Change admin password PDO error: " . $e->getMessage());
    error_log("PDO Error Code: " . $e->getCode());
    error_log("PDO Error Info: " . print_r($e->errorInfo ?? [], true));
    jsonResponse(false, '数据库操作失败，请稍后重试', ['error_code' => $e->getCode()], 500);
} catch (Exception $e) {
    error_log("Change admin password error: " . $e->getMessage());
    error_log("Error trace: " . $e->getTraceAsString());
    jsonResponse(false, '系统错误：' . $e->getMessage(), ['error_line' => $e->getLine()], 500);
}
?>
