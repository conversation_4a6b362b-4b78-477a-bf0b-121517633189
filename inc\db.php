<?php
require_once __DIR__ . '/../config.php';

function db(): PDO {
    static $pdo = null;
    if ($pdo === null) {
        $dsn = 'mysql:host=' . DB_HOST . ';port=' . DB_PORT . ';dbname=' . DB_NAME . ';charset=utf8mb4';
        $pdo = new PDO($dsn, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
    }
    return $pdo;
}

function now(): string { return date('Y-m-d H:i:s'); }

function ip(): string { return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0'; }

function start_session() {
    if (session_status() === PHP_SESSION_NONE) {
        session_name(defined('SESSION_NAME') ? SESSION_NAME : 'sgs_sess');
        session_start();
    }
}

function require_admin_login() {
    start_session();
    if (empty($_SESSION['admin_id'])) {
        header('Location: /admin/login.php');
        exit;
    }
}

function require_customer_login() {
    start_session();
    if (empty($_SESSION['customer_id'])) {
        header('Location: /user/login.php');
        exit;
    }
}

function log_event(string $actor_type, ?int $actor_id, string $action, array $details = []) {
    try {
        // 首先尝试插入到operation_logs表（新版本）
        $stmt = db()->prepare('INSERT INTO operation_logs (user_type, user_id, action, description, ip_address, created_at) VALUES (?,?,?,?,?,NOW())');
        $stmt->execute([$actor_type, $actor_id, $action, json_encode($details, JSON_UNESCAPED_UNICODE), ip()]);
    } catch (Throwable $e) {
        try {
            // 如果operation_logs表不存在，尝试插入到logs表（旧版本）
            $stmt = db()->prepare('INSERT INTO logs (actor_type, actor_id, action, details, ip, created_at) VALUES (?,?,?,?,?,NOW())');
            $stmt->execute([$actor_type, $actor_id, $action, json_encode($details, JSON_UNESCAPED_UNICODE), ip()]);
        } catch (Throwable $e2) {
            // ignore logging errors
        }
    }
}

