2025-08-20 14:06:45 [CLIENT-API] API请求开始 | {"method":"GET","uri":"\/api\/client-compatible.php","ip":"***************","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","content_type":""}
2025-08-20 14:06:45 [CLIENT-API] 配置文件加载成功
2025-08-20 14:06:45 [CLIENT-API] 原始请求数据 | {"raw":"","post":[],"get":[]}
2025-08-20 14:06:45 [CLIENT-API] 最终请求数据 | []
2025-08-20 14:06:45 [CLIENT-API] 标准化参数 | {"action":null,"customer_account":null}
2025-08-20 14:06:45 [CLIENT-API] 从URL推断action | {"uri":"\/api\/client-compatible.php","action":null}
2025-08-20 14:06:45 [CLIENT-API] API错误 | {"error":"PDO::__construct(): Argument #1 ($dsn) must be a valid data source name","trace":"#0 \/www\/wwwroot\/sanguosha.lanjuan.xin\/api\/client-compatible.php(121): PDO->__construct()\n#1 {main}"}
2025-08-20 14:06:45 [CLIENT-API] API请求结束
