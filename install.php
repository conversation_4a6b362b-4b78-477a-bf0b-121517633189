<?php
// 一键安装脚本（适配宝塔面板/阿里云）
// 用途：初始化数据库结构、写入配置、创建默认管理员
// 访问：将本项目上传到站点根目录后，浏览器打开 http(s)://你的域名/install.php

ini_set('display_errors', 0);
error_reporting(E_ALL);
header('Content-Type: text/html; charset=utf-8');

define('LOCK_FILE', __DIR__ . '/install.lock');

function h($s){ return htmlspecialchars((string)$s, ENT_QUOTES, 'UTF-8'); }
function json_ok($data){ header('Content-Type: application/json'); echo json_encode(['success'=>true,'data'=>$data], JSON_UNESCAPED_UNICODE); exit; }
function json_err($msg, $extra = []){ header('Content-Type: application/json'); http_response_code(400); echo json_encode(['success'=>false,'message'=>$msg]+$extra, JSON_UNESCAPED_UNICODE); exit; }

// 检查PHP环境
$requirements = [
  'PHP >= 7.2' => version_compare(PHP_VERSION, '7.2.0', '>='),
  'PDO' => extension_loaded('pdo'),
  'pdo_mysql' => extension_loaded('pdo_mysql'),
  'json' => extension_loaded('json'),
  'mbstring' => extension_loaded('mbstring'),
  'session' => function_exists('session_start'),
];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (file_exists(LOCK_FILE)) json_err('已安装。如需重新安装，请先删除 install.lock 再试。');

  $db_host = trim($_POST['db_host'] ?? 'localhost');
  $db_port = trim($_POST['db_port'] ?? '3306');
  $db_name = trim($_POST['db_name'] ?? 'sanguosha');
  $db_user = trim($_POST['db_user'] ?? 'root');
  $db_pass = (string)($_POST['db_pass'] ?? '');
  $admin_user = trim($_POST['admin_user'] ?? 'admin');
  $admin_pass = (string)($_POST['admin_pass'] ?? 'admin123');

  foreach ($requirements as $k=>$ok) { if (!$ok) json_err('环境检查未通过：'.$k); }
  if ($db_name==='') json_err('数据库名不能为空');
  if ($admin_user==='') json_err('管理员用户名不能为空');
  if (strlen($admin_pass) < 6) json_err('管理员密码至少6位');

  try {
    // 连接到MySQL（无库）
    $dsn = "mysql:host={$db_host};port={$db_port};charset=utf8mb4";
    $pdo = new PDO($dsn, $db_user, $db_pass, [
      PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
      PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);

    // 创建数据库
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE `{$db_name}`");

    // 建表示例/兼容多版本代码：尽量包含项目中引用到的表和字段
    $sqls = [];

    // 管理员表（admin & admin_accounts）
    $sqls[] = "CREATE TABLE IF NOT EXISTS admin (
      id INT PRIMARY KEY AUTO_INCREMENT,
      username VARCHAR(50) NOT NULL UNIQUE,
      password VARCHAR(255) NOT NULL,
      email VARCHAR(100) NULL,
      full_name VARCHAR(100) NULL,
      role VARCHAR(20) DEFAULT 'admin',
      status ENUM('active','disabled') DEFAULT 'active',
      last_login DATETIME NULL,
      login_count INT DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $sqls[] = str_replace(' admin ', ' admin_accounts ', $sqls[count($sqls)-1]);

    // 管理员会话
    $sqls[] = "CREATE TABLE IF NOT EXISTS admin_sessions (
      id INT PRIMARY KEY AUTO_INCREMENT,
      admin_id INT NOT NULL,
      session_token VARCHAR(128) NOT NULL,
      ip_address VARCHAR(45) NULL,
      user_agent VARCHAR(255) NULL,
      status ENUM('active','logout') DEFAULT 'active',
      expires_at DATETIME NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      KEY idx_token (session_token),
      KEY idx_admin (admin_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // 客户账号
    $sqls[] = "CREATE TABLE IF NOT EXISTS customer_accounts (
      id INT PRIMARY KEY AUTO_INCREMENT,
      account_number VARCHAR(8) NOT NULL UNIQUE,
      account_type ENUM('normal','premium') NOT NULL DEFAULT 'normal',
      duration_hours INT NOT NULL,
      remaining_hours DECIMAL(10,2) DEFAULT NULL,
      status ENUM('active','expired','banned') NOT NULL DEFAULT 'active',
      ban_reason TEXT NULL,
      remark TEXT NULL,
      first_login_time DATETIME NULL,
      session_token VARCHAR(128) NULL,
      session_expires DATETIME NULL,
      last_login DATETIME NULL,
      login_count INT DEFAULT 0,
      expires_at DATETIME NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      KEY idx_account_number (account_number),
      KEY idx_status (status),
      KEY idx_type (account_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // 游戏账号
    $sqls[] = "CREATE TABLE IF NOT EXISTS game_accounts (
      id INT PRIMARY KEY AUTO_INCREMENT,
      account VARCHAR(100) NOT NULL,
      password VARCHAR(100) NOT NULL,
      account_name VARCHAR(100) NULL,
      level INT DEFAULT 0,
      vip_level INT DEFAULT 0,
      `rank` VARCHAR(50) NULL,
      nation_war VARCHAR(50) NULL,
      skin VARCHAR(500) NULL,
      gender ENUM('male','female') NULL,
      price DECIMAL(10,2) DEFAULT 0,
      premium_generals TEXT NULL,
      dynamic_skins TEXT NULL,
      general_count INT DEFAULT 0,
      skin_count INT DEFAULT 0,
      account_type ENUM('normal','premium') NOT NULL DEFAULT 'normal',
      status ENUM('available','in_use','banned','reported') NOT NULL DEFAULT 'available',
      current_user_id INT NULL,
      server_name VARCHAR(100) NULL,
      description TEXT NULL,
      skin_details TEXT NULL,
      taken_at DATETIME NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      KEY idx_status (status),
      KEY idx_type (account_type),
      KEY idx_current_user (current_user_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // 密码修改记录（新）
    $sqls[] = "CREATE TABLE IF NOT EXISTS password_change_records (
      id INT PRIMARY KEY AUTO_INCREMENT,
      customer_id INT NULL,
      customer_account_id INT NULL,
      game_account_id INT NOT NULL,
      old_password VARCHAR(100) NOT NULL,
      new_password VARCHAR(100) NULL,
      reason ENUM('expired','switched','manual','released') DEFAULT 'manual',
      status ENUM('pending','approved','rejected','completed','cancelled') DEFAULT 'pending',
      admin_note TEXT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP NULL,
      KEY idx_status (status),
      KEY idx_game_account (game_account_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // 密码修改请求（旧接口备用）
    $sqls[] = "CREATE TABLE IF NOT EXISTS password_change_requests (
      id INT PRIMARY KEY AUTO_INCREMENT,
      customer_id INT NULL,
      game_account_id INT NOT NULL,
      admin_id INT NULL,
      new_password VARCHAR(100) NULL,
      status ENUM('pending','completed','cancelled') DEFAULT 'pending',
      notes TEXT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      processed_at DATETIME NULL,
      KEY idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // 操作日志（同时提供 description 和 details 字段以兼容不同代码路径）
    $sqls[] = "CREATE TABLE IF NOT EXISTS operation_logs (
      id INT PRIMARY KEY AUTO_INCREMENT,
      user_type ENUM('admin','customer','system') NOT NULL,
      user_id INT NOT NULL,
      action VARCHAR(100) NOT NULL,
      description TEXT NULL,
      details TEXT NULL,
      ip_address VARCHAR(45) NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      KEY idx_user (user_type, user_id),
      KEY idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // 搜索关键词
    $sqls[] = "CREATE TABLE IF NOT EXISTS search_keywords (
      id INT PRIMARY KEY AUTO_INCREMENT,
      keyword VARCHAR(100) NOT NULL,
      search_count INT DEFAULT 1,
      last_searched TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      UNIQUE KEY unique_keyword (keyword)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // 系统配置
    $sqls[] = "CREATE TABLE IF NOT EXISTS system_config (
      id INT PRIMARY KEY AUTO_INCREMENT,
      config_key VARCHAR(100) NOT NULL UNIQUE,
      config_value TEXT NULL,
      description VARCHAR(255) NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // 客户会话/在线统计
    $sqls[] = "CREATE TABLE IF NOT EXISTS customer_sessions (
      id INT PRIMARY KEY AUTO_INCREMENT,
      customer_id INT NOT NULL,
      session_token VARCHAR(128) NOT NULL,
      status ENUM('active','expired','revoked') DEFAULT 'active',
      expires_at DATETIME NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      KEY idx_token (session_token),
      KEY idx_customer (customer_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // 定时任务
    $sqls[] = "CREATE TABLE IF NOT EXISTS scheduled_tasks (
      id INT PRIMARY KEY AUTO_INCREMENT,
      task_name VARCHAR(100) NOT NULL UNIQUE,
      is_active TINYINT(1) DEFAULT 1,
      run_interval INT NOT NULL DEFAULT 60, -- 秒
      last_run DATETIME NULL,
      next_run DATETIME NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // 账号使用统计（仪表板用）
    $sqls[] = "CREATE TABLE IF NOT EXISTS game_account_usage (
      id INT PRIMARY KEY AUTO_INCREMENT,
      game_account_id INT NOT NULL,
      customer_id INT NULL,
      start_time DATETIME NULL,
      end_time DATETIME NULL,
      duration_minutes INT DEFAULT 0,
      status ENUM('active','completed') DEFAULT 'completed',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      KEY idx_ga (game_account_id),
      KEY idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // 兼容 admin 旧页面：customers / games / pending_passwords
    $sqls[] = "CREATE TABLE IF NOT EXISTS customers (
      id INT PRIMARY KEY AUTO_INCREMENT,
      username VARCHAR(50) NOT NULL UNIQUE,
      remark VARCHAR(255) NULL,
      banned TINYINT(1) DEFAULT 0,
      ban_reason VARCHAR(255) NULL,
      expire_at DATETIME NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $sqls[] = "CREATE TABLE IF NOT EXISTS games (
      id INT PRIMARY KEY AUTO_INCREMENT,
      account_login VARCHAR(100) NOT NULL,
      password VARCHAR(100) NOT NULL,
      name VARCHAR(100) NULL,
      level INT DEFAULT 0,
      vip_level INT DEFAULT 0,
      rank_title VARCHAR(50) NULL,
      national_war VARCHAR(50) NULL,
      skins VARCHAR(500) NULL,
      gender VARCHAR(10) DEFAULT '未知',
      price DECIMAL(10,2) NULL,
      elite_generals TEXT NULL,
      dynamic_skins TEXT NULL,
      general_count INT DEFAULT 0,
      skin_count INT DEFAULT 0,
      status VARCHAR(20) DEFAULT 'available',
      assigned_customer_id INT NULL,
      assigned_at DATETIME NULL,
      updated_at DATETIME NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      KEY idx_status (status),
      KEY idx_assigned (assigned_customer_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $sqls[] = "CREATE TABLE IF NOT EXISTS pending_passwords (
      id INT PRIMARY KEY AUTO_INCREMENT,
      game_id INT NOT NULL,
      account_login VARCHAR(100) NOT NULL,
      old_password VARCHAR(100) NOT NULL,
      uploaded_by VARCHAR(50) NULL,
      uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // 老日志表（inc/db.php 兼容）
    $sqls[] = "CREATE TABLE IF NOT EXISTS logs (
      id INT PRIMARY KEY AUTO_INCREMENT,
      actor_type VARCHAR(20) NULL,
      actor_id INT NULL,
      action VARCHAR(100) NULL,
      details TEXT NULL,
      ip VARCHAR(45) NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // 执行所有建表
    foreach ($sqls as $sql) { $pdo->exec($sql); }

    // 视图（如果已存在则略过）
    try {
      $pdo->exec("CREATE VIEW dashboard_stats AS
        SELECT
          (SELECT COUNT(*) FROM customer_accounts) as total_customers,
          (SELECT COUNT(*) FROM customer_accounts WHERE status = 'active') as active_customers,
          (SELECT COUNT(*) FROM game_accounts) as total_game_accounts,
          (SELECT COUNT(*) FROM game_accounts WHERE status = 'available') as available_game_accounts,
          (SELECT COUNT(*) FROM game_accounts WHERE status = 'in_use') as used_game_accounts,
          (SELECT COUNT(*) FROM password_change_records WHERE status = 'pending') as pending_password_changes,
          (SELECT COUNT(*) FROM customer_accounts WHERE status = 'active' AND (remaining_hours IS NOT NULL AND remaining_hours <= 1)) as expiring_soon_customers
      ");
    } catch (Throwable $e) { /* MySQL 5.7 不支持 IF NOT EXISTS，忽略已有错误 */ }

    // 默认配置
    $pdo->exec("INSERT IGNORE INTO system_config (config_key, config_value, description) VALUES
      ('duration_options','0.5,1,2,3,4,5,6,12,24,48,72,168,360,720','时长选项（小时）'),
      ('warning_times','30,20,10','时长警告（分钟）'),
      ('site_title','三国杀取号系统','网站标题'),
      ('contact_info','如有问题请联系客服','联系信息')");

    // 默认定时任务
    $stmt = $pdo->prepare("INSERT IGNORE INTO scheduled_tasks (task_name, is_active, run_interval, next_run) VALUES ('update_remaining_time',1,60,NOW())");
    $stmt->execute();

    // 创建默认管理员（两张表都插入以兼容不同代码）
    $hash = password_hash($admin_pass, PASSWORD_DEFAULT);
    $insertAdmin = function($table) use ($pdo, $admin_user, $hash){
      $stmt = $pdo->prepare("SELECT id FROM {$table} WHERE username = ?");
      $stmt->execute([$admin_user]);
      if (!$stmt->fetch()) {
        $stmt2 = $pdo->prepare("INSERT INTO {$table} (username, password, role, status, login_count) VALUES (?,?,?,?,0)");
        $stmt2->execute([$admin_user, $hash, 'admin', 'active']);
      }
    };
    $insertAdmin('admin');
    $insertAdmin('admin_accounts');

    // 写入配置文件（更新 config.php 与 config/database.php）
    $cfg_errors = [];
    // 更新 config.php
    try {
      $cfg = file_get_contents(__DIR__.'/config.php');
      if ($cfg!==false) {
        $cfg = preg_replace("/define\('DB_HOST',\s*'.*?'\);/", "define('DB_HOST', '".addslashes($db_host)."');", $cfg);
        $cfg = preg_replace("/define\('DB_PORT',\s*'.*?'\);/", "define('DB_PORT', '".addslashes($db_port)."');", $cfg);
        $cfg = preg_replace("/define\('DB_NAME',\s*'.*?'\);/", "define('DB_NAME', '".addslashes($db_name)."');", $cfg);
        $cfg = preg_replace("/define\('DB_USER',\s*'.*?'\);/", "define('DB_USER', '".addslashes($db_user)."');", $cfg);
        $cfg = preg_replace("/define\('DB_PASS',\s*'.*?'\);/", "define('DB_PASS', '".addslashes($db_pass)."');", $cfg);
        file_put_contents(__DIR__.'/config.php', $cfg);
      }
    } catch (Throwable $e) { $cfg_errors[] = '更新 config.php 失败：'.$e->getMessage(); }

    // 更新 config/database.php
    try {
      $dbphp = file_get_contents(__DIR__.'/config/database.php');
      if ($dbphp!==false) {
        // 在替换内容中对 $ 进行转义，避免被当作变量
        $dbphp = preg_replace("/private \\$host = '.*?';/", "private \\$host = '".addslashes($db_host)."';", $dbphp);
        $dbphp = preg_replace("/private \\$db_name = '.*?';/", "private \\$db_name = '".addslashes($db_name)."';", $dbphp);
        $dbphp = preg_replace("/private \\$username = '.*?';/", "private \\$username = '".addslashes($db_user)."';", $dbphp);
        $dbphp = preg_replace("/private \\$password = '.*?';/", "private \\$password = '".addslashes($db_pass)."';", $dbphp);
        file_put_contents(__DIR__.'/config/database.php', $dbphp);
      }
    } catch (Throwable $e) { $cfg_errors[] = '更新 config/database.php 失败：'.$e->getMessage(); }

    // 写入锁文件
    @file_put_contents(LOCK_FILE, date('Y-m-d H:i:s'));

    json_ok([
      'message' => '安装完成',
      'db' => ['host'=>$db_host,'port'=>$db_port,'name'=>$db_name,'user'=>$db_user],
      'config_warnings' => $cfg_errors,
      'next_steps' => [
        '1. 为安全起见，删除 install.php 或创建 install.lock 防止重复安装',
        '2. 登录后台：使用管理员账号 '. $admin_user,
        '3. 在宝塔计划任务中每分钟访问一次 /cron/update_remaining_time.php?force=1 或配置CLI定时任务',
      ],
    ]);

  } catch (Throwable $e) {
    json_err('安装失败：'.$e->getMessage());
  }
} else {
  // 显示安装表单
  if (file_exists(LOCK_FILE)) {
    echo '<h2>已安装完成</h2><p>如需重新安装，请先删除 ./install.lock 文件。</p>';
    exit;
  }
  $okList = [];
  foreach ($requirements as $k=>$ok) { $okList[] = ($ok?'✅ ':'❌ ').h($k); }
  echo '<!doctype html><html lang="zh-CN"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1">'
    .'<title>一键安装向导</title>'
    .'<style>body{font-family:-apple-system,Segoe UI,Roboto,Helvetica,Arial;margin:24px} .g{max-width:720px;margin:auto} label{display:block;margin:8px 0 4px} input{width:100%;padding:8px;border:1px solid #ddd;border-radius:6px} .row{display:flex;gap:12px} .row>div{flex:1} .btn{margin-top:12px;padding:10px 16px;border:0;background:#2d74ff;color:#fff;border-radius:6px;cursor:pointer} .card{padding:12px;border:1px solid #eee;border-radius:8px;margin:12px 0;background:#fafafa}</style>'
    .'</head><body><div class="g">'
    .'<h2>一键安装向导</h2>'
    .'<div class="card"><strong>环境检查：</strong><br>'.implode('<br>',$okList).'</div>'
    .'<form method="post" onsubmit="return confirm(\'开始安装?\')">'
    .'<label>数据库主机</label><input name="db_host" value="localhost">'
    .'<div class="row"><div><label>端口</label><input name="db_port" value="3306"></div><div><label>数据库名</label><input name="db_name" value="sanguosha"></div></div>'
    .'<div class="row"><div><label>用户名</label><input name="db_user" value="root"></div><div><label>密码</label><input type="password" name="db_pass" value=""></div></div>'
    .'<div class="card"><strong>默认管理员</strong><div class="row"><div><label>用户名</label><input name="admin_user" value="admin"></div><div><label>密码</label><input type="password" name="admin_pass" value="admin123"></div></div></div>'
    .'<button class="btn" type="submit">开始安装</button>'
    .'</form></div></body></html>';
}

