<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三国杀账号管理系统</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>⚔️</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        
        .hero-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            opacity: 0.3;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 48px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 1000px;
            width: 100%;
            margin: 20px;
            position: relative;
            z-index: 1;
        }
        
        .logo-section {
            text-align: center;
            margin-bottom: 48px;
        }
        
        .logo-icon {
            font-size: 5rem;
            margin-bottom: 20px;
            display: block;
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .logo-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .logo-subtitle {
            color: #718096;
            font-size: 1.1rem;
            margin-bottom: 8px;
        }
        
        .version-badge {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .entrance-cards {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 32px;
            margin-top: 48px;
        }
        
        .entrance-card {
            background: white;
            border-radius: 20px;
            padding: 40px 32px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
            text-decoration: none;
            color: inherit;
        }
        
        .entrance-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            color: inherit;
        }
        
        .entrance-card.user-card:hover {
            border-color: #667eea;
        }
        
        .entrance-card.admin-card:hover {
            border-color: #764ba2;
        }
        
        .card-icon {
            font-size: 4rem;
            margin-bottom: 24px;
            display: block;
        }
        
        .user-card .card-icon {
            color: #667eea;
        }
        
        .admin-card .card-icon {
            color: #764ba2;
        }
        
        .card-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 16px;
            color: #2d3748;
        }
        
        .card-description {
            color: #718096;
            margin-bottom: 24px;
            line-height: 1.6;
        }
        
        .card-features {
            list-style: none;
            padding: 0;
            margin: 0 0 24px 0;
        }
        
        .card-features li {
            padding: 8px 0;
            color: #4a5568;
            font-size: 0.9rem;
        }
        
        .card-features li i {
            margin-right: 8px;
            width: 16px;
        }
        
        .card-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 32px;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .user-card .card-button {
            background: linear-gradient(135deg, #667eea, #5a67d8);
        }
        
        .admin-card .card-button {
            background: linear-gradient(135deg, #764ba2, #9f7aea);
        }
        
        .card-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .system-info {
            margin-top: 48px;
            padding-top: 32px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 24px;
            margin-top: 24px;
        }
        
        .info-item {
            background: #f7fafc;
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }
        
        .info-label {
            font-size: 0.8rem;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }
        
        .info-value {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
        }
        
        .footer-links {
            margin-top: 32px;
            text-align: center;
            padding-top: 24px;
            border-top: 1px solid #e2e8f0;
        }
        
        .footer-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 16px;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }
        
        .footer-links a:hover {
            color: #764ba2;
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .main-container {
                padding: 32px 24px;
                margin: 10px;
            }
            
            .entrance-cards {
                grid-template-columns: 1fr;
                gap: 24px;
            }
            
            .logo-title {
                font-size: 2rem;
            }
            
            .card-icon {
                font-size: 3rem;
            }
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #28a745;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="hero-bg"></div>
        
        <div class="main-container">
            <div class="logo-section">
                <span class="logo-icon">⚔️</span>
                <h1 class="logo-title">三国杀账号管理系统</h1>
                <p class="logo-subtitle">专业的游戏账号管理解决方案</p>
                <span class="version-badge">v2.0 完整版</span>
            </div>
            
            <div class="entrance-cards">
                <!-- 用户入口 -->
                <a href="/customer-login.html" class="entrance-card user-card">
                    <span class="card-icon">🎮</span>
                    <h3 class="card-title">客户入口</h3>
                    <p class="card-description">登录您的客户账号，查询和使用游戏账号</p>
                    
                    <ul class="card-features">
                        <li><i class="bi bi-check-circle text-success"></i>安全登录验证</li>
                        <li><i class="bi bi-check-circle text-success"></i>智能账号搜索</li>
                        <li><i class="bi bi-check-circle text-success"></i>一键取号使用</li>
                        <li><i class="bi bi-check-circle text-success"></i>账号详情查看</li>
                        <li><i class="bi bi-check-circle text-success"></i>便捷账号切换</li>
                    </ul>
                    
                    <span class="card-button">
                        <i class="bi bi-box-arrow-in-right me-2"></i>客户登录
                    </span>
                </a>
                
                <!-- 管理员入口 -->
                <a href="/admin-login.html" class="entrance-card admin-card">
                    <span class="card-icon">👨‍💼</span>
                    <h3 class="card-title">管理后台</h3>
                    <p class="card-description">管理员登录，进行系统管理和数据监控</p>
                    
                    <ul class="card-features">
                        <li><i class="bi bi-check-circle text-success"></i>实时数据统计</li>
                        <li><i class="bi bi-check-circle text-success"></i>客户账号管理</li>
                        <li><i class="bi bi-check-circle text-success"></i>游戏账号管理</li>
                        <li><i class="bi bi-check-circle text-success"></i>操作日志监控</li>
                        <li><i class="bi bi-check-circle text-success"></i>密码修改处理</li>
                    </ul>
                    
                    <span class="card-button">
                        <i class="bi bi-shield-lock me-2"></i>管理员登录
                    </span>
                </a>
            </div>
            
            <div class="system-info">
                <h5 class="mb-3">
                    <span class="status-indicator"></span>
                    系统状态
                </h5>
                
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">系统版本</div>
                        <div class="info-value">v2.0 完整版</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">服务状态</div>
                        <div class="info-value">正常运行</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">最后更新</div>
                        <div class="info-value" id="lastUpdate">2025-08-13</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">在线状态</div>
                        <div class="info-value">
                            <span class="text-success">
                                <i class="bi bi-circle-fill me-1"></i>在线
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="footer-links">
                <a href="/test-fix-verification.html">
                    <i class="bi bi-check-circle me-1"></i>系统测试
                </a>
                <a href="/upgrade-database-fixed.php">
                    <i class="bi bi-database me-1"></i>数据库升级
                </a>
                <a href="/upgrade-password-table.php">
                    <i class="bi bi-plus-circle me-1"></i>功能升级
                </a>
                <a href="#" onclick="showHelp()">
                    <i class="bi bi-question-circle me-1"></i>使用帮助
                </a>
            </div>
        </div>
    </div>

    <!-- 帮助模态框 -->
    <div class="modal fade" id="helpModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-question-circle me-2"></i>系统使用帮助
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="bi bi-person me-2"></i>客户使用指南</h6>
                            <ol class="list-group list-group-numbered">
                                <li class="list-group-item">使用客户账号登录系统</li>
                                <li class="list-group-item">浏览可用的游戏账号</li>
                                <li class="list-group-item">选择合适的账号进行取号</li>
                                <li class="list-group-item">查看账号详情和复制密码</li>
                                <li class="list-group-item">需要时可以切换其他账号</li>
                            </ol>
                            
                            <h6 class="mt-4"><i class="bi bi-star me-2"></i>账号类型说明</h6>
                            <div class="alert alert-info">
                                <strong>高级账号：</strong>可使用所有游戏账号<br>
                                <strong>普通账号：</strong>仅可使用普通游戏账号
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="bi bi-shield me-2"></i>管理员功能</h6>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <i class="bi bi-graph-up me-2"></i>实时数据统计和监控
                                </li>
                                <li class="list-group-item">
                                    <i class="bi bi-people me-2"></i>客户账号管理和封禁
                                </li>
                                <li class="list-group-item">
                                    <i class="bi bi-controller me-2"></i>游戏账号管理
                                </li>
                                <li class="list-group-item">
                                    <i class="bi bi-journal-text me-2"></i>操作日志查看
                                </li>
                                <li class="list-group-item">
                                    <i class="bi bi-key me-2"></i>密码修改请求处理
                                </li>
                            </ul>
                            
                            <h6 class="mt-4"><i class="bi bi-info-circle me-2"></i>系统特性</h6>
                            <div class="alert alert-success">
                                <small>
                                    • 实时数据更新<br>
                                    • 安全权限控制<br>
                                    • 完整操作日志<br>
                                    • 响应式设计<br>
                                    • 智能搜索功能
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateLastUpdateTime();
            checkSystemStatus();
        });

        /**
         * 更新最后更新时间
         */
        function updateLastUpdateTime() {
            const now = new Date();
            const dateStr = now.toLocaleDateString('zh-CN');
            document.getElementById('lastUpdate').textContent = dateStr;
        }

        /**
         * 检查系统状态
         */
        async function checkSystemStatus() {
            try {
                // 简单的健康检查
                const response = await fetch('/api/customer_new.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'health_check'
                    })
                });
                
                // 不管返回什么，只要能连通就认为系统正常
                console.log('系统状态检查完成');
            } catch (error) {
                console.log('系统状态检查失败，但不影响使用');
            }
        }

        /**
         * 显示帮助
         */
        function showHelp() {
            new bootstrap.Modal(document.getElementById('helpModal')).show();
        }

        /**
         * 添加一些交互效果
         */
        document.querySelectorAll('.entrance-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
