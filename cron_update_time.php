<?php
/**
 * 定时更新客户剩余时长脚本
 * 适用于宝塔环境
 */

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 防止超时
set_time_limit(60);

try {
    // 数据库连接
    $host = 'localhost';
    $dbname = 'sanguosha';
    $username = 'sanguosha';
    $password = '983315523';

    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $startTime = microtime(true);
    $logPrefix = "[" . date('Y-m-d H:i:s') . "] ";
    
    // 检查是否需要执行（如果有定时任务表的话）
    $shouldRun = true;
    try {
        $stmt = $pdo->prepare("
            SELECT * FROM scheduled_tasks 
            WHERE task_name = 'update_remaining_time' AND is_active = 1
        ");
        $stmt->execute();
        $task = $stmt->fetch();
        
        if ($task) {
            $lastRun = strtotime($task['last_run']);
            $interval = $task['run_interval'];
            $shouldRun = (time() - $lastRun) >= $interval;
        }
    } catch (Exception $e) {
        // 如果没有定时任务表，直接执行
        $shouldRun = true;
    }
    
    if (!$shouldRun) {
        echo $logPrefix . "任务未到执行时间\n";
        exit(0);
    }
    
    echo $logPrefix . "开始更新客户剩余时长...\n";
    
    // 1. 更新剩余时长
    $stmt = $pdo->prepare("
        UPDATE customer_accounts 
        SET remaining_hours = CASE 
            WHEN first_login_time IS NULL THEN duration_hours
            WHEN expires_at IS NOT NULL THEN GREATEST(0, TIMESTAMPDIFF(SECOND, NOW(), expires_at) / 3600)
            ELSE GREATEST(0, duration_hours - TIMESTAMPDIFF(SECOND, first_login_time, NOW()) / 3600)
        END,
        status = CASE 
            WHEN first_login_time IS NOT NULL AND (
                (expires_at IS NOT NULL AND expires_at <= NOW()) OR
                (expires_at IS NULL AND TIMESTAMPDIFF(SECOND, first_login_time, NOW()) / 3600 >= duration_hours)
            ) THEN 'expired'
            WHEN status = 'expired' AND first_login_time IS NOT NULL AND (
                (expires_at IS NOT NULL AND expires_at > NOW()) OR
                (expires_at IS NULL AND TIMESTAMPDIFF(SECOND, first_login_time, NOW()) / 3600 < duration_hours)
            ) THEN 'active'
            ELSE status
        END,
        updated_at = NOW()
        WHERE status != 'banned'
    ");
    $stmt->execute();
    $updatedRows = $stmt->rowCount();
    
    // 2. 处理过期客户的游戏账号
    $stmt = $pdo->prepare("
        UPDATE game_accounts ga
        JOIN customer_accounts ca ON ga.current_user_id = ca.id
        SET ga.current_user_id = NULL, ga.status = 'available', ga.taken_at = NULL
        WHERE ca.status = 'expired' AND ga.status = 'in_use'
    ");
    $stmt->execute();
    $releasedAccounts = $stmt->rowCount();
    
    // 3. 记录过期客户释放账号的操作日志
    if ($releasedAccounts > 0) {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO operation_logs (user_type, user_id, action, description, ip_address, created_at)
                VALUES ('system', 0, 'auto_release_expired', ?, 'system', NOW())
            ");
            $stmt->execute(["系统自动释放过期客户的游戏账号，共释放 {$releasedAccounts} 个账号"]);
        } catch (Exception $e) {
            // 忽略日志记录错误
        }
    }
    
    // 4. 更新任务执行时间
    try {
        $nextRun = date('Y-m-d H:i:s', time() + 60); // 下次1分钟后执行
        $stmt = $pdo->prepare("
            UPDATE scheduled_tasks 
            SET last_run = NOW(), next_run = ? 
            WHERE task_name = 'update_remaining_time'
        ");
        $stmt->execute([$nextRun]);
    } catch (Exception $e) {
        // 如果没有定时任务表，忽略错误
    }
    
    // 5. 清理过期的操作日志（保留最近30天）
    try {
        $stmt = $pdo->prepare("
            DELETE FROM operation_logs 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
        ");
        $stmt->execute();
        $deletedLogs = $stmt->rowCount();
        
        if ($deletedLogs > 0) {
            echo $logPrefix . "清理了 {$deletedLogs} 条过期日志\n";
        }
    } catch (Exception $e) {
        // 忽略清理错误
    }
    
    $endTime = microtime(true);
    $executionTime = round(($endTime - $startTime) * 1000, 2);
    
    echo $logPrefix . "更新完成: 更新了 {$updatedRows} 个客户账号, 释放了 {$releasedAccounts} 个游戏账号, 耗时 {$executionTime}ms\n";
    
    // 如果是通过浏览器访问，返回HTML格式
    if (php_sapi_name() !== 'cli' && !isset($_GET['format']) || $_GET['format'] !== 'text') {
        echo "<br><strong>统计信息:</strong><br>";
        
        // 显示当前客户状态统计
        try {
            $stmt = $pdo->query("
                SELECT 
                    status,
                    COUNT(*) as count,
                    AVG(remaining_hours) as avg_remaining
                FROM customer_accounts 
                GROUP BY status
            ");
            $stats = $stmt->fetchAll();
            
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>状态</th><th>数量</th><th>平均剩余时长</th></tr>";
            foreach ($stats as $stat) {
                $avgRemaining = $stat['avg_remaining'] ? number_format($stat['avg_remaining'], 2) . 'h' : '-';
                echo "<tr>";
                echo "<td>{$stat['status']}</td>";
                echo "<td>{$stat['count']}</td>";
                echo "<td>{$avgRemaining}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } catch (Exception $e) {
            echo "<br>统计信息获取失败: " . $e->getMessage();
        }
    }
    
} catch (Exception $e) {
    $errorMsg = $logPrefix . "更新失败: " . $e->getMessage() . "\n";
    echo $errorMsg;
    error_log($errorMsg);
    
    // 如果是通过浏览器访问，显示错误信息
    if (php_sapi_name() !== 'cli') {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ 执行失败:</h3>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    exit(1);
}
?>
