-- 取号网站系统数据库安装脚本
-- 数据库：sanguosha

USE sanguosha;

-- 1. 管理员表
CREATE TABLE IF NOT EXISTS admin (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. 客户账号表
CREATE TABLE IF NOT EXISTS customer_accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_number VARCHAR(8) NOT NULL UNIQUE COMMENT '8位数字客户账号',
    account_type ENUM('normal', 'premium') NOT NULL DEFAULT 'normal' COMMENT '账号类型：普通/高级',
    duration_hours INT NOT NULL COMMENT '时长（小时）',
    remaining_hours DECIMAL(10,2) DEFAULT NULL COMMENT '剩余时长（小时）',
    status ENUM('active', 'expired', 'banned') NOT NULL DEFAULT 'active' COMMENT '状态',
    ban_reason TEXT COMMENT '封禁理由',
    first_login_time TIMESTAMP NULL COMMENT '首次登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_account_number (account_number),
    INDEX idx_status (status),
    INDEX idx_account_type (account_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. 游戏账号表
CREATE TABLE IF NOT EXISTS game_accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account VARCHAR(100) NOT NULL COMMENT '游戏账号',
    password VARCHAR(100) NOT NULL COMMENT '游戏密码',
    account_name VARCHAR(100) COMMENT '账号名称',
    level INT DEFAULT 0 COMMENT '等级',
    vip_level INT DEFAULT 0 COMMENT 'VIP等级',
    rank VARCHAR(50) COMMENT '军衔',
    nation_war VARCHAR(50) COMMENT '国战',
    skin VARCHAR(500) COMMENT '皮肤',
    gender ENUM('male', 'female') COMMENT '性别',
    price DECIMAL(10,2) DEFAULT 0 COMMENT '出售价格',
    premium_generals TEXT COMMENT '精品武将',
    dynamic_skins TEXT COMMENT '动态皮肤',
    general_count INT DEFAULT 0 COMMENT '武将数量',
    skin_count INT DEFAULT 0 COMMENT '皮肤数量',
    account_type ENUM('normal', 'premium') NOT NULL DEFAULT 'normal' COMMENT '账号区域类型',
    status ENUM('available', 'in_use', 'banned', 'reported') NOT NULL DEFAULT 'available' COMMENT '状态',
    current_user_id INT NULL COMMENT '当前使用的客户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_account_type (account_type),
    INDEX idx_current_user (current_user_id),
    FOREIGN KEY (current_user_id) REFERENCES customer_accounts(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. 密码修改记录表
CREATE TABLE IF NOT EXISTS password_change_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    game_account_id INT NOT NULL,
    old_password VARCHAR(100) NOT NULL,
    customer_account_id INT NOT NULL COMMENT '提交修改的客户账号',
    reason ENUM('expired', 'switched') NOT NULL COMMENT '修改原因：到期/切换账号',
    status ENUM('pending', 'completed') NOT NULL DEFAULT 'pending',
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    INDEX idx_status (status),
    INDEX idx_game_account (game_account_id),
    FOREIGN KEY (game_account_id) REFERENCES game_accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_account_id) REFERENCES customer_accounts(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_type ENUM('admin', 'customer') NOT NULL,
    user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL COMMENT '操作类型',
    description TEXT COMMENT '操作描述',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user (user_type, user_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. 搜索关键词统计表
CREATE TABLE IF NOT EXISTS search_keywords (
    id INT PRIMARY KEY AUTO_INCREMENT,
    keyword VARCHAR(100) NOT NULL,
    search_count INT DEFAULT 1,
    last_searched TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_keyword (keyword),
    INDEX idx_search_count (search_count DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 7. 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入初始数据
-- 默认管理员账号 (密码: admin123)
INSERT INTO admin (username, password) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');

-- 系统配置
INSERT INTO system_config (config_key, config_value, description) VALUES
('duration_options', '0.5,1,2,3,4,5,6,12,24,48,72,168,360,720', '时长选项（小时）'),
('warning_times', '30,20,10', '时长警告时间（分钟）'),
('site_title', '三国杀取号系统', '网站标题'),
('contact_info', '如有问题请联系客服', '联系信息');

-- 注意：触发器需要在MySQL命令行中单独创建，因为PDO不支持DELIMITER语法
-- 安装完成后，请在MySQL命令行中执行以下触发器创建语句：

/*
DELIMITER $$

CREATE TRIGGER customer_login_log
AFTER UPDATE ON customer_accounts
FOR EACH ROW
BEGIN
    IF OLD.first_login_time IS NULL AND NEW.first_login_time IS NOT NULL THEN
        INSERT INTO operation_logs (user_type, user_id, action, description)
        VALUES ('customer', NEW.id, 'first_login', CONCAT('客户账号 ', NEW.account_number, ' 首次登录'));
    END IF;
END$$

CREATE TRIGGER game_account_usage_log
AFTER UPDATE ON game_accounts
FOR EACH ROW
BEGIN
    IF OLD.current_user_id IS NULL AND NEW.current_user_id IS NOT NULL THEN
        INSERT INTO operation_logs (user_type, user_id, action, description)
        VALUES ('customer', NEW.current_user_id, 'take_account', CONCAT('取用游戏账号: ', NEW.account));
    ELSEIF OLD.current_user_id IS NOT NULL AND NEW.current_user_id IS NULL THEN
        INSERT INTO operation_logs (user_type, user_id, action, description)
        VALUES ('customer', OLD.current_user_id, 'release_account', CONCAT('释放游戏账号: ', NEW.account));
    END IF;
END$$

DELIMITER ;
*/

-- 创建视图：数据统计
CREATE VIEW IF NOT EXISTS dashboard_stats AS
SELECT
    (SELECT COUNT(*) FROM customer_accounts) as total_customers,
    (SELECT COUNT(*) FROM customer_accounts WHERE status = 'active') as active_customers,
    (SELECT COUNT(*) FROM game_accounts) as total_game_accounts,
    (SELECT COUNT(*) FROM game_accounts WHERE status = 'available') as available_game_accounts,
    (SELECT COUNT(*) FROM game_accounts WHERE status = 'in_use') as used_game_accounts,
    (SELECT COUNT(*) FROM password_change_records WHERE status = 'pending') as pending_password_changes,
    (SELECT COUNT(*) FROM customer_accounts WHERE status = 'active' AND remaining_hours <= 1) as expiring_soon_customers;
