<?php
// 管理后台统计API
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

require_once '../config/database.php';

// 开始会话
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 验证管理员权限 - 对于敏感操作需要验证会话
// 某些操作（如修改密码）需要管理员登录
function requireAdminSession() {
    if (!isset($_SESSION['admin_id']) || $_SESSION['admin_id'] <= 0) {
        jsonResponse(false, '需要管理员权限，请先登录', null, 401);
        exit;
    }
}

try {
    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        jsonResponse(false, '数据库连接失败', null, 500);
        exit;
    }
} catch (Exception $e) {
    error_log("Database connection error: " . $e->getMessage());
    jsonResponse(false, '数据库连接错误', null, 500);
    exit;
}

// 处理请求
$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'POST') {

// 返回可用的管理员表名：优先 'admin'，否则 'admin_accounts'，都不存在则返回空
function getAdminTableName($db) {
    try {
        $stmt = $db->query("SHOW TABLES LIKE 'admin'");
        if ($stmt && $stmt->rowCount() > 0) return 'admin';
    } catch (Exception $e) {}
    try {
        $stmt = $db->query("SHOW TABLES LIKE 'admin_accounts'");
        if ($stmt && $stmt->rowCount() > 0) return 'admin_accounts';
    } catch (Exception $e) {}
    return '';
}

    try {
        $rawInput = file_get_contents('php://input');
        if (empty($rawInput)) {
            jsonResponse(false, '请求体为空');
            exit;
        }

        $input = json_decode($rawInput, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            jsonResponse(false, 'JSON解析错误：' . json_last_error_msg());
            exit;
        }

        if (!$input) {
            jsonResponse(false, '无效的请求数据');
            exit;
        }

        $action = $input['action'] ?? '';

        switch ($action) {
            case 'get_dashboard_stats':
                getDashboardStats($db, $input);
                break;
            case 'get_operation_logs':
            case 'get_logs':
                getOperationLogs($db, $input);
                break;
            case 'get_operation_logs_raw':
                getOperationLogsRaw($db, $input);
                break;
            case 'get_real_time_logs':
            case 'get_realtime_logs':
                getRealTimeLogs($db, $input);
                break;
            case 'get_real_time_logs_raw':
                getRealTimeLogsRaw($db, $input);
                break;
            case 'get_customer_stats':
                getCustomerStats($db, $input);
                break;
            case 'get_customers':
                getCustomers($db, $input);
                break;
            case 'get_game_accounts':
                getGameAccounts($db, $input);
                break;
            case 'get_password_records':
                getPasswordRecords($db, $input);
                break;
            case 'generate_customers':
                generateCustomers($db, $input);
                break;
            case 'add_game_account':
                addGameAccount($db, $input);
                break;
            case 'delete_customer':
                deleteCustomer($db, $input);
                break;
            case 'delete_game_account':
                deleteGameAccount($db, $input);
                break;
            case 'approve_password_change':
                approvePasswordChange($db, $input);
                break;
            case 'reject_password_change':
                rejectPasswordChange($db, $input);
                break;
            case 'delete_password_record':
                deletePasswordRecord($db, $input);
                break;
            case 'change_admin_password':
                requireAdminSession(); // 验证管理员会话
                changeAdminPassword($db, $input);
                break;
            case 'check_session':
                checkSessionStatus($db, $input);
                break;
            case 'check_database':
                checkDatabaseStatus($db, $input);
                break;
            case 'get_hot_search_words':
                getHotSearchWords($db, $input);
                break;
            case 'update_remaining_time':
                updateRemainingTime($db, $input);
                break;
            case 'record_search':
                recordSearchKeyword($db, $input);
                break;
            case 'clear_hot_search_words':
                clearHotSearchWords($db, $input);
                break;
            case 'get_customer_detail':
                getCustomerDetail($db, $input);
                break;
            case 'update_customer':
                updateCustomer($db, $input);
                break;
            case 'toggle_customer_status':
                toggleCustomerStatus($db, $input);
                break;
            case 'get_game_account_detail':
                getGameAccountDetail($db, $input);
                break;
            case 'update_game_account':
                updateGameAccount($db, $input);
                break;
            case 'auto_submit_password_change':
                autoSubmitPasswordChange($db, $input);
                break;
            case 'manual_update_password':
                manualUpdatePassword($db, $input);
                break;
            case 'get_account_usage_stats':
                getAccountUsageStats($db, $input);
                break;
            case 'get_daily_stats':
                getDailyStats($db, $input);
                break;
            case 'update_password_table_structure':
                updatePasswordTableStructure($db, $input);
                break;
            case 'check_password_table_structure':
                checkPasswordTableStructure($db, $input);
                break;
            default:
                jsonResponse(false, '未知的操作：' . $action);
                break;
        }
    } catch (Exception $e) {
        error_log("Request processing error: " . $e->getMessage());
        jsonResponse(false, '请求处理错误', null, 500);
    }
} else {
    jsonResponse(false, '不支持的请求方法：' . $method);
}

/**
 * 获取仪表板统计数据
 */
function getDashboardStats($db, $input) {
    try {
        $stats = [];

        // 客户统计
        $stmt = $db->query("SELECT
            COUNT(*) as total_customers,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_customers,
            SUM(CASE WHEN status = 'banned' THEN 1 ELSE 0 END) as banned_customers,
            SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired_customers,
            SUM(CASE WHEN first_login_time IS NOT NULL THEN 1 ELSE 0 END) as activated_customers
            FROM customer_accounts
        ");
        $customerStats = $stmt->fetch(PDO::FETCH_ASSOC);

        // 游戏账号统计
        $stmt = $db->query("SELECT
            COUNT(*) as total_accounts,
            SUM(CASE WHEN status = 'available' THEN 1 ELSE 0 END) as available_accounts,
            SUM(CASE WHEN status = 'in_use' THEN 1 ELSE 0 END) as in_use_accounts,
            SUM(CASE WHEN account_type = 'premium' THEN 1 ELSE 0 END) as premium_accounts,
            SUM(CASE WHEN account_type = 'normal' THEN 1 ELSE 0 END) as normal_accounts
            FROM game_accounts
        ");
        $accountStats = $stmt->fetch(PDO::FETCH_ASSOC);

        // 今日活动统计 - 使用安全查询
        try {
            $stmt = $db->query("SELECT
                COUNT(DISTINCT CASE WHEN action = 'login' AND DATE(created_at) = CURDATE() THEN user_id END) as today_logins,
                COUNT(CASE WHEN action = 'take_account' AND DATE(created_at) = CURDATE() THEN 1 END) as today_takes,
                COUNT(CASE WHEN action = 'release_account' AND DATE(created_at) = CURDATE() THEN 1 END) as today_releases
                FROM operation_logs WHERE user_type = 'customer'
            ");
            $todayStats = $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $todayStats = ['today_logins' => 0, 'today_takes' => 0, 'today_releases' => 0];
        }

        // 在线用户统计 - 使用安全查询
        try {
            $stmt = $db->query("SELECT COUNT(*) as online_users FROM customer_sessions WHERE status = 'active' AND expires_at > NOW()");
            $onlineStats = $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $onlineStats = ['online_users' => 0];
        }

        // 密码修改请求统计 - 使用安全查询
        try {
            $stmt = $db->query("SELECT COUNT(*) as pending_password_changes FROM password_change_records WHERE status = 'pending'");
            $passwordStats = $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $passwordStats = ['pending_password_changes' => 0];
        }

        // 使用时长统计 - 使用安全查询
        try {
            $stmt = $db->query("SELECT
                AVG(duration_minutes) as avg_usage_minutes,
                MAX(duration_minutes) as max_usage_minutes,
                COUNT(*) as total_usage_sessions
                FROM game_account_usage WHERE status = 'completed' AND DATE(created_at) = CURDATE()
            ");
            $usageStats = $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $usageStats = ['avg_usage_minutes' => 0, 'max_usage_minutes' => 0, 'total_usage_sessions' => 0];
        }

        $stats = [
            'total_customers' => $customerStats['total_customers'],
            'active_customers' => $customerStats['activated_customers'], // 使用已激活客户数
            'banned_customers' => $customerStats['banned_customers'],
            'expired_customers' => $customerStats['expired_customers'],
            'total_game_accounts' => $accountStats['total_accounts'],
            'available_game_accounts' => $accountStats['available_accounts'],
            'in_use_game_accounts' => $accountStats['in_use_accounts'],
            'premium_accounts' => $accountStats['premium_accounts'],
            'normal_accounts' => $accountStats['normal_accounts'],
            'today_logins' => $todayStats['today_logins'],
            'today_takes' => $todayStats['today_takes'],
            'today_releases' => $todayStats['today_releases'],
            'online_users' => $onlineStats['online_users'],
            'pending_password_changes' => $passwordStats['pending_password_changes'],
            'customers' => $customerStats,
            'accounts' => $accountStats,
            'today' => $todayStats,
            'online' => $onlineStats,
            'usage' => $usageStats,
            'last_updated' => date('Y-m-d H:i:s')
        ];

        jsonResponse(true, '获取成功', $stats);

    } catch (Exception $e) {
        error_log("Get dashboard stats error: " . $e->getMessage());
        jsonResponse(false, '获取统计数据失败', null, 500);
    }
}

/**
 * 获取操作日志
 */
function getOperationLogs($db, $input) {
    try {
        $page = max(1, intval($input['page'] ?? 1));
        $limit = min(100, max(10, intval($input['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;

        $userType = $input['user_type'] ?? '';
        // 注意：顶层 action 用于路由，这里不要用同名键作为过滤条件
        $filterAction = $input['log_action'] ?? ($input['filter_action'] ?? '');
        $dateFrom = $input['date_from'] ?? '';
        $dateTo = $input['date_to'] ?? '';

        // 构建查询条件
        $conditions = [];
        $params = [];

        if (!empty($userType)) {
            $conditions[] = "user_type = ?";
            $params[] = $userType;
        }

        if (!empty($filterAction)) {
            $conditions[] = "action = ?";
            $params[] = $filterAction;
        }

        if (!empty($dateFrom)) {
            $conditions[] = "DATE(created_at) >= ?";
            $params[] = $dateFrom;
        }

        if (!empty($dateTo)) {
            $conditions[] = "DATE(created_at) <= ?";
            $params[] = $dateTo;
        }

        // 组合过滤条件
        $whereClause = '';
        if (!empty($conditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $conditions);
        }

        // 检查表是否存在
        try {
            $stmt = $db->query("SHOW TABLES LIKE 'operation_logs'");
            if ($stmt->rowCount() == 0) {
                // 表不存在，返回空数据
                jsonResponse(true, '获取成功', [
                    'logs' => [],
                    'pagination' => [
                        'page' => $page,
                        'limit' => $limit,
                        'total' => 0,
                        'pages' => 0
                    ]
                ]);
                return;
            }
        } catch (Exception $e) {
            // 如果检查表失败，继续尝试查询
        }

        // 获取总数
        try {
            // 若未指定任何过滤，则默认限制最近7天，避免全表过大
            if (empty($conditions)) {
                $whereClause = "WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                $params = [];
            }
            $countSql = "SELECT COUNT(*) as total FROM operation_logs $whereClause";
            $stmt = $db->prepare($countSql);
            $stmt->execute($params);
            $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        } catch (Exception $e) {
            $total = 0;
        }

        // 获取日志数据
        try {
            $adminTable = getAdminTableName($db);
            $adminJoin = $adminTable ? "LEFT JOIN $adminTable aa ON ol.user_type = 'admin' AND ol.user_id = aa.id" : "";

            // 调整WHERE子句以匹配JOIN查询的表别名
            $listWhereClause = $whereClause;
            if (!empty($whereClause) && strpos($whereClause, 'ol.') === false) {
                $listWhereClause = str_replace('WHERE created_at', 'WHERE ol.created_at', $whereClause);
            }

            $sql = "SELECT ol.*,
                    CASE
                        WHEN ol.user_type = 'customer' THEN ca.account_number
                        WHEN ol.user_type = 'admin' THEN aa.username
                        ELSE CONCAT('系统操作-', ol.user_id)
                    END as user_name
                    FROM operation_logs ol
                    LEFT JOIN customer_accounts ca ON ol.user_type = 'customer' AND ol.user_id = ca.id
                    $adminJoin
                    $listWhereClause
                    ORDER BY ol.created_at DESC
                    LIMIT $limit OFFSET $offset";


            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $logs = [];
        }

        jsonResponse(true, '获取成功', [
            'logs' => $logs,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);

    } catch (Exception $e) {

/**
 * 原始操作日志（不做任何 JOIN/复杂过滤）
 */
function getOperationLogsRaw($db, $input) {
    try {
        $page = max(1, intval($input['page'] ?? 1));
        $limit = min(100, max(10, intval($input['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;

        // 只按 operation_logs 直接返回
        $countStmt = $db->query("SELECT COUNT(*) AS total FROM operation_logs");
        $total = (int)($countStmt ? $countStmt->fetch(PDO::FETCH_ASSOC)['total'] : 0);

        $stmt = $db->prepare("SELECT * FROM operation_logs ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
        $stmt->execute();
        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

        jsonResponse(true, '获取成功', [
            'logs' => $logs,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ($limit ? ceil($total / $limit) : 0)
            ]
        ]);
    } catch (Exception $e) {
        error_log('getOperationLogsRaw error: ' . $e->getMessage());
        jsonResponse(true, '获取成功', [ 'logs' => [], 'pagination' => ['page'=>1,'limit'=>20,'total'=>0,'pages'=>0] ]);
    }
}

/**
 * 原始实时日志（不做任何 JOIN）
 */
function getRealTimeLogsRaw($db, $input) {
    try {
        $lastId = intval($input['last_id'] ?? 0);
        $limit = min(50, max(5, intval($input['limit'] ?? 10)));
        $stmt = $db->prepare("SELECT * FROM operation_logs WHERE id > ? ORDER BY created_at DESC LIMIT ?");
        $stmt->execute([$lastId, $limit]);
        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $newLastId = $lastId;
        if (!empty($logs)) { $newLastId = max(array_column($logs, 'id')); }
        jsonResponse(true, '获取成功', [ 'logs'=>$logs, 'last_id'=>$newLastId, 'has_new'=>!empty($logs) ]);
    } catch (Exception $e) {
        error_log('getRealTimeLogsRaw error: ' . $e->getMessage());
        jsonResponse(false, '获取实时日志失败', null, 500);
    }
}

        error_log("Get operation logs error: " . $e->getMessage());
        // 即使出错也返回空数据而不是错误
        jsonResponse(true, '获取成功', [
            'logs' => [],
            'pagination' => [
                'page' => 1,
                'limit' => 20,
                'total' => 0,
                'pages' => 0
            ]
        ]);
    }
}

/**
 * 获取实时日志
 */
function getRealTimeLogs($db, $input) {
    try {
        $lastId = intval($input['last_id'] ?? 0);
        $limit = min(50, max(5, intval($input['limit'] ?? 10)));

        // 获取最新的日志记录
        $adminTable = getAdminTableName($db);
        $adminJoin = $adminTable ? "LEFT JOIN $adminTable aa ON ol.user_type = 'admin' AND ol.user_id = aa.id" : "";
        $sql = "SELECT ol.*,
                CASE

                    WHEN ol.user_type = 'customer' THEN ca.account_number
                    WHEN ol.user_type = 'admin' THEN aa.username
                    ELSE NULL
                END as user_name
                FROM operation_logs ol
                LEFT JOIN customer_accounts ca ON ol.user_type = 'customer' AND ol.user_id = ca.id
                $adminJoin
                WHERE ol.id > ?
                ORDER BY ol.created_at DESC
                LIMIT ?";

        try {
            $stmt = $db->prepare($sql);
            $stmt->execute([$lastId, $limit]);
            $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            // 兜底：不做任何 JOIN，直接查询
            $stmt = $db->prepare("SELECT * FROM operation_logs WHERE id > ? ORDER BY created_at DESC LIMIT ?");
            $stmt->execute([$lastId, $limit]);
            $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }

        // 获取最新的ID
        $newLastId = $lastId;
        if (!empty($logs)) {

            $newLastId = max(array_column($logs, 'id'));
        }

        jsonResponse(true, '获取成功', [
            'logs' => $logs,
            'last_id' => $newLastId,
            'has_new' => !empty($logs)
        ]);

    } catch (Exception $e) {
        error_log("Get real time logs error: " . $e->getMessage());
        jsonResponse(false, '获取实时日志失败', null, 500);
    }
}

/**
 * 获取客户统计
 */
function getCustomerStats($db, $input) {
    try {
        // 客户类型分布
        $stmt = $db->query("SELECT account_type, COUNT(*) as count FROM customer_accounts GROUP BY account_type");
        $typeDistribution = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 客户状态分布
        $stmt = $db->query("SELECT status, COUNT(*) as count FROM customer_accounts GROUP BY status");
        $statusDistribution = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 最近7天注册趋势
        $stmt = $db->query("SELECT DATE(created_at) as date, COUNT(*) as count
                           FROM customer_accounts
                           WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
                           GROUP BY DATE(created_at)
                           ORDER BY date");
        $registrationTrend = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 活跃客户（最近7天有登录）
        $stmt = $db->query("SELECT COUNT(DISTINCT user_id) as active_customers
                           FROM operation_logs
                           WHERE user_type = 'customer' AND action = 'login'
                           AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
        $activeCustomers = $stmt->fetch(PDO::FETCH_ASSOC);

        jsonResponse(true, '获取成功', [
            'type_distribution' => $typeDistribution,
            'status_distribution' => $statusDistribution,
            'registration_trend' => $registrationTrend,
            'active_customers' => $activeCustomers['active_customers']
        ]);

    } catch (Exception $e) {
        error_log("Get customer stats error: " . $e->getMessage());
        jsonResponse(false, '获取客户统计失败', null, 500);
    }
}

/**
 * 获取账号使用统计
 */
function getAccountUsageStats($db, $input) {
    try {
        // 账号使用率
        $stmt = $db->query("SELECT
            account_type,
            COUNT(*) as total,
            SUM(CASE WHEN status = 'in_use' THEN 1 ELSE 0 END) as in_use,
            ROUND(SUM(CASE WHEN status = 'in_use' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as usage_rate
            FROM game_accounts
            GROUP BY account_type");
        $usageRate = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 最受欢迎的账号（按使用次数）
        $stmt = $db->query("SELECT ga.account, ga.account_name, ga.level, ga.account_type, COUNT(gau.id) as usage_count
                           FROM game_accounts ga
                           LEFT JOIN game_account_usage gau ON ga.id = gau.game_account_id
                           GROUP BY ga.id
                           ORDER BY usage_count DESC
                           LIMIT 10");
        $popularAccounts = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 平均使用时长
        $stmt = $db->query("SELECT
            AVG(duration_minutes) as avg_duration,
            MIN(duration_minutes) as min_duration,
            MAX(duration_minutes) as max_duration
            FROM game_account_usage
            WHERE status = 'completed' AND duration_minutes > 0");
        $durationStats = $stmt->fetch(PDO::FETCH_ASSOC);

        jsonResponse(true, '获取成功', [
            'usage_rate' => $usageRate,
            'popular_accounts' => $popularAccounts,
            'duration_stats' => $durationStats
        ]);

    } catch (Exception $e) {
        error_log("Get account usage stats error: " . $e->getMessage());
        jsonResponse(false, '获取账号使用统计失败', null, 500);
    }
}

// 获取客户列表
function getCustomers($db, $input) {
    try {
        $page = max(1, intval($input['page'] ?? 1));
        $limit = min(100, max(10, intval($input['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;
        $search = $input['search'] ?? '';

        // 构建查询条件
        $whereClause = '';
        $params = [];

        if (!empty($search)) {
            $whereClause = "WHERE account_number LIKE ?";
            $params[] = "%$search%";
        }

        // 获取总数
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM customer_accounts $whereClause");
        $stmt->execute($params);
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // 获取客户列表
        $stmt = $db->prepare("SELECT * FROM customer_accounts $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
        $stmt->execute($params);
        $customers = $stmt->fetchAll(PDO::FETCH_ASSOC);

        jsonResponse(true, '获取成功', [
            'customers' => $customers,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ]);
    } catch (Exception $e) {
        error_log("Get customers error: " . $e->getMessage());
        jsonResponse(false, '获取客户列表失败');
    }
}

// 获取游戏账号列表
function getGameAccounts($db, $input) {
    try {
        $page = max(1, intval($input['page'] ?? 1));
        $limit = min(100, max(10, intval($input['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;
        $search = $input['search'] ?? '';

        // 构建查询条件
        $whereClause = '';
        $params = [];

        if (!empty($search)) {
            $whereClause = "WHERE account LIKE ? OR account_name LIKE ?";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }

        // 获取总数
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM game_accounts $whereClause");
        $stmt->execute($params);
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // 获取游戏账号列表
        $stmt = $db->prepare("SELECT * FROM game_accounts $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
        $stmt->execute($params);
        $accounts = $stmt->fetchAll(PDO::FETCH_ASSOC);

        jsonResponse(true, '获取成功', [
            'accounts' => $accounts,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ]);
    } catch (Exception $e) {
        error_log("Get game accounts error: " . $e->getMessage());
        jsonResponse(false, '获取游戏账号列表失败');
    }
}

// 获取密码修改记录
function getPasswordRecords($db, $input) {
    try {
        $page = max(1, intval($input['page'] ?? 1));
        $limit = min(100, max(10, intval($input['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;

        // 检查表是否存在（优先使用password_change_records表，与统计面板保持一致）
        $tableName = 'password_change_records';
        try {
            $stmt = $db->query("SHOW TABLES LIKE 'password_change_records'");
            if ($stmt->rowCount() == 0) {
                // 如果password_change_records不存在，尝试password_change_requests
                $stmt = $db->query("SHOW TABLES LIKE 'password_change_requests'");
                if ($stmt->rowCount() > 0) {
                    $tableName = 'password_change_requests';
                } else {
                    // 两个表都不存在，返回空数据
                    jsonResponse(true, '获取成功', [
                        'records' => [],
                        'total' => 0,
                        'page' => $page,
                        'limit' => $limit
                    ]);
                    return;
                }
            }
        } catch (Exception $e) {
            // 如果检查表失败，继续尝试查询
        }

        // 获取总数
        try {
            $stmt = $db->query("SELECT COUNT(*) as total FROM $tableName");
            $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        } catch (Exception $e) {
            $total = 0;
        }

        // 获取密码修改记录
        try {
            $stmt = $db->prepare("
                SELECT pcr.*, ca.account_number, ga.account as game_account
                FROM $tableName pcr
                LEFT JOIN customer_accounts ca ON pcr.customer_id = ca.id
                LEFT JOIN game_accounts ga ON pcr.game_account_id = ga.id
                ORDER BY pcr.created_at DESC
                LIMIT $limit OFFSET $offset
            ");
            $stmt->execute();
            $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $records = [];
        }

        jsonResponse(true, '获取成功', [
            'records' => $records,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ]);
    } catch (Exception $e) {
        error_log("Get password records error: " . $e->getMessage());
        // 即使出错也返回空数据而不是错误
        jsonResponse(true, '获取成功', [
            'records' => [],
            'total' => 0,
            'page' => 1,
            'limit' => 20
        ]);
    }
}

// 生成客户账号
function generateCustomers($db, $input) {
    try {
        $accountType = $input['account_type'] ?? 'normal';
        $durationHours = intval($input['duration_hours'] ?? 24);
        $count = intval($input['count'] ?? 1);

        if ($count < 1 || $count > 100) {
            jsonResponse(false, '生成数量必须在1-100之间');
            return;
        }

        if ($durationHours < 1) {
            jsonResponse(false, '时长必须大于0');
            return;
        }

        $generatedAccounts = [];

        for ($i = 0; $i < $count; $i++) {
            // 生成8位数字账号
            do {
                $accountNumber = str_pad(mt_rand(********, ********), 8, '0', STR_PAD_LEFT);
                $stmt = $db->prepare("SELECT COUNT(*) FROM customer_accounts WHERE account_number = ?");
                $stmt->execute([$accountNumber]);
                $exists = $stmt->fetchColumn() > 0;
            } while ($exists);

            // 插入客户账号
            $stmt = $db->prepare("
                INSERT INTO customer_accounts (account_number, account_type, duration_hours, remaining_hours, status, created_at)
                VALUES (?, ?, ?, ?, 'active', NOW())
            ");
            $stmt->execute([$accountNumber, $accountType, $durationHours, $durationHours]);

            $generatedAccounts[] = [
                'account_number' => $accountNumber,
                'account_type' => $accountType,
                'duration_hours' => $durationHours
            ];
        }

        // 记录操作日志
        logOperation($db, 'admin', $_SESSION['admin_id'] ?? 1, 'generate_customers',
            "生成客户账号: {$count}个{$accountType}账号，时长{$durationHours}小时");

        jsonResponse(true, "成功生成 {$count} 个客户账号", [
            'accounts' => $generatedAccounts,
            'count' => $count
        ]);
    } catch (Exception $e) {
        error_log("Generate customers error: " . $e->getMessage());
        jsonResponse(false, '生成客户账号失败');
    }
}

// 添加游戏账号
function addGameAccount($db, $input) {
    try {
        $account = trim($input['account'] ?? '');
        $password = trim($input['password'] ?? '');
        $accountName = trim($input['account_name'] ?? '');
        $level = intval($input['level'] ?? 0);
        $vipLevel = intval($input['vip_level'] ?? 0);
        $rank = trim($input['rank'] ?? '');
        $nationWar = trim($input['nation_war'] ?? '');
        $gender = trim($input['gender'] ?? '');
        // 处理性别字段，确保只有有效值才插入
        if (!in_array($gender, ['male', 'female'])) {
            $gender = null;
        }
        $price = floatval($input['price'] ?? 0);
        $accountType = $input['account_type'] ?? 'normal';
        $generalCount = intval($input['general_count'] ?? 0);
        $skinCount = intval($input['skin_count'] ?? 0);
        $premiumGenerals = trim($input['premium_generals'] ?? '');
        $dynamicSkins = trim($input['dynamic_skins'] ?? '');
        $skinDetails = trim($input['skin'] ?? '');

        if (empty($account) || empty($password)) {
            jsonResponse(false, '游戏账号和密码不能为空');
            return;
        }

        // 检查账号是否已存在
        $stmt = $db->prepare("SELECT COUNT(*) FROM game_accounts WHERE account = ?");
        $stmt->execute([$account]);
        if ($stmt->fetchColumn() > 0) {
            jsonResponse(false, '该游戏账号已存在');
            return;
        }

        // 计算史诗武将和动态皮肤数量
        $epicGeneralCount = 0;
        $dynamicSkinCount = 0;

        if (!empty($premiumGenerals)) {
            if (strpos($premiumGenerals, '[') === 0) {
                $decoded = json_decode($premiumGenerals, true);
                $epicGeneralCount = is_array($decoded) ? count($decoded) : 0;
            } else {
                $items = explode(',', $premiumGenerals);
                $items = array_filter(array_map('trim', $items));
                $epicGeneralCount = count($items);
            }
        }

        if (!empty($dynamicSkins)) {
            if (strpos($dynamicSkins, '[') === 0) {
                $decoded = json_decode($dynamicSkins, true);
                $dynamicSkinCount = is_array($decoded) ? count($decoded) : 0;
            } else {
                $items = explode(',', $dynamicSkins);
                $items = array_filter(array_map('trim', $items));
                $dynamicSkinCount = count($items);
            }
        }

        // 插入游戏账号
        $stmt = $db->prepare("
            INSERT INTO game_accounts (
                account, password, account_name, level, vip_level, `rank`, nation_war,
                skin, gender, price, premium_generals, dynamic_skins, general_count,
                skin_count, epic_general_count, dynamic_skin_count, account_type, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'available', NOW())
        ");
        $stmt->execute([
            $account, $password, $accountName, $level, $vipLevel, $rank, $nationWar,
            $skinDetails, $gender, $price, $premiumGenerals, $dynamicSkins,
            $generalCount, $skinCount, $epicGeneralCount, $dynamicSkinCount, $accountType
        ]);

        $gameAccountId = $db->lastInsertId();

        // 记录操作日志
        logOperation($db, 'admin', $_SESSION['admin_id'] ?? 1, 'add_game_account',
            "添加游戏账号: {$account} ({$accountName}), 等级{$level}, VIP{$vipLevel}");

        jsonResponse(true, '游戏账号添加成功', [
            'id' => $gameAccountId,
            'account' => $account,
            'account_name' => $accountName
        ]);
    } catch (Exception $e) {
        error_log("Add game account error: " . $e->getMessage());
        jsonResponse(false, '添加游戏账号失败');
    }
}

// 删除客户账号
function deleteCustomer($db, $input) {
    try {
        $customerId = intval($input['customer_id'] ?? 0);

        if ($customerId <= 0) {
            jsonResponse(false, '无效的客户ID');
            return;
        }

        // 检查客户是否存在
        $stmt = $db->prepare("SELECT account_number FROM customer_accounts WHERE id = ?");
        $stmt->execute([$customerId]);
        $customer = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$customer) {
            jsonResponse(false, '客户账号不存在');
            return;
        }

        // 释放该客户正在使用的游戏账号
        $stmt = $db->prepare("UPDATE game_accounts SET current_user_id = NULL, status = 'available' WHERE current_user_id = ?");
        $stmt->execute([$customerId]);

        // 删除客户账号
        $stmt = $db->prepare("DELETE FROM customer_accounts WHERE id = ?");
        $stmt->execute([$customerId]);

        // 记录操作日志
        logOperation($db, 'admin', $_SESSION['admin_id'] ?? 1, 'delete_customer',
            "删除客户账号: {$customer['account_number']}");

        jsonResponse(true, '客户账号删除成功', [
            'deleted_account' => $customer['account_number']
        ]);
    } catch (Exception $e) {
        error_log("Delete customer error: " . $e->getMessage());
        jsonResponse(false, '删除客户账号失败');
    }
}

// 删除游戏账号
function deleteGameAccount($db, $input) {
    try {
        $accountId = intval($input['account_id'] ?? 0);

        if ($accountId <= 0) {
            jsonResponse(false, '无效的账号ID');
            return;
        }

        // 检查游戏账号是否存在
        $stmt = $db->prepare("SELECT account, current_user_id FROM game_accounts WHERE id = ?");
        $stmt->execute([$accountId]);
        $account = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$account) {
            jsonResponse(false, '游戏账号不存在');
            return;
        }

        if ($account['current_user_id']) {
            jsonResponse(false, '该游戏账号正在使用中，无法删除');
            return;
        }

        // 删除游戏账号
        $stmt = $db->prepare("DELETE FROM game_accounts WHERE id = ?");
        $stmt->execute([$accountId]);

        jsonResponse(true, '游戏账号删除成功', [
            'deleted_account' => $account['account']
        ]);
    } catch (Exception $e) {
        error_log("Delete game account error: " . $e->getMessage());
        jsonResponse(false, '删除游戏账号失败');
    }
}

// 审核密码修改 - 通过
function approvePasswordChange($db, $input) {
    try {
        $recordId = intval($input['record_id'] ?? 0);

        if ($recordId <= 0) {
            jsonResponse(false, '无效的记录ID');
            return;
        }

        // 获取密码修改记录
        $stmt = $db->prepare("
            SELECT pcr.*, ga.id as game_account_id
            FROM password_change_records pcr
            JOIN game_accounts ga ON pcr.game_account_id = ga.id
            WHERE pcr.id = ? AND pcr.status = 'pending'
        ");
        $stmt->execute([$recordId]);
        $record = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$record) {
            jsonResponse(false, '记录不存在或已处理');
            return;
        }

        // 更新游戏账号密码
        $stmt = $db->prepare("UPDATE game_accounts SET password = ? WHERE id = ?");
        $stmt->execute([$record['new_password'], $record['game_account_id']]);

        // 更新记录状态
        $stmt = $db->prepare("UPDATE password_change_records SET status = 'approved', updated_at = NOW() WHERE id = ?");
        $stmt->execute([$recordId]);

        jsonResponse(true, '密码修改申请已通过');
    } catch (Exception $e) {
        error_log("Approve password change error: " . $e->getMessage());
        jsonResponse(false, '审核失败');
    }
}

// 审核密码修改 - 拒绝
function rejectPasswordChange($db, $input) {
    try {
        $recordId = intval($input['record_id'] ?? 0);
        $reason = trim($input['reason'] ?? '');

        if ($recordId <= 0) {
            jsonResponse(false, '无效的记录ID');
            return;
        }

        // 更新记录状态
        $stmt = $db->prepare("UPDATE password_change_records SET status = 'rejected', admin_note = ?, updated_at = NOW() WHERE id = ? AND status = 'pending'");
        $stmt->execute([$reason, $recordId]);

        if ($stmt->rowCount() > 0) {
            jsonResponse(true, '密码修改申请已拒绝');
        } else {
            jsonResponse(false, '记录不存在或已处理');
        }
    } catch (Exception $e) {
        error_log("Reject password change error: " . $e->getMessage());
        jsonResponse(false, '审核失败');
    }
}

// 删除密码记录
function deletePasswordRecord($db, $input) {
    try {
        $recordId = intval($input['record_id'] ?? 0);

        if ($recordId <= 0) {
            jsonResponse(false, '无效的记录ID');
            return;
        }

        // 检查记录是否存在（优先使用password_change_records表）
        $tableName = 'password_change_records';
        try {
            $stmt = $db->query("SHOW TABLES LIKE 'password_change_records'");
            if ($stmt->rowCount() == 0) {
                $stmt = $db->query("SHOW TABLES LIKE 'password_change_requests'");
                if ($stmt->rowCount() > 0) {
                    $tableName = 'password_change_requests';
                } else {
                    jsonResponse(false, '密码记录表不存在');
                    return;
                }
            }
        } catch (Exception $e) {
            // 继续尝试删除
        }

        // 获取记录信息用于日志
        $stmt = $db->prepare("SELECT * FROM $tableName WHERE id = ?");
        $stmt->execute([$recordId]);
        $record = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$record) {
            jsonResponse(false, '记录不存在');
            return;
        }

        // 删除记录
        $stmt = $db->prepare("DELETE FROM $tableName WHERE id = ?");
        $stmt->execute([$recordId]);

        if ($stmt->rowCount() > 0) {
            // 记录操作日志
            logOperation($db, 'admin', $_SESSION['admin_id'] ?? 1, 'delete_password_record',
                "删除密码记录: 游戏账号 {$record['game_account_id']}, 记录ID {$recordId}");

            jsonResponse(true, '密码记录删除成功');
        } else {
            jsonResponse(false, '删除失败，记录可能已被删除');
        }
    } catch (Exception $e) {
        error_log("Delete password record error: " . $e->getMessage());
        jsonResponse(false, '删除失败');
    }
}

// 修改管理员密码
function changeAdminPassword($db, $input) {
    try {
        // 启动会话（如果还没有启动）
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $currentPassword = $input['current_password'] ?? '';
        $newPassword = $input['new_password'] ?? '';
        $confirmPassword = $input['confirm_password'] ?? '';

        // 验证输入
        if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
            jsonResponse(false, '请填写所有密码字段');
            return;
        }

        if ($newPassword !== $confirmPassword) {
            jsonResponse(false, '新密码和确认密码不匹配');
            return;
        }

        if (strlen($newPassword) < 6) {
            jsonResponse(false, '新密码长度至少6位');
            return;
        }

        // 获取当前管理员信息
        $adminId = $_SESSION['admin_id'] ?? 0;
        if ($adminId <= 0) {
            error_log("Change admin password: Invalid admin session. Session data: " . print_r($_SESSION, true));
            jsonResponse(false, '管理员会话无效，请重新登录');
            return;
        }

        // 查询管理员信息
        $stmt = $db->prepare("SELECT * FROM admin_accounts WHERE id = ?");
        $stmt->execute([$adminId]);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$admin) {
            error_log("Change admin password: Admin not found. Admin ID: $adminId");
            jsonResponse(false, '管理员账号不存在');
            return;
        }

        // 验证当前密码
        if (!password_verify($currentPassword, $admin['password'])) {
            error_log("Change admin password: Current password verification failed for admin ID: $adminId");
            jsonResponse(false, '当前密码错误');
            return;
        }

        // 更新密码
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        $stmt = $db->prepare("UPDATE admin_accounts SET password = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$hashedPassword, $adminId]);

        if ($stmt->rowCount() > 0) {
            // 记录操作日志
            logOperation($db, 'admin', $adminId, 'change_password',
                "管理员 {$admin['username']} 修改密码");

            error_log("Change admin password: Password changed successfully for admin ID: $adminId");
            jsonResponse(true, '密码修改成功');
        } else {
            error_log("Change admin password: No rows affected for admin ID: $adminId");
            jsonResponse(false, '密码修改失败，请稍后重试');
        }
    } catch (PDOException $e) {
        error_log("Change admin password PDO error: " . $e->getMessage());
        jsonResponse(false, '数据库操作失败，请稍后重试');
    } catch (Exception $e) {
        error_log("Change admin password error: " . $e->getMessage());
        jsonResponse(false, '密码修改失败：' . $e->getMessage());
    }
}

// 检查会话状态（测试用）
function checkSessionStatus($db, $input) {
    try {
        $sessionInfo = [
            'session_started' => session_status() === PHP_SESSION_ACTIVE,
            'admin_id' => $_SESSION['admin_id'] ?? null,
            'admin_username' => $_SESSION['admin_username'] ?? null,
            'session_id' => session_id(),
            'session_data' => $_SESSION ?? []
        ];

        if (isset($_SESSION['admin_id']) && $_SESSION['admin_id'] > 0) {
            jsonResponse(true, '管理员会话有效', $sessionInfo);
        } else {
            jsonResponse(false, '管理员会话无效或未登录', $sessionInfo);
        }
    } catch (Exception $e) {
        error_log("Check session status error: " . $e->getMessage());
        jsonResponse(false, '检查会话状态失败');
    }
}

// 检查数据库状态（测试用）
function checkDatabaseStatus($db, $input) {
    try {
        $dbInfo = [];

        // 检查管理员表
        $tables = ['admin', 'admin_accounts'];
        foreach ($tables as $table) {
            try {
                $stmt = $db->query("SHOW TABLES LIKE '$table'");
                $exists = $stmt && $stmt->rowCount() > 0;
                $dbInfo['tables'][$table] = $exists;

                if ($exists) {
                    // 获取表结构
                    $stmt = $db->query("DESCRIBE $table");
                    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    $dbInfo['table_structure'][$table] = $columns;

                    // 获取记录数
                    $stmt = $db->query("SELECT COUNT(*) FROM $table");
                    $count = $stmt->fetchColumn();
                    $dbInfo['record_count'][$table] = $count;
                }
            } catch (Exception $e) {
                $dbInfo['tables'][$table] = false;
                $dbInfo['errors'][$table] = $e->getMessage();
            }
        }

        jsonResponse(true, '数据库状态检查完成', $dbInfo);
    } catch (Exception $e) {
        error_log("Check database status error: " . $e->getMessage());
        jsonResponse(false, '检查数据库状态失败：' . $e->getMessage());
    }
}

// 获取客户热搜词排行
function getHotSearchWords($db, $input) {
    try {
        $limit = min(20, max(5, intval($input['limit'] ?? 10)));
        // 仅按 search_keywords 表返回排行，避免 LIMIT 绑定兼容性问题，直接内联安全整数
        $limitSql = (int)$limit;
        try {
            $sql = "SELECT keyword, search_count, last_searched
                    FROM search_keywords
                    WHERE keyword IS NOT NULL AND keyword != ''


                    ORDER BY search_count DESC, last_searched DESC
                    LIMIT $limitSql";
            $stmt = $db->query($sql);
            $words = $stmt ? $stmt->fetchAll(PDO::FETCH_ASSOC) : [];
        } catch (Exception $e) {
            error_log('Search keywords query error: ' . $e->getMessage());
            $words = [];
        }
        jsonResponse(true, '获取成功', ['words' => $words]);
        return;


        // 从客户搜索记录获取真实数据
        $words = [];
        try {
            // 首先尝试从search_keywords表获取
            $stmt = $db->prepare("
                SELECT keyword, search_count
                FROM search_keywords
                WHERE keyword IS NOT NULL AND keyword != ''
                ORDER BY search_count DESC, last_searched DESC
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            $words = $stmt->fetchAll(PDO::FETCH_ASSOC);
            // 仅基于 search_keywords 表，直接返回
            jsonResponse(true, '获取成功', ['words' => $words]);
            return;

            // 不再从 operation_logs 回退统计，直接返回（上面已 return，这段是冗余保护）
            jsonResponse(true, '获取成功', ['words' => $words]);
            return;


            // 如果search_keywords表没有数据，从operation_logs获取
            if (empty($words)) {
                $stmt = $db->prepare("
                    SELECT details as keyword, COUNT(*) as search_count
                    FROM operation_logs
                    WHERE action = 'search' AND details IS NOT NULL AND details != '' AND user_type = 'customer'
                    GROUP BY details
                    ORDER BY search_count DESC, MAX(created_at) DESC
                    LIMIT ?
                ");
                $stmt->execute([$limit]);
                $words = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
        } catch (Exception $e) {
            error_log("Search keywords query error: " . $e->getMessage());
        }

        // 如果没有任何搜索记录，返回空数组而不是模拟数据
        if (empty($words)) {
            $words = [];
        }

        jsonResponse(true, '获取成功', ['words' => $words]);
    } catch (Exception $e) {
        error_log("Get hot search words error: " . $e->getMessage());
        jsonResponse(false, '获取热搜词失败');
    }
}

// 获取客户详情
function getCustomerDetail($db, $input) {
    try {
        $customerId = intval($input['customer_id'] ?? 0);

        if ($customerId <= 0) {
            jsonResponse(false, '无效的客户ID');
            return;
        }

        $stmt = $db->prepare("SELECT * FROM customer_accounts WHERE id = ?");
        $stmt->execute([$customerId]);
        $customer = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$customer) {
            jsonResponse(false, '客户不存在');

// 清空热搜关键词表
function clearHotSearchWords($db, $input) {
    try {
        $confirm = $input['confirm'] ?? '';
        if ($confirm !== 'yes') {
            jsonResponse(false, '缺少确认参数confirm=yes');
            return;
        }
        $db->exec("TRUNCATE TABLE search_keywords");
        // 记录操作日志
        logOperation($db, 'admin', $_SESSION['admin_id'] ?? 1, 'clear_hot_search_words', '清空热搜关键词');
        jsonResponse(true, '已清空热搜关键词');
    } catch (Exception $e) {
        error_log('Clear hot search words error: ' . $e->getMessage());
        jsonResponse(false, '清空失败');
    }
}

            return;
        }

        jsonResponse(true, '获取成功', $customer);
    } catch (Exception $e) {
        error_log("Get customer detail error: " . $e->getMessage());
        jsonResponse(false, '获取客户详情失败');
    }
}

// 更新客户信息
function updateCustomer($db, $input) {
    try {
        $customerId = intval($input['customer_id'] ?? 0);
        $accountType = $input['account_type'] ?? 'normal';
        $durationHours = intval($input['duration_hours'] ?? 0);
        $remainingHours = floatval($input['remaining_hours'] ?? 0);
        $status = $input['status'] ?? 'active';

        if ($customerId <= 0) {
            jsonResponse(false, '无效的客户ID');
            return;
        }

        if ($durationHours < 1) {
            jsonResponse(false, '总时长必须大于0');
            return;
        }

        if ($remainingHours < 0) {
            jsonResponse(false, '剩余时长不能为负数');
            return;
        }

        $stmt = $db->prepare("
            UPDATE customer_accounts
            SET account_type = ?, duration_hours = ?, remaining_hours = ?, status = ?, updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$accountType, $durationHours, $remainingHours, $status, $customerId]);

        if ($stmt->rowCount() > 0) {
            jsonResponse(true, '客户信息更新成功');
        } else {
            jsonResponse(false, '客户不存在或信息未变更');
        }
    } catch (Exception $e) {
        error_log("Update customer error: " . $e->getMessage());
        jsonResponse(false, '更新客户信息失败');
    }
}

// 切换客户状态
function toggleCustomerStatus($db, $input) {
    try {
        $customerId = intval($input['customer_id'] ?? 0);
        $status = $input['status'] ?? 'active';

        if ($customerId <= 0) {
            jsonResponse(false, '无效的客户ID');
            return;
        }

        if (!in_array($status, ['active', 'banned', 'expired'])) {
            jsonResponse(false, '无效的状态值');
            return;
        }

        $stmt = $db->prepare("UPDATE customer_accounts SET status = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$status, $customerId]);

        if ($stmt->rowCount() > 0) {
            jsonResponse(true, '客户状态更新成功');
        } else {
            jsonResponse(false, '客户不存在');
        }
    } catch (Exception $e) {
        error_log("Toggle customer status error: " . $e->getMessage());
        jsonResponse(false, '更新客户状态失败');
    }
}

// 获取游戏账号详情
function getGameAccountDetail($db, $input) {
    try {
        $accountId = intval($input['account_id'] ?? 0);

        if ($accountId <= 0) {
            jsonResponse(false, '无效的账号ID');
            return;
        }

        // 先检查是否有任何游戏账号
        $stmt = $db->query("SELECT COUNT(*) as total FROM game_accounts");
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        if ($total == 0) {
            jsonResponse(false, '系统中暂无游戏账号，请先添加游戏账号');
            return;
        }

        // 如果指定ID不存在，返回第一个账号
        $stmt = $db->prepare("SELECT * FROM game_accounts WHERE id = ?");
        $stmt->execute([$accountId]);
        $account = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$account) {
            // 获取第一个可用的游戏账号
            $stmt = $db->query("SELECT * FROM game_accounts ORDER BY id ASC LIMIT 1");
            $account = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$account) {
                jsonResponse(false, '游戏账号不存在');
                return;
            }
        }

        jsonResponse(true, '获取成功', $account);
    } catch (Exception $e) {
        error_log("Get game account detail error: " . $e->getMessage());
        jsonResponse(false, '获取游戏账号详情失败');
    }
}

// 更新游戏账号
function updateGameAccount($db, $input) {
    try {
        $accountId = intval($input['account_id'] ?? 0);
        $account = trim($input['account'] ?? '');
        $password = trim($input['password'] ?? '');
        $accountName = trim($input['account_name'] ?? '');
        $level = intval($input['level'] ?? 0);
        $vipLevel = intval($input['vip_level'] ?? 0);
        $rank = trim($input['rank'] ?? '');
        $nationWar = trim($input['nation_war'] ?? '');
        $gender = trim($input['gender'] ?? '');
        // 处理性别字段，确保只有有效值才更新
        if (!in_array($gender, ['male', 'female'])) {
            $gender = null;
        }
        $price = floatval($input['price'] ?? 0);
        $accountType = $input['account_type'] ?? 'normal';
        $generalCount = intval($input['general_count'] ?? 0);
        $skinCount = intval($input['skin_count'] ?? 0);
        $premiumGenerals = trim($input['premium_generals'] ?? '');
        $dynamicSkins = trim($input['dynamic_skins'] ?? '');
        $skinDetails = trim($input['skin'] ?? '');

        if ($accountId <= 0) {
            jsonResponse(false, '无效的账号ID');
            return;
        }

        if (empty($account) || empty($password)) {
            jsonResponse(false, '游戏账号和密码不能为空');
            return;
        }

        // 检查账号是否被其他记录使用
        $stmt = $db->prepare("SELECT COUNT(*) FROM game_accounts WHERE account = ? AND id != ?");
        $stmt->execute([$account, $accountId]);
        if ($stmt->fetchColumn() > 0) {
            jsonResponse(false, '该游戏账号已被其他记录使用');
            return;
        }

        // 计算史诗武将和动态皮肤数量
        $epicGeneralCount = 0;
        $dynamicSkinCount = 0;

        if (!empty($premiumGenerals)) {
            if (strpos($premiumGenerals, '[') === 0) {
                $decoded = json_decode($premiumGenerals, true);
                $epicGeneralCount = is_array($decoded) ? count($decoded) : 0;
            } else {
                $items = explode(',', $premiumGenerals);
                $items = array_filter(array_map('trim', $items));
                $epicGeneralCount = count($items);
            }
        }

        if (!empty($dynamicSkins)) {
            if (strpos($dynamicSkins, '[') === 0) {
                $decoded = json_decode($dynamicSkins, true);
                $dynamicSkinCount = is_array($decoded) ? count($decoded) : 0;
            } else {
                $items = explode(',', $dynamicSkins);
                $items = array_filter(array_map('trim', $items));
                $dynamicSkinCount = count($items);
            }
        }

        $stmt = $db->prepare("
            UPDATE game_accounts
            SET account = ?, password = ?, account_name = ?, level = ?, vip_level = ?,
                `rank` = ?, nation_war = ?, gender = ?, price = ?, account_type = ?,
                general_count = ?, skin_count = ?, premium_generals = ?,
                dynamic_skins = ?, skin = ?, epic_general_count = ?,
                dynamic_skin_count = ?, updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([
            $account, $password, $accountName, $level, $vipLevel,
            $rank, $nationWar, $gender, $price, $accountType,
            $generalCount, $skinCount, $premiumGenerals,
            $dynamicSkins, $skinDetails, $epicGeneralCount,
            $dynamicSkinCount, $accountId
        ]);

        if ($stmt->rowCount() > 0) {
            jsonResponse(true, '游戏账号更新成功');
        } else {
            jsonResponse(false, '游戏账号不存在或信息未变更');
        }
    } catch (Exception $e) {
        error_log("Update game account error: " . $e->getMessage());
        jsonResponse(false, '更新游戏账号失败');
    }
}

// 自动提交密码修改记录（客户时长到期或切换账号时调用）
function autoSubmitPasswordChange($db, $input) {
    try {
        $customerId = intval($input['customer_id'] ?? 0);
        $gameAccountId = intval($input['game_account_id'] ?? 0);
        $reason = trim($input['reason'] ?? ''); // 'expired' 或 'switched'

        if ($customerId <= 0 || $gameAccountId <= 0) {
            jsonResponse(false, '无效的参数');
            return;
        }

        // 获取游戏账号当前密码
        $stmt = $db->prepare("SELECT account, password FROM game_accounts WHERE id = ?");
        $stmt->execute([$gameAccountId]);
        $gameAccount = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$gameAccount) {
            jsonResponse(false, '游戏账号不存在');
            return;
        }

        // 智能重复检查：检查是否已有相同原因的待处理记录
        $stmt = $db->prepare("
            SELECT id, created_at FROM password_change_records
            WHERE game_account_id = ? AND status = 'pending' AND reason = ?
            ORDER BY created_at DESC LIMIT 1
        ");
        $stmt->execute([$gameAccountId, $reason]);
        $existingRecord = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existingRecord) {
            // 如果存在相同原因的记录，检查时间间隔
            $recordTime = new DateTime($existingRecord['created_at']);
            $now = new DateTime();
            $interval = $now->diff($recordTime);
            $hoursDiff = $interval->h + ($interval->days * 24);

            if ($hoursDiff < 1) {
                // 1小时内的相同原因记录，更新时间而不创建新记录
                $stmt = $db->prepare("
                    UPDATE password_change_records
                    SET created_at = NOW(), old_password = ?
                    WHERE id = ?
                ");
                $stmt->execute([$gameAccount['password'], $existingRecord['id']]);

                logOperation($db, 'system', 0, 'update_password_submit',
                    "自动更新密码修改记录: 游戏账号 {$gameAccount['account']}, 原因: $reason");

                jsonResponse(true, '密码修改记录已更新');
                return;
            }
            // 超过1小时，继续创建新记录
        }

        // 生成新密码建议
        $newPassword = generateRandomPassword();

        // 插入密码修改记录
        $stmt = $db->prepare("
            INSERT INTO password_change_records
            (customer_id, game_account_id, old_password, new_password, status, reason, created_at)
            VALUES (?, ?, ?, ?, 'pending', ?, NOW())
        ");
        $stmt->execute([
            $customerId,
            $gameAccountId,
            $gameAccount['password'],
            $newPassword,
            $reason
        ]);

        // 记录操作日志
        logOperation($db, 'system', 0, 'auto_password_change',
            "自动提交密码修改: 游戏账号 {$gameAccount['account']}, 原因: $reason");

        jsonResponse(true, '密码修改记录已自动提交', [
            'record_id' => $db->lastInsertId(),
            'suggested_password' => $newPassword
        ]);

    } catch (Exception $e) {
        error_log("Auto submit password change error: " . $e->getMessage());
        jsonResponse(false, '自动提交密码修改记录失败');
    }
}

// 管理员手动更新密码
function manualUpdatePassword($db, $input) {
    try {
        $recordId = intval($input['record_id'] ?? 0);
        $newPassword = trim($input['new_password'] ?? '');

        if ($recordId <= 0 || empty($newPassword)) {
            jsonResponse(false, '请提供有效的记录ID和新密码');
            return;
        }

        // 检查表名并获取密码修改记录（优先使用password_change_records表）
        $tableName = 'password_change_records';
        try {
            $stmt = $db->query("SHOW TABLES LIKE 'password_change_records'");
            if ($stmt->rowCount() == 0) {
                $stmt = $db->query("SHOW TABLES LIKE 'password_change_requests'");
                if ($stmt->rowCount() > 0) {
                    $tableName = 'password_change_requests';
                } else {
                    jsonResponse(false, '密码记录表不存在');
                    return;
                }
            }
        } catch (Exception $e) {
            // 继续尝试查询
        }

        $stmt = $db->prepare("
            SELECT pcr.*, ga.account, ga.id as game_account_id
            FROM $tableName pcr
            JOIN game_accounts ga ON pcr.game_account_id = ga.id
            WHERE pcr.id = ? AND pcr.status = 'pending'
        ");
        $stmt->execute([$recordId]);
        $record = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$record) {
            jsonResponse(false, '记录不存在或已处理');
            return;
        }

        // 开始事务
        $db->beginTransaction();

        try {
            // 更新游戏账号密码
            $stmt = $db->prepare("UPDATE game_accounts SET password = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$newPassword, $record['game_account_id']]);

            // 删除密码修改记录（完成后清除）
            $stmt = $db->prepare("DELETE FROM $tableName WHERE id = ?");
            $stmt->execute([$recordId]);

            // 记录操作日志
            logOperation($db, 'admin', $_SESSION['admin_id'] ?? 1, 'manual_password_update',
                "手动更新密码: 游戏账号 {$record['account']}");

            $db->commit();

            jsonResponse(true, '密码更新成功，记录已清除', [
                'game_account' => $record['account'],
                'new_password' => $newPassword
            ]);

        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }

    } catch (Exception $e) {
        error_log("Manual update password error: " . $e->getMessage());
        jsonResponse(false, '手动更新密码失败');
    }
}

// 生成随机密码
function generateRandomPassword($length = 8) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $password = '';
    for ($i = 0; $i < $length; $i++) {
        $password .= $chars[rand(0, strlen($chars) - 1)];
    }
    return $password;
}

/**
 * 更新密码修改记录表结构
 */
function updatePasswordTableStructure($db, $input) {
    try {
        // 更新 password_change_records 表的 reason 字段，添加 'released' 选项
        $sql = "ALTER TABLE password_change_records
                MODIFY COLUMN reason ENUM('expired', 'switched', 'manual', 'released') DEFAULT 'manual'";

        $db->exec($sql);

        jsonResponse(true, '密码修改记录表结构更新成功', [
            'updated_field' => 'reason',
            'new_values' => ['expired', 'switched', 'manual', 'released']
        ]);

    } catch (Exception $e) {
        error_log("Update password table structure error: " . $e->getMessage());
        jsonResponse(false, '更新表结构失败: ' . $e->getMessage());
    }
}

/**
 * 检查密码修改记录表结构
 */
function checkPasswordTableStructure($db, $input) {
    try {
        // 查询表结构
        $stmt = $db->query("SHOW COLUMNS FROM password_change_records WHERE Field = 'reason'");
        $column = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($column) {
            jsonResponse(true, '表结构检查完成', [
                'field' => $column['Field'],
                'type' => $column['Type'],
                'default' => $column['Default']
            ]);
        } else {
            jsonResponse(false, '未找到 reason 字段');
        }

    } catch (Exception $e) {
        error_log("Check password table structure error: " . $e->getMessage());
        jsonResponse(false, '检查表结构失败: ' . $e->getMessage());
    }
}

// 更新客户剩余时长
function updateRemainingTime($db, $input) {
    try {
        // 执行剩余时长更新
        $stmt = $db->prepare("
            UPDATE customer_accounts
            SET remaining_hours = CASE
                WHEN first_login_time IS NULL THEN duration_hours
                WHEN expires_at IS NOT NULL THEN GREATEST(0, TIMESTAMPDIFF(SECOND, NOW(), expires_at) / 3600)
                ELSE GREATEST(0, duration_hours - TIMESTAMPDIFF(SECOND, first_login_time, NOW()) / 3600)
            END,
            status = CASE
                WHEN first_login_time IS NOT NULL AND (
                    (expires_at IS NOT NULL AND expires_at <= NOW()) OR
                    (expires_at IS NULL AND TIMESTAMPDIFF(SECOND, first_login_time, NOW()) / 3600 >= duration_hours)
                ) THEN 'expired'
                WHEN status = 'expired' AND first_login_time IS NOT NULL AND (
                    (expires_at IS NOT NULL AND expires_at > NOW()) OR
                    (expires_at IS NULL AND TIMESTAMPDIFF(SECOND, first_login_time, NOW()) / 3600 < duration_hours)
                ) THEN 'active'
                ELSE status
            END,
            updated_at = NOW()
            WHERE status != 'banned'
        ");
        $stmt->execute();
        $updatedRows = $stmt->rowCount();

        // 处理过期客户的游戏账号
        $stmt = $db->prepare("
            UPDATE game_accounts ga
            JOIN customer_accounts ca ON ga.current_user_id = ca.id
            SET ga.current_user_id = NULL, ga.status = 'available', ga.taken_at = NULL
            WHERE ca.status = 'expired' AND ga.status = 'in_use'
        ");
        $stmt->execute();
        $releasedAccounts = $stmt->rowCount();

        jsonResponse(true, '剩余时长更新成功', [
            'updated_customers' => $updatedRows,
            'released_accounts' => $releasedAccounts
        ]);

    } catch (Exception $e) {
        error_log("Update remaining time error: " . $e->getMessage());
        jsonResponse(false, '更新剩余时长失败');
    }
}

// 记录搜索关键词
function recordSearchKeyword($db, $input) {
    try {
        $keyword = trim($input['keyword'] ?? '');
        $customerId = intval($input['customer_id'] ?? 1);

        if (empty($keyword)) {
            jsonResponse(false, '搜索关键词不能为空');
            return;
        }

        // 记录到搜索关键词表
        $stmt = $db->prepare("
            INSERT INTO search_keywords (keyword, search_count, last_searched)
            VALUES (?, 1, NOW())
            ON DUPLICATE KEY UPDATE
            search_count = search_count + 1,
            last_searched = NOW()
        ");
        $stmt->execute([$keyword]);

        // 记录操作日志
        $stmt = $db->prepare("
            INSERT INTO operation_logs (user_type, user_id, action, description, ip_address, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            'customer',
            $customerId,
            'search',
            "搜索关键词: {$keyword}",
            $_SERVER['REMOTE_ADDR'] ?? ''
        ]);

        jsonResponse(true, '搜索记录已添加');

    } catch (Exception $e) {
        error_log("Record search keyword error: " . $e->getMessage());
        jsonResponse(false, '记录搜索关键词失败');
    }
}
?>
