<?php
// 清除热搜关键词API
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    require_once '../config/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        echo json_encode(['success' => false, 'message' => '数据库连接失败'], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 获取清除前的记录数
    $stmt = $db->query("SELECT COUNT(*) as count FROM search_keywords");
    $beforeCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // 清空search_keywords表
    $stmt = $db->prepare("DELETE FROM search_keywords");
    $result = $stmt->execute();
    
    if ($result) {
        // 记录操作日志
        try {
            $stmt = $db->prepare("INSERT INTO operation_logs (user_type, user_id, action, description, ip_address, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            $stmt->execute(['admin', 'admin', 'clear_search_keywords', "清空热搜关键词，删除{$beforeCount}条记录", $_SERVER['REMOTE_ADDR'] ?? '']);
        } catch (Exception $e) {
            // 记录日志失败不影响主要操作
        }
        
        echo json_encode([
            'success' => true,
            'message' => "热搜关键词已清空，共删除{$beforeCount}条记录",
            'deleted_count' => $beforeCount
        ], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode(['success' => false, 'message' => '清空失败'], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'message' => '错误: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
