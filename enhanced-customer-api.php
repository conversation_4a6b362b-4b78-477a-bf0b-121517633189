<?php
/**
 * 增强版客户端API - 带详细错误日志
 */

// 启用详细错误日志
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/api_debug.log');

// 创建日志目录
if (!is_dir(__DIR__ . '/../logs')) {
    mkdir(__DIR__ . '/../logs', 0755, true);
}

// 记录详细日志
function debugLog($message, $data = null) {
    $logMessage = date('Y-m-d H:i:s') . ' [DEBUG] ' . $message;
    if ($data !== null) {
        $logMessage .= ' | Data: ' . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    error_log($logMessage);
}

// 设置响应头
header("Content-Type: application/json; charset=utf-8");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// 处理预检请求
if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    debugLog("收到OPTIONS预检请求");
    exit(0);
}

debugLog("API请求开始", [
    'method' => $_SERVER['REQUEST_METHOD'],
    'uri' => $_SERVER['REQUEST_URI'],
    'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
]);

try {
    // 引入配置
    require_once __DIR__ . '/../config.php';
    require_once __DIR__ . '/../config/database.php';
    
    debugLog("配置文件加载成功");
    
    // 数据库连接
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('数据库连接失败');
    }
    
    debugLog("数据库连接成功");
    
    // 获取输入数据
    $rawInput = file_get_contents("php://input");
    debugLog("原始输入数据", $rawInput);
    
    if (empty($rawInput)) {
        jsonResponse(false, "请求体为空");
    }
    
    $input = json_decode($rawInput, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        debugLog("JSON解析错误", json_last_error_msg());
        jsonResponse(false, "JSON解析错误: " . json_last_error_msg());
    }
    
    debugLog("输入数据解析成功", $input);
    
    $action = $input["action"] ?? "";
    debugLog("执行操作", $action);
    
    // 启动会话
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // 路由处理
    switch ($action) {
        case "login":
            debugLog("处理登录请求");
            customerLogin($db, $input);
            break;
            
        case "get_available_accounts":
            debugLog("处理获取账号列表请求");
            getAvailableAccounts($db, $input);
            break;
            
        case "take_account":
            debugLog("处理取号请求");
            takeAccount($db, $input);
            break;
            
        case "release_account":
            debugLog("处理释放账号请求");
            releaseAccount($db, $input);
            break;
            
        case "check_session":
            debugLog("处理会话检查请求");
            checkCustomerSession($db, $input);
            break;
            
        default:
            debugLog("未知操作", $action);
            jsonResponse(false, "未知的操作: " . $action);
    }
    
} catch (Exception $e) {
    debugLog("API异常", [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
    
    jsonResponse(false, "服务器内部错误: " . $e->getMessage());
}

// 客户登录函数
function customerLogin($db, $input) {
    try {
        debugLog("开始登录验证");
        
        $customerAccount = trim($input["customer_account"] ?? "");
        if (empty($customerAccount)) {
            jsonResponse(false, "请输入客户账号");
        }
        
        debugLog("查询客户账号", $customerAccount);
        
        // 查询客户账号
        $stmt = $db->prepare("SELECT * FROM customer_accounts WHERE account_number = ?");
        $stmt->execute([$customerAccount]);
        $customer = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$customer) {
            debugLog("客户账号不存在", $customerAccount);
            jsonResponse(false, "客户账号不存在");
        }
        
        debugLog("客户账号查询成功", [
            'id' => $customer['id'],
            'account_type' => $customer['account_type'],
            'status' => $customer['status']
        ]);
        
        // 检查账号状态
        if ($customer["status"] === "banned") {
            jsonResponse(false, "账号已被封禁");
        }
        
        // 生成会话token
        $sessionToken = bin2hex(random_bytes(32));
        $sessionExpires = date("Y-m-d H:i:s", time() + 7200); // 2小时
        
        debugLog("生成会话token", $sessionToken);
        
        // 更新会话信息
        $clientIp = $_SERVER['REMOTE_ADDR'] ?? '';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        $stmt = $db->prepare("
            UPDATE customer_accounts
            SET session_token = ?, session_expires = ?, last_login = NOW(),
                current_ip_address = ?, current_user_agent = ?
            WHERE id = ?
        ");
        $stmt->execute([$sessionToken, $sessionExpires, $clientIp, $userAgent, $customer["id"]]);
        
        debugLog("会话信息更新成功");
        
        // 设置PHP会话
        $_SESSION["customer_id"] = $customer["id"];
        $_SESSION["session_token"] = $sessionToken;
        
        jsonResponse(true, "登录成功", [
            "customer_id" => $customer["id"],
            "account_number" => $customer["account_number"],
            "account_type" => $customer["account_type"],
            "session_token" => $sessionToken
        ]);
        
    } catch (Exception $e) {
        debugLog("登录异常", $e->getMessage());
        jsonResponse(false, "登录失败: " . $e->getMessage());
    }
}

// 获取可用账号
function getAvailableAccounts($db, $input) {
    try {
        debugLog("开始获取账号列表");
        
        $sessionToken = $input["session_token"] ?? "";
        if (empty($sessionToken)) {
            jsonResponse(false, "缺少会话令牌");
        }
        
        debugLog("验证会话", $sessionToken);
        
        // 简化的会话验证（临时禁用IP检查）
        $stmt = $db->prepare("
            SELECT id FROM customer_accounts 
            WHERE session_token = ? AND session_expires > NOW()
        ");
        $stmt->execute([$sessionToken]);
        $customer = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$customer) {
            debugLog("会话无效或已过期");
            jsonResponse(false, "会话无效，请重新登录");
        }
        
        debugLog("会话验证成功", $customer['id']);
        
        // 获取游戏账号列表
        $stmt = $db->prepare("
            SELECT id, account, account_name, level, vip_level, rank,
                   nation_war, account_type, status, description, skin,
                   premium_generals, dynamic_skins, general_count, skin_count,
                   epic_general_count, dynamic_skin_count, epic_generals,
                   dynamic_skin_details, general_details, skin_details, gender, price
            FROM game_accounts
            ORDER BY account_type DESC, level DESC
        ");
        $stmt->execute();
        $accounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        debugLog("查询到游戏账号数量", count($accounts));
        
        // 分类账号
        $available = [];
        $inUse = [];
        
        foreach ($accounts as $account) {
            if ($account['status'] === 'available') {
                $available[] = $account;
            } elseif ($account['status'] === 'in_use') {
                $inUse[] = $account;
            }
        }
        
        debugLog("账号分类完成", [
            'available' => count($available),
            'in_use' => count($inUse)
        ]);
        
        jsonResponse(true, "获取成功", [
            "available_accounts" => $available,
            "in_use_accounts" => $inUse,
            "has_account_in_use" => false
        ]);
        
    } catch (Exception $e) {
        debugLog("获取账号列表异常", $e->getMessage());
        jsonResponse(false, "获取账号列表失败: " . $e->getMessage());
    }
}

// 其他函数的简化实现...
function takeAccount($db, $input) {
    jsonResponse(false, "取号功能暂时维护中");
}

function releaseAccount($db, $input) {
    jsonResponse(false, "释放功能暂时维护中");
}

function checkCustomerSession($db, $input) {
    jsonResponse(true, "会话检查成功");
}

debugLog("API请求处理完成");
?>
