<?php
/**
 * 一键安装脚本
 * 清除所有数据并重新安装系统
 */

// 设置执行时间限制
set_time_limit(300);
ini_set('memory_limit', '256M');

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>一键安装系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn.danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .step {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 一键安装系统</h1>
        <p>此脚本将完全清除现有数据并重新安装整个系统</p>

        <div class="step">
            <h3>⚠️ 重要警告</h3>
            <p style="color: red; font-weight: bold;">
                此操作将：<br>
                1. 删除所有数据库表和数据<br>
                2. 清除所有测试文件<br>
                3. 重新创建数据库结构<br>
                4. 初始化系统配置<br>
                <br>
                <strong>此操作不可逆！请确保您已备份重要数据！</strong>
            </p>
        </div>

        <div class="step">
            <h3>📋 安装步骤</h3>
            <div class="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            <div id="progressText">准备安装...</div>
            
            <button class="btn danger" onclick="startInstall()" id="installBtn">开始一键安装</button>
            <button class="btn" onclick="cleanTestFiles()" id="cleanBtn">仅清理测试文件</button>
        </div>

        <div class="log" id="log">点击"开始一键安装"开始...</div>
    </div>

    <script>
        let currentStep = 0;
        const totalSteps = 8;

        function updateProgress(step, message) {
            currentStep = step;
            const percentage = (step / totalSteps) * 100;
            document.getElementById('progressBar').style.width = percentage + '%';
            document.getElementById('progressText').textContent = message;
        }

        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : 
                             type === 'error' ? 'error' : 
                             type === 'warning' ? 'warning' : 'info';
            
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        async function startInstall() {
            if (!confirm('确定要开始一键安装吗？这将删除所有现有数据！')) {
                return;
            }

            document.getElementById('installBtn').disabled = true;
            document.getElementById('cleanBtn').disabled = true;
            
            log('🚀 开始一键安装...', 'info');
            
            try {
                // 步骤1: 清理测试文件
                updateProgress(1, '步骤1: 清理测试文件...');
                await makeRequest('clean_files');

                // 步骤2: 备份配置
                updateProgress(2, '步骤2: 备份配置文件...');
                await makeRequest('backup_config');

                // 步骤3: 清除数据库
                updateProgress(3, '步骤3: 清除数据库...');
                await makeRequest('clear_database');

                // 步骤4: 创建数据库结构
                updateProgress(4, '步骤4: 创建数据库结构...');
                await makeRequest('create_database');

                // 步骤5: 初始化数据
                updateProgress(5, '步骤5: 初始化基础数据...');
                await makeRequest('init_data');

                // 步骤6: 创建管理员账号
                updateProgress(6, '步骤6: 创建管理员账号...');
                await makeRequest('create_admin');

                // 步骤7: 设置权限
                updateProgress(7, '步骤7: 设置文件权限...');
                await makeRequest('set_permissions');

                // 步骤8: 完成安装
                updateProgress(8, '安装完成！');
                log('🎉 一键安装完成！', 'success');
                log('', 'info');
                log('📋 安装结果:', 'info');
                log('✅ 数据库已重新创建', 'success');
                log('✅ 系统配置已初始化', 'success');
                log('✅ 测试文件已清理', 'success');
                log('✅ 管理员账号: admin / admin123', 'success');
                log('', 'info');
                log('🎮 现在可以访问:', 'info');
                log('- 管理后台: dashboard.html', 'info');
                log('- 客户端: customer-dashboard.html', 'info');

            } catch (error) {
                log(`❌ 安装失败: ${error.message}`, 'error');
                updateProgress(currentStep, '安装失败');
            } finally {
                document.getElementById('installBtn').disabled = false;
                document.getElementById('cleanBtn').disabled = false;
            }
        }

        async function cleanTestFiles() {
            if (!confirm('确定要清理测试文件吗？')) {
                return;
            }

            log('🧹 开始清理测试文件...', 'info');
            
            try {
                await makeRequest('clean_files');
                log('✅ 测试文件清理完成', 'success');
            } catch (error) {
                log(`❌ 清理失败: ${error.message}`, 'error');
            }
        }

        async function makeRequest(action) {
            const response = await fetch('one_click_install.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=${action}`
            });

            const result = await response.json();
            
            if (result.success) {
                log(result.message, 'success');
                if (result.details) {
                    result.details.forEach(detail => log(`  ${detail}`, 'info'));
                }
            } else {
                throw new Error(result.message);
            }
        }
    </script>
</body>
</html>

<?php
// 处理POST请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json; charset=utf-8');
    
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'clean_files':
                $result = cleanTestFiles();
                break;
            case 'backup_config':
                $result = backupConfig();
                break;
            case 'clear_database':
                $result = clearDatabase();
                break;
            case 'create_database':
                $result = createDatabase();
                break;
            case 'init_data':
                $result = initializeData();
                break;
            case 'create_admin':
                $result = createAdminAccount();
                break;
            case 'set_permissions':
                $result = setPermissions();
                break;
            default:
                throw new Exception('未知操作');
        }
        
        echo json_encode(['success' => true, 'message' => $result['message'], 'details' => $result['details'] ?? []]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// 清理测试文件
function cleanTestFiles() {
    $testFiles = [
        // 测试和调试文件
        'test_*.html', 'test_*.php', 'debug_*.php', 'debug_*.html',
        'client_debug.html', 'customer_debug.html', 'final_test.html',
        'quick_fix.html', 'test_account_list.html', 'test_api.php',
        
        // 修复和诊断文件
        'fix_*.php', 'fix_*.html', 'diagnose_*.php', 'create_test_*.php',
        'debug_session.php', 'debug_api_detailed.php', 'api_test.php',
        
        // 临时文件
        'client_*.html', 'complete_fix_guide.html', 'dashboard_*.html',
        'account_display_fix.html', 'data_format_fix.html', 'format_fix_guide.html',
        
        // 说明文档
        '*.md', '优化说明.md', '问题修复完成报告.md',
        
        // 迁移和更新文件
        '一键迁移.php', '修复迁移.php', '最终修复.php', '临时修复登录.php',
        '补充迁移.php', '调试登录.php', 'organize_files.php',
        
        // SQL更新文件
        'update_*.sql', 'fix_*.sql', 'database_migration_*.sql',
        
        // 其他测试文件
        'manual-input-demo.html', 'nation_war_update_tool.html',
        'verify-optimization.html', 'client-simulator.html'
    ];
    
    $deletedFiles = [];
    $errors = [];
    
    foreach ($testFiles as $pattern) {
        $files = glob($pattern);
        foreach ($files as $file) {
            if (file_exists($file)) {
                if (unlink($file)) {
                    $deletedFiles[] = $file;
                } else {
                    $errors[] = "无法删除: $file";
                }
            }
        }
    }
    
    // 清理测试API文件
    $testApiFiles = [
        'api/test_simple.php', 'api/simple_accounts.php', 'api/minimal-api.php',
        'api/client-compatible.php'
    ];
    
    foreach ($testApiFiles as $file) {
        if (file_exists($file)) {
            if (unlink($file)) {
                $deletedFiles[] = $file;
            } else {
                $errors[] = "无法删除: $file";
            }
        }
    }
    
    return [
        'message' => '测试文件清理完成',
        'details' => array_merge(
            ["删除了 " . count($deletedFiles) . " 个文件"],
            $errors
        )
    ];
}

// 备份配置
function backupConfig() {
    $configFiles = ['config.php', 'config/database.php'];
    $backupDir = 'backup_' . date('Y-m-d_H-i-s');

    if (!is_dir($backupDir)) {
        mkdir($backupDir, 0755, true);
    }

    $backedUp = [];
    foreach ($configFiles as $file) {
        if (file_exists($file)) {
            $backupFile = $backupDir . '/' . basename($file);
            if (copy($file, $backupFile)) {
                $backedUp[] = $file;
            }
        }
    }

    return [
        'message' => '配置文件备份完成',
        'details' => ["备份到目录: $backupDir", "备份了 " . count($backedUp) . " 个文件"]
    ];
}

// 清除数据库
function clearDatabase() {
    require_once 'config.php';
    require_once 'config/database.php';

    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        throw new Exception('数据库连接失败');
    }

    // 获取所有表
    $stmt = $db->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

    $droppedTables = [];

    // 禁用外键检查
    $db->exec("SET FOREIGN_KEY_CHECKS = 0");

    foreach ($tables as $table) {
        try {
            $db->exec("DROP TABLE IF EXISTS `$table`");
            $droppedTables[] = $table;
        } catch (Exception $e) {
            // 忽略删除错误
        }
    }

    // 重新启用外键检查
    $db->exec("SET FOREIGN_KEY_CHECKS = 1");

    return [
        'message' => '数据库清除完成',
        'details' => ["删除了 " . count($droppedTables) . " 个表"]
    ];
}

// 创建数据库结构
function createDatabase() {
    require_once 'config.php';
    require_once 'config/database.php';

    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        throw new Exception('数据库连接失败');
    }

    $sql = "
    -- 客户账号表
    CREATE TABLE customer_accounts (
        id INT PRIMARY KEY AUTO_INCREMENT,
        account_number VARCHAR(50) UNIQUE NOT NULL COMMENT '客户账号',
        account_type ENUM('normal', 'premium') DEFAULT 'normal' COMMENT '账号类型',
        duration_hours INT DEFAULT 0 COMMENT '总时长(小时)',
        remaining_hours INT DEFAULT 0 COMMENT '剩余时长(小时)',
        status ENUM('active', 'suspended', 'expired') DEFAULT 'active' COMMENT '状态',
        session_token VARCHAR(64) COMMENT '会话令牌',
        session_expires DATETIME COMMENT '会话过期时间',
        current_ip_address VARCHAR(45) COMMENT '当前IP地址',
        device_fingerprint VARCHAR(32) COMMENT '设备指纹',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_account_number (account_number),
        INDEX idx_session_token (session_token),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户账号表';

    -- 游戏账号表
    CREATE TABLE game_accounts (
        id INT PRIMARY KEY AUTO_INCREMENT,
        account VARCHAR(100) NOT NULL COMMENT '游戏账号',
        password VARCHAR(255) NOT NULL COMMENT '密码',
        account_name VARCHAR(100) COMMENT '账号名称',
        level INT DEFAULT 1 COMMENT '等级',
        vip_level INT DEFAULT 0 COMMENT 'VIP等级',
        \`rank\` VARCHAR(50) COMMENT '官阶',
        nation_war INT DEFAULT 0 COMMENT '国战将池数量',
        gender ENUM('男', '女') DEFAULT '男' COMMENT '性别',
        price DECIMAL(10,2) DEFAULT 0.00 COMMENT '价格',
        account_type ENUM('normal', 'premium') DEFAULT 'normal' COMMENT '账号类型',
        status ENUM('available', 'in_use', 'banned', 'reported') DEFAULT 'available' COMMENT '状态',
        server_name VARCHAR(100) COMMENT '服务器名称',
        description TEXT COMMENT '描述',
        skin TEXT COMMENT '皮肤信息',
        premium_generals JSON COMMENT '高级武将',
        dynamic_skins JSON COMMENT '动态皮肤',
        general_count INT DEFAULT 0 COMMENT '武将数量',
        skin_count INT DEFAULT 0 COMMENT '皮肤数量',
        epic_general_count INT DEFAULT 0 COMMENT '史诗武将数量',
        dynamic_skin_count INT DEFAULT 0 COMMENT '动态皮肤数量',
        epic_generals JSON COMMENT '史诗武将列表',
        dynamic_skin_details TEXT COMMENT '动态皮肤详情',
        general_details TEXT COMMENT '武将详情',
        skin_details TEXT COMMENT '皮肤详情',
        current_user_id INT COMMENT '当前使用用户ID',
        taken_at DATETIME COMMENT '取号时间',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_account (account),
        INDEX idx_status (status),
        INDEX idx_account_type (account_type),
        INDEX idx_current_user (current_user_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏账号表';

    -- 客户会话表
    CREATE TABLE customer_sessions (
        id INT PRIMARY KEY AUTO_INCREMENT,
        customer_id INT NOT NULL,
        session_token VARCHAR(64) NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        device_fingerprint VARCHAR(32),
        status ENUM('active', 'expired', 'kicked') DEFAULT 'active',
        expires_at DATETIME NOT NULL,
        last_activity DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_customer_id (customer_id),
        INDEX idx_session_token (session_token),
        INDEX idx_status_expires (status, expires_at),
        FOREIGN KEY (customer_id) REFERENCES customer_accounts(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户会话表';

    -- 系统配置表
    CREATE TABLE system_config (
        id INT PRIMARY KEY AUTO_INCREMENT,
        config_key VARCHAR(100) NOT NULL UNIQUE,
        config_value TEXT,
        description VARCHAR(255),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_config_key (config_key)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

    -- 管理员账号表
    CREATE TABLE admin_accounts (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(100),
        role ENUM('admin', 'operator') DEFAULT 'admin',
        status ENUM('active', 'disabled') DEFAULT 'active',
        last_login DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_username (username),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员账号表';

    -- 操作日志表
    CREATE TABLE operation_logs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_type ENUM('admin', 'customer') NOT NULL,
        user_id INT NOT NULL,
        action VARCHAR(100) NOT NULL,
        details TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_type_id (user_type, user_id),
        INDEX idx_action (action),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

    -- 搜索关键词表
    CREATE TABLE search_keywords (
        id INT PRIMARY KEY AUTO_INCREMENT,
        keyword VARCHAR(255) UNIQUE NOT NULL,
        search_count INT DEFAULT 1,
        last_searched DATETIME DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_keyword (keyword),
        INDEX idx_search_count (search_count),
        INDEX idx_last_searched (last_searched)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索关键词表';

    -- 密码修改记录表
    CREATE TABLE password_change_records (
        id INT PRIMARY KEY AUTO_INCREMENT,
        game_account_id INT NOT NULL,
        old_password VARCHAR(255) NOT NULL,
        new_password VARCHAR(255) NOT NULL,
        reason TEXT COMMENT '修改原因',
        admin_id INT COMMENT '操作管理员ID',
        status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        completed_at DATETIME,
        INDEX idx_game_account_id (game_account_id),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at),
        FOREIGN KEY (game_account_id) REFERENCES game_accounts(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='密码修改记录表';
    ";

    // 执行SQL
    $statements = explode(';', $sql);
    $createdTables = [];

    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            try {
                $db->exec($statement);
                if (preg_match('/CREATE TABLE (\w+)/', $statement, $matches)) {
                    $createdTables[] = $matches[1];
                }
            } catch (Exception $e) {
                throw new Exception("创建表失败: " . $e->getMessage());
            }
        }
    }

    return [
        'message' => '数据库结构创建完成',
        'details' => ["创建了 " . count($createdTables) . " 个表"]
    ];
}

// 初始化基础数据
function initializeData() {
    require_once 'config.php';
    require_once 'config/database.php';

    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        throw new Exception('数据库连接失败');
    }

    $details = [];

    // 插入系统配置
    $configs = [
        ['single_device_login', '0', '是否启用单设备登录限制'],
        ['system_name', '三国杀账号管理系统', '系统名称'],
        ['version', '2.0', '系统版本'],
        ['maintenance_mode', '0', '维护模式']
    ];

    $stmt = $db->prepare("INSERT INTO system_config (config_key, config_value, description) VALUES (?, ?, ?)");
    foreach ($configs as $config) {
        $stmt->execute($config);
    }
    $details[] = "插入了 " . count($configs) . " 个系统配置";

    // 创建示例游戏账号
    $gameAccounts = [
        [
            'account' => 'demo001',
            'password' => 'demo123456',
            'account_name' => '演示账号一',
            'level' => 45,
            'vip_level' => 8,
            'rank' => '王者官阶',
            'nation_war' => 35,
            'gender' => '男',
            'price' => 299.00,
            'account_type' => 'premium',
            'general_count' => 12,
            'skin_count' => 8,
            'epic_general_count' => 5,
            'dynamic_skin_count' => 3,
            'general_details' => '张飞,关羽,刘备,赵云,诸葛亮,司马懿,周瑜,孙权,曹操,貂蝉,大乔,小乔',
            'skin_details' => '貂蝉-闭月,大乔-国色,小乔-天香,甄姬-洛神,孙尚香-枭姬,花木兰-剑舞者,不知火舞-魅语,王昭君-凤凰于飞'
        ],
        [
            'account' => 'demo002',
            'password' => 'demo789012',
            'account_name' => '演示账号二',
            'level' => 38,
            'vip_level' => 6,
            'rank' => '钻石官阶',
            'nation_war' => 28,
            'gender' => '女',
            'price' => 199.00,
            'account_type' => 'normal',
            'general_count' => 10,
            'skin_count' => 6,
            'epic_general_count' => 3,
            'dynamic_skin_count' => 2,
            'general_details' => '张飞,关羽,刘备,赵云,诸葛亮,周瑜,孙权,曹操,貂蝉,大乔',
            'skin_details' => '貂蝉-闭月,大乔-国色,小乔-天香,甄姬-洛神,孙尚香-枭姬,花木兰-剑舞者'
        ],
        [
            'account' => 'demo003',
            'password' => 'demo345678',
            'account_name' => '演示账号三',
            'level' => 52,
            'vip_level' => 10,
            'rank' => '至尊官阶',
            'nation_war' => 68,
            'gender' => '男',
            'price' => 599.00,
            'account_type' => 'premium',
            'general_count' => 15,
            'skin_count' => 12,
            'epic_general_count' => 8,
            'dynamic_skin_count' => 6,
            'general_details' => '张飞,关羽,刘备,赵云,诸葛亮,司马懿,周瑜,孙权,曹操,貂蝉,大乔,小乔,甄姬,孙尚香,花木兰',
            'skin_details' => '貂蝉-闭月,大乔-国色,小乔-天香,甄姬-洛神,孙尚香-枭姬,花木兰-剑舞者,不知火舞-魅语,王昭君-凤凰于飞,安琪拉-心灵骇客,妲己-仙境爱丽丝,王者荣耀-限定,三国杀-典藏'
        ]
    ];

    $stmt = $db->prepare("
        INSERT INTO game_accounts (
            account, password, account_name, level, vip_level, `rank`,
            nation_war, gender, price, account_type, status,
            general_count, skin_count, epic_general_count, dynamic_skin_count,
            general_details, skin_details, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'available', ?, ?, ?, ?, ?, ?, NOW())
    ");

    foreach ($gameAccounts as $account) {
        $stmt->execute([
            $account['account'], $account['password'], $account['account_name'],
            $account['level'], $account['vip_level'], $account['rank'],
            $account['nation_war'], $account['gender'], $account['price'], $account['account_type'],
            $account['general_count'], $account['skin_count'],
            $account['epic_general_count'], $account['dynamic_skin_count'],
            $account['general_details'], $account['skin_details']
        ]);
    }
    $details[] = "创建了 " . count($gameAccounts) . " 个演示游戏账号";

    // 创建示例客户账号
    $customerAccounts = [
        ['demo_customer', 'normal', 100, 50],
        ['vip_customer', 'premium', 200, 150]
    ];

    $stmt = $db->prepare("
        INSERT INTO customer_accounts (account_number, account_type, duration_hours, remaining_hours, status, created_at)
        VALUES (?, ?, ?, ?, 'active', NOW())
    ");

    foreach ($customerAccounts as $customer) {
        $stmt->execute($customer);
    }
    $details[] = "创建了 " . count($customerAccounts) . " 个演示客户账号";

    return [
        'message' => '基础数据初始化完成',
        'details' => $details
    ];
}

// 创建管理员账号
function createAdminAccount() {
    require_once 'config.php';
    require_once 'config/database.php';

    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        throw new Exception('数据库连接失败');
    }

    // 创建默认管理员账号
    $username = 'admin';
    $password = password_hash('admin123', PASSWORD_DEFAULT);

    $stmt = $db->prepare("
        INSERT INTO admin_accounts (username, password, email, role, status, created_at)
        VALUES (?, ?, ?, 'admin', 'active', NOW())
    ");

    $stmt->execute([$username, $password, '<EMAIL>']);

    return [
        'message' => '管理员账号创建完成',
        'details' => [
            "用户名: admin",
            "密码: admin123",
            "请登录后立即修改密码"
        ]
    ];
}

// 设置文件权限
function setPermissions() {
    $directories = ['logs', 'backup'];
    $details = [];

    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                $details[] = "创建目录: $dir";
            }
        }

        if (is_writable($dir)) {
            $details[] = "目录 $dir 权限正常";
        } else {
            $details[] = "警告: 目录 $dir 可能没有写入权限";
        }
    }

    // 创建 .htaccess 文件保护敏感目录
    $htaccessContent = "Order Deny,Allow\nDeny from all";

    $protectedDirs = ['config', 'logs', 'backup'];
    foreach ($protectedDirs as $dir) {
        if (is_dir($dir)) {
            $htaccessFile = $dir . '/.htaccess';
            if (file_put_contents($htaccessFile, $htaccessContent)) {
                $details[] = "创建保护文件: $htaccessFile";
            }
        }
    }

    // 创建安装锁定文件
    if (file_put_contents('install.lock', date('Y-m-d H:i:s'))) {
        $details[] = "创建安装锁定文件";
    }

    return [
        'message' => '文件权限设置完成',
        'details' => $details
    ];
}

?>
