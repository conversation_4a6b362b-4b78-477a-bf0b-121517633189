// 客户取号面板JavaScript
let sessionToken = '';
let customerInfo = {};
let availableAccounts = [];
let inUseAccounts = [];
let refreshInterval = null;
let sessionCheckInterval = null;
let currentView = 'accounts'; // 'accounts' 或 'details'
let hasAccountInUse = false;
let allowAutoJumpToDetails = true; // 控制是否允许自动跳转到详情页面

/**
 * 处理API响应错误
 */
function handleApiError(result, error = null) {
    // 检查是否是会话相关的错误
    if (result && result.message && (
        result.message.includes('会话已过期') ||
        result.message.includes('会话无效') ||
        result.message.includes('未登录') ||
        result.message.includes('session') ||
        result.message.includes('登录') ||
        result.message.includes('IP地址变更') ||
        result.message.includes('其他设备登录')
    )) {
        console.log('会话验证失败，跳转到登录页面:', result.message);

        // 如果是设备冲突，显示特殊提示
        if (result.message.includes('IP地址变更') || result.message.includes('其他设备登录')) {
            showAlert('warning', '检测到您的账号在其他设备登录，当前会话已失效。请重新登录。', 5000);
        }

        logout();
        return true; // 表示已处理
    }

    // 检查HTTP状态码
    if (error && (error.message.includes('401') || error.message.includes('Unauthorized'))) {
        console.log('收到401错误，可能是会话问题');
        logout();
        return true; // 表示已处理
    }

    return false; // 表示未处理，需要显示错误消息
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeCustomerDashboard();
});

/**
 * 初始化客户面板
 */
function initializeCustomerDashboard() {
    // 检查登录状态
    sessionToken = localStorage.getItem('customer_session_token');
    const customerInfoStr = localStorage.getItem('customer_info');

    if (!sessionToken || !customerInfoStr) {
        // 未登录，跳转到登录页
        window.location.href = '/customer-login.html';
        return;
    }

    try {
        customerInfo = JSON.parse(customerInfoStr);
        displayCustomerInfo();
        loadAvailableAccounts();
        startTimeUpdates();
        startAutoRefresh();

        // 检查是否需要显示租号必读
        checkAndShowRentalTerms();
    } catch (error) {
        console.error('初始化失败:', error);
        logout();
    }
}

/**
 * 显示客户信息
 */
function displayCustomerInfo() {
    document.getElementById('customerAccount').textContent = customerInfo.account_number || '-';
    document.getElementById('totalHours').textContent = (customerInfo.duration_hours ?? customerInfo.total_hours ?? '-') ;

    // 显示账号类型
    const typeBadge = document.getElementById('accountTypeBadge');
    if (customerInfo.account_type === 'premium') {
        typeBadge.textContent = '高级客户';
        typeBadge.className = 'sgs-account-type sgs-type-premium';
    } else {
        typeBadge.textContent = '普通客户';
        typeBadge.className = 'sgs-account-type sgs-type-normal';
    }

    updateRemainingTime();
}

/**
 * 更新剩余时间
 */
function updateRemainingTime() {
    const remainingHours = parseFloat(customerInfo.remaining_hours || 0);
    document.getElementById('remainingHours').textContent = remainingHours.toFixed(1);

    // 更新倒计时显示
    updateCountdownDisplay(remainingHours);

    // 检查时间警告
    checkTimeWarning(remainingHours);
}

/**
 * 更新倒计时显示
 */
function updateCountdownDisplay(remainingHours) {
    const totalMinutes = Math.floor(remainingHours * 60);
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    const seconds = Math.floor((remainingHours * 3600) % 60);

    const countdownText = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    document.getElementById('countdownTime').textContent = countdownText;

    // 根据剩余时间改变颜色
    const countdownElement = document.getElementById('countdownTime');
    if (remainingHours <= 0.5) { // 30分钟以下
        countdownElement.style.color = '#dc3545'; // 红色
    } else if (remainingHours <= 1) { // 1小时以下
        countdownElement.style.color = '#fd7e14'; // 橙色
    } else {
        countdownElement.style.color = '#198754'; // 绿色
    }
}

/**
 * 检查时间警告
 */
function checkTimeWarning(remainingHours) {
    const warningDiv = document.getElementById('timeWarning');
    const warningMessage = document.getElementById('warningMessage');

    const remainingMinutes = remainingHours * 60;

    if (remainingMinutes <= 10) {
        warningMessage.textContent = '剩余时长不足10分钟，如需延时请联系客服';
        warningDiv.style.display = 'block';
    } else if (remainingMinutes <= 30) {
        warningMessage.textContent = '剩余时长不足30分钟，如需延时请联系客服';
        warningDiv.style.display = 'block';
    } else {
        warningDiv.style.display = 'none';
    }
}

/**
 * 加载可用账号
 */
async function loadAvailableAccounts() {
    try {
        const response = await fetch('api/customer_new.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_available_accounts',
                session_token: sessionToken
            })
        });

        const result = await response.json();

        if (result.success) {
            availableAccounts = result.data.available_accounts || [];
            inUseAccounts = result.data.in_use_accounts || [];
            hasAccountInUse = result.data.has_account_in_use || false;

            // 如果有使用中的账号且允许自动跳转，自动跳转到详情页面
            if (hasAccountInUse && inUseAccounts.length > 0 && currentView === 'accounts' && allowAutoJumpToDetails) {
                showAccountDetailsPage(inUseAccounts[0]);
            } else {
                displayAvailableAccounts();
                displayInUseAccounts();
                updateAccountCounts();
            }
        } else {
            // 使用统一的错误处理
            if (!handleApiError(result)) {
                showAlert('danger', result.message || '加载账号失败');
            }
        }
    } catch (error) {
        console.error('加载账号失败:', error);
        // 使用统一的错误处理
        if (!handleApiError(null, error)) {
            showAlert('danger', '网络错误，请稍后重试');
        }
    }
}

/**
 * 显示可用账号
 */
function displayAvailableAccounts() {
    const container = document.getElementById('availableAccountsContainer');

    if (!availableAccounts || availableAccounts.length === 0) {
        container.innerHTML = `
            <div class="sgs-empty-state">
                <div class="sgs-empty-icon">⚔️</div>
                <div class="sgs-empty-text">暂无可选账号</div>
                <div class="sgs-empty-hint">请稍后刷新或联系管理员</div>
            </div>
        `;
        return;
    }

    try {
        // 验证账号数据完整性
        const validAccounts = availableAccounts.filter(account => {
            if (!account || typeof account !== 'object') {
                console.warn('无效的账号数据:', account);
                return false;
            }

            // 检查必要字段
            const requiredFields = ['id', 'account', 'password'];
            for (const field of requiredFields) {
                if (!account[field]) {
                    console.warn(`账号缺少必要字段 ${field}:`, account);
                    return false;
                }
            }

            return true;
        });

        if (validAccounts.length === 0) {
            container.innerHTML = `
                <div class="sgs-empty-state">
                    <div class="sgs-empty-icon">⚠️</div>
                    <div class="sgs-empty-text">账号数据异常</div>
                    <div class="sgs-empty-hint">请联系管理员检查账号数据</div>
                </div>
            `;
            return;
        }

        // 按账号类型分组
        const premiumAccounts = validAccounts.filter(account => account.account_type === 'premium');
        const normalAccounts = validAccounts.filter(account => account.account_type !== 'premium');

        let html = '';

        // 高级账号区域
        if (premiumAccounts.length > 0) {
            html += `
                <div class="sgs-account-section">
                    <div class="sgs-section-header premium-section">
                        <div class="sgs-section-title">
                            <i class="bi bi-star-fill"></i>
                            高级账号区域
                        </div>
                        <div class="sgs-section-count">${premiumAccounts.length} 个账号</div>
                    </div>
                    <div class="sgs-accounts-grid">
                        ${premiumAccounts.map(account => {
                            try {
                                return createAccountCard(account, 'available');
                            } catch (error) {
                                console.error('创建高级账号卡片失败:', error, account);
                                return `<div class="sgs-account-error">账号 ${account.account || 'Unknown'} 显示异常</div>`;
                            }
                        }).join('')}
                    </div>
                </div>
            `;
        }

        // 普通账号区域
        if (normalAccounts.length > 0) {
            html += `
                <div class="sgs-account-section">
                    <div class="sgs-section-header normal-section">
                        <div class="sgs-section-title">
                            <i class="bi bi-circle-fill"></i>
                            普通账号区域
                        </div>
                        <div class="sgs-section-count">${normalAccounts.length} 个账号</div>
                    </div>
                    <div class="sgs-accounts-grid">
                        ${normalAccounts.map(account => {
                            try {
                                return createAccountCard(account, 'available');
                            } catch (error) {
                                console.error('创建普通账号卡片失败:', error, account);
                                return `<div class="sgs-account-error">账号 ${account.account || 'Unknown'} 显示异常</div>`;
                            }
                        }).join('')}
                    </div>
                </div>
            `;
        }

        container.innerHTML = html;

    } catch (error) {
        console.error('显示可用账号失败:', error);
        container.innerHTML = `
            <div class="sgs-empty-state">
                <div class="sgs-empty-icon">❌</div>
                <div class="sgs-empty-text">显示错误</div>
                <div class="sgs-empty-hint">请刷新页面重试</div>
            </div>
        `;
    }
}

/**
 * 显示使用中账号
 */
function displayInUseAccounts() {
    const container = document.getElementById('inUseAccountsContainer');

    if (!inUseAccounts || inUseAccounts.length === 0) {
        container.innerHTML = `
            <div class="sgs-empty-state">
                <div class="sgs-empty-icon">🛡️</div>
                <div class="sgs-empty-text">暂无使用中账号</div>
                <div class="sgs-empty-hint">选择一个账号开始使用</div>
            </div>
        `;
        return;
    }

    // 按账号类型分组
    const premiumAccounts = inUseAccounts.filter(account => account.account_type === 'premium');
    const normalAccounts = inUseAccounts.filter(account => account.account_type !== 'premium');

    let html = '';

    // 高级账号区域
    if (premiumAccounts.length > 0) {
        html += `
            <div class="sgs-account-section">
                <div class="sgs-section-header premium-section">
                    <div class="sgs-section-title">
                        <i class="bi bi-star-fill"></i>
                        使用中的高级账号
                    </div>
                    <div class="sgs-section-count">${premiumAccounts.length} 个账号</div>
                </div>
                <div class="sgs-accounts-grid">
                    ${premiumAccounts.map(account => createAccountCard(account, 'in-use')).join('')}
                </div>
            </div>
        `;
    }

    // 普通账号区域
    if (normalAccounts.length > 0) {
        html += `
            <div class="sgs-account-section">
                <div class="sgs-section-header normal-section">
                    <div class="sgs-section-title">
                        <i class="bi bi-circle-fill"></i>
                        使用中的普通账号
                    </div>
                    <div class="sgs-section-count">${normalAccounts.length} 个账号</div>
                </div>
                <div class="sgs-accounts-grid">
                    ${normalAccounts.map(account => createAccountCard(account, 'in-use')).join('')}
                </div>
            </div>
        `;
    }

    container.innerHTML = html;
}

/**
 * 获取VIP等级显示
 */
function getVipDisplay(vipLevel) {
    if (vipLevel === 0) return '普通用户';
    return `VIP${vipLevel}`;
}

/**
 * 获取官阶显示
 */
function getRankDisplay(rank) {
    if (!rank || rank.trim() === '') return '平民';
    return rank;
}

/**
 * 获取国战将池等级颜色
 */
function getNationWarColor(poolCount) {
    const count = parseInt(poolCount) || 0;
    if (count === 0) return '#666';           // 未参与 - 灰色
    if (count <= 10) return '#95a5a6';        // 新手将池 - 浅灰
    if (count <= 30) return '#3498db';        // 进阶将池 - 蓝色
    if (count <= 50) return '#e74c3c';        // 高级将池 - 红色
    return '#f39c12';                         // 顶级将池 - 橙色
}

/**
 * 获取国战将池等级描述
 */
function getPoolLevelDesc(poolCount) {
    const count = parseInt(poolCount) || 0;
    if (count === 0) return '未参与国战';
    if (count <= 10) return '新手将池';
    if (count <= 30) return '进阶将池';
    if (count <= 50) return '高级将池';
    return '顶级将池';
}

/**
 * 创建账号卡片
 */
function createAccountCard(account, type) {
    const isPremium = account.account_type === 'premium';
    const cardClass = type === 'in-use' ? 'sgs-account-card in-use' : (isPremium ? 'sgs-account-card premium' : 'sgs-account-card');

    // 解析武将和皮肤数据
    let premiumGenerals = [];
    let dynamicSkins = [];
    let allSkins = [];

    try {
        // 安全解析史诗武将 - 优先使用epic_generals，回退到premium_generals
        const epicGeneralsData = account.epic_generals || account.premium_generals;
        if (epicGeneralsData) {
            if (typeof epicGeneralsData === 'string') {
                // 如果是逗号分隔的字符串，先分割再处理
                if (epicGeneralsData.includes(',')) {
                    premiumGenerals = epicGeneralsData.split(/[,，、\n]/).map(s => s.trim()).filter(s => s);
                } else {
                    // 尝试作为JSON解析
                    try {
                        premiumGenerals = JSON.parse(epicGeneralsData);
                    } catch (e) {
                        // 如果JSON解析失败，作为单个项目处理
                        premiumGenerals = [epicGeneralsData.trim()].filter(s => s);
                    }
                }
            } else if (Array.isArray(epicGeneralsData)) {
                premiumGenerals = epicGeneralsData;
            }
        }

        // 安全解析动态皮肤 - 优先使用dynamic_skin_details，回退到dynamic_skins
        const dynamicSkinsData = account.dynamic_skin_details || account.dynamic_skins;
        if (dynamicSkinsData) {
            if (typeof dynamicSkinsData === 'string') {
                // 如果是逗号分隔的字符串，先分割再处理
                if (dynamicSkinsData.includes(',')) {
                    dynamicSkins = dynamicSkinsData.split(/[,，、\n]/).map(s => s.trim()).filter(s => s);
                } else {
                    // 尝试作为JSON解析
                    try {
                        dynamicSkins = JSON.parse(dynamicSkinsData);
                    } catch (e) {
                        // 如果JSON解析失败，作为单个项目处理
                        dynamicSkins = [dynamicSkinsData.trim()].filter(s => s);
                    }
                }
            } else if (Array.isArray(dynamicSkinsData)) {
                dynamicSkins = dynamicSkinsData;
            }
        }

        // 确保是数组类型
        if (!Array.isArray(premiumGenerals)) premiumGenerals = [];
        if (!Array.isArray(dynamicSkins)) dynamicSkins = [];

        // 调试日志
        console.log('账号数据解析结果:', {
            accountId: account.id,
            accountName: account.account_name,
            epicGeneralsRaw: account.epic_generals,
            premiumGeneralsRaw: account.premium_generals,
            dynamicSkinDetailsRaw: account.dynamic_skin_details,
            dynamicSkinsRaw: account.dynamic_skins,
            parsedPremiumGenerals: premiumGenerals,
            parsedDynamicSkins: dynamicSkins
        });

        // 尝试解析皮肤详情 - 优先使用skin_details，回退到skin
        const skinData = account.skin_details || account.skin;
        if (skinData && skinData.trim()) {
            allSkins = skinData.split(/[,，、\n]/).map(s => s.trim()).filter(s => s);
        }
    } catch (e) {
        // 如果解析失败，使用空数组
        console.warn('解析账号数据失败:', e);
        premiumGenerals = [];
        dynamicSkins = [];
        allSkins = [];
    }

    // 获取VIP等级显示
    const vipDisplay = getVipDisplay(account.vip_level || 0);

    // 获取官阶显示
    const rankDisplay = getRankDisplay(account.rank);

    // 获取国战将池信息
    const poolCount = parseInt(account.nation_war) || 0;
    const poolLevelDesc = getPoolLevelDesc(poolCount);
    const poolDisplayText = poolCount > 0 ? `${poolCount}个` : '未参与';

    // 计算总皮肤数量
    const totalSkins = Math.max(account.skin_count || 0, allSkins.length);

    // 计算总武将数量
    const totalGenerals = Math.max(account.general_count || 0, premiumGenerals.length);

    // 史诗武将标签 - 显示完整列表
    const generalsHtml = premiumGenerals.length > 0 ?
        `<div class="sgs-feature-section">
            <div class="sgs-feature-title">
                <i class="bi bi-star-fill"></i>史诗武将
            </div>
            <div class="sgs-feature-list">
                ${premiumGenerals.map(general =>
                    `<span class="sgs-feature-tag sgs-general-tag">${general}</span>`
                ).join('')}
            </div>
        </div>` : '';

    // 动态皮肤标签 - 显示完整列表
    const dynamicSkinsHtml = dynamicSkins.length > 0 ?
        `<div class="sgs-feature-section">
            <div class="sgs-feature-title">
                <i class="bi bi-lightning-fill"></i>动态皮肤
            </div>
            <div class="sgs-feature-list">
                ${dynamicSkins.map(skin =>
                    `<span class="sgs-feature-tag sgs-dynamic-skin-tag">${skin}</span>`
                ).join('')}
            </div>
        </div>` : '';

    // 普通皮肤标签（排除动态皮肤）- 显示完整列表
    const normalSkins = allSkins.filter(skin => !dynamicSkins.includes(skin));
    const normalSkinsHtml = normalSkins.length > 0 ?
        `<div class="sgs-feature-section">
            <div class="sgs-feature-title">
                <i class="bi bi-palette-fill"></i>皮肤收藏
            </div>
            <div class="sgs-feature-list">
                ${normalSkins.map(skin =>
                    `<span class="sgs-feature-tag sgs-skin-tag">${skin}</span>`
                ).join('')}
            </div>
        </div>` : '';

    // 操作按钮
    let actionButtons = '';
    if (type === 'available') {
        if (hasAccountInUse) {
            if (account.can_take) {
                actionButtons = `
                    <button class="sgs-btn sgs-btn-warning" onclick="switchAccount('${account.id}')">
                        <i class="bi bi-arrow-repeat"></i>切换账号
                    </button>`;
            } else {
                actionButtons = `
                    <button class="sgs-btn" disabled title="普通客户无法使用高级账号" style="opacity: 0.5;">
                        <i class="bi bi-lock"></i>权限不足
                    </button>`;
            }
        } else {
            if (account.can_take) {
                actionButtons = `
                    <button class="sgs-btn sgs-btn-primary" onclick="takeAccount('${account.id}')">
                        <i class="bi bi-person-check"></i>选择此账号
                    </button>`;
            } else {
                actionButtons = `
                    <button class="sgs-btn" disabled title="普通客户无法使用高级账号" style="opacity: 0.5;">
                        <i class="bi bi-lock"></i>权限不足
                    </button>`;
            }
        }
    } else {
        if (String(account.current_user_id) === String(customerInfo.id)) {
            actionButtons = `
                <button class="sgs-btn sgs-btn-success" onclick="releaseAccount('${account.id}')">
                    <i class="bi bi-person-x"></i>释放账号
                </button>`;
        } else {
            actionButtons = `
                <button class="sgs-btn" disabled title="该账号被其他玩家占用" style="opacity: 0.5;">
                    <i class="bi bi-person-x"></i>已被占用
                </button>`;
        }
    }

    return `
        <div class="${cardClass}">
            <!-- 账号类型标识 -->
            <div class="sgs-account-type-header">
                <div class="sgs-account-type-badge ${isPremium ? 'premium-type' : 'normal-type'}">
                    <i class="bi bi-${isPremium ? 'star-fill' : 'circle-fill'}"></i>
                    ${isPremium ? '高级账号区域' : '普通账号区域'}
                </div>
                ${type === 'in-use' ? '<div class="sgs-status-badge sgs-status-in-use">使用中</div>' :
                  '<div class="sgs-status-badge sgs-status-available">可使用</div>'}
            </div>

            <!-- 武将头部信息 -->
            <div class="sgs-account-header">
                <div class="sgs-account-title">
                    <div class="sgs-level-badge">Lv.${account.level || 0}</div>
                    <div>
                        <h4 class="sgs-account-name">${account.account_name || '神秘武将'}</h4>
                        <div class="sgs-badges">
                            <span class="sgs-badge sgs-badge-vip">${vipDisplay}</span>
                            ${account.rank ? `<span class="sgs-badge sgs-badge-rank">${rankDisplay}</span>` : ''}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 武将属性统计 -->
            <div class="sgs-stats-grid">
                <div class="sgs-stat-item">
                    <div class="sgs-stat-icon">⚔️</div>
                    <div class="sgs-stat-value" style="color: ${getNationWarColor(poolCount)}">${poolDisplayText}</div>
                    <div class="sgs-stat-label">国战将池</div>
                </div>
                <div class="sgs-stat-item">
                    <div class="sgs-stat-icon">👥</div>
                    <div class="sgs-stat-value">${totalGenerals}</div>
                    <div class="sgs-stat-label">武将数量</div>
                </div>
                <div class="sgs-stat-item">
                    <div class="sgs-stat-icon">⭐</div>
                    <div class="sgs-stat-value" style="color: #ffd700">${account.epic_general_count || premiumGenerals.length}</div>
                    <div class="sgs-stat-label">史诗武将</div>
                </div>
                <div class="sgs-stat-item">
                    <div class="sgs-stat-icon">🎨</div>
                    <div class="sgs-stat-value">${totalSkins}</div>
                    <div class="sgs-stat-label">皮肤数量</div>
                </div>
                <div class="sgs-stat-item">
                    <div class="sgs-stat-icon">⚡</div>
                    <div class="sgs-stat-value" style="color: #ffd700">${account.dynamic_skin_count || dynamicSkins.length}</div>
                    <div class="sgs-stat-label">动态皮肤</div>
                </div>
            </div>

            <!-- 账号特色展示 -->
            <div class="sgs-features">
                ${generalsHtml}
                ${dynamicSkinsHtml}
                ${normalSkinsHtml}
            </div>

            <!-- 操作按钮 -->
            <div class="sgs-actions">
                ${actionButtons}
            </div>
        </div>
    `;
}

/**
 * 取用账号
 */
async function takeAccount(accountId) {
    try {
        const response = await fetch('api/customer_new.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'take_account',
                session_token: sessionToken,
                game_account_id: accountId
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('success', '取号成功！');

            // 更新本地状态
            hasAccountInUse = true;
            inUseAccounts = [result.data.game_account];

            // 从可用账号列表中移除已取用的账号
            availableAccounts = availableAccounts.filter(account => account.id !== accountId);

            // 刷新界面显示
            displayAvailableAccounts();
            displayInUseAccounts();
            updateAccountCounts();

            // 重新启用自动跳转
            allowAutoJumpToDetails = true;

            // 直接跳转到账号详情页面
            showAccountDetailsPage(result.data.game_account);
        } else {
            // 使用统一的错误处理
            if (!handleApiError(result)) {
                showAlert('danger', result.message || '取号失败');
            }
        }
    } catch (error) {
        console.error('取号失败:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

/**
 * 切换账号
 */
async function switchAccount(accountId) {
    if (!confirm('确定要切换到此账号吗？原账号将被释放并提交密码修改请求。')) {
        return;
    }

    try {
        const response = await fetch('api/customer_new.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'take_account',
                session_token: sessionToken,
                game_account_id: accountId,
                is_switch: true
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('success', '切换成功！原账号已提交密码修改请求。');
            // 直接跳转到新账号详情页面
            showAccountDetailsPage(result.data.game_account);
        } else {
            showAlert('danger', result.message || '切换失败');
        }
    } catch (error) {
        console.error('切换失败:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

/**
 * 释放账号
 */
async function releaseAccount(accountId) {
    if (!confirm('确定要释放此游戏账号吗？')) {
        return;
    }

    try {
        const response = await fetch('api/customer_new.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'release_account',
                session_token: sessionToken,
                game_account_id: accountId
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('success', '释放成功！');

            // 更新本地状态
            hasAccountInUse = false;

            // 将释放的账号从使用中移动到可用列表
            const releasedAccount = inUseAccounts.find(account => account.id === accountId);
            if (releasedAccount) {
                // 更新账号状态
                releasedAccount.status = 'available';
                releasedAccount.current_user_id = null;
                releasedAccount.taken_at = null;

                // 移动到可用列表
                availableAccounts.unshift(releasedAccount);
                inUseAccounts = inUseAccounts.filter(account => account.id !== accountId);
            }

            // 刷新界面显示
            displayAvailableAccounts();
            displayInUseAccounts();
            updateAccountCounts();

            // 重新加载账号列表以确保数据同步
            loadAvailableAccounts();
        } else {
            // 使用统一的错误处理
            if (!handleApiError(result)) {
                showAlert('danger', result.message || '释放失败');
            }
        }
    } catch (error) {
        console.error('释放失败:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

/**
 * 显示账号详情页面
 */
function showAccountDetailsPage(account) {
    currentView = 'details';

    // 隐藏搜索区域和账号列表，显示详情页面
    const searchPanel = document.querySelector('.sgs-search-panel');
    if (searchPanel) {
        searchPanel.style.display = 'none';
    }
    document.getElementById('accountsSection').style.display = 'none';
    document.getElementById('accountDetailsSection').style.display = 'block';

    // 填充详情内容
    displayAccountDetailsContent(account);

    // 停止自动刷新账号列表
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
    }
}

/**
 * 返回账号列表
 */
function backToAccountList() {
    currentView = 'accounts';

    // 禁用自动跳转到详情页面
    allowAutoJumpToDetails = false;

    // 重新显示搜索区域和账号列表，隐藏详情页面
    const searchPanel = document.querySelector('.sgs-search-panel');
    if (searchPanel) {
        searchPanel.style.display = 'block';
    }
    document.getElementById('accountsSection').style.display = 'block';
    document.getElementById('accountDetailsSection').style.display = 'none';

    // 重新加载账号列表
    loadAvailableAccounts();

    // 重新开始自动刷新
    startAutoRefresh();

    // 显示提示信息
    showAlert('info', '已返回选号界面，您可以选择其他账号');

    // 5秒后重新启用自动跳转（避免用户操作被干扰）
    setTimeout(() => {
        allowAutoJumpToDetails = true;
    }, 5000);
}

/**
 * 显示账号详情内容
 */
function displayAccountDetailsContent(account) {
    const content = document.getElementById('accountDetailsContent');

    // 安全解析数据
    let premiumGenerals = [];
    let dynamicSkins = [];
    let allSkins = [];

    try {
        // 安全解析史诗武将 - 优先使用epic_generals，回退到premium_generals
        const epicGeneralsData = account.epic_generals || account.premium_generals;
        if (epicGeneralsData) {
            if (typeof epicGeneralsData === 'string') {
                // 如果是逗号分隔的字符串，先分割再处理
                if (epicGeneralsData.includes(',')) {
                    premiumGenerals = epicGeneralsData.split(/[,，、\n]/).map(s => s.trim()).filter(s => s);
                } else {
                    // 尝试作为JSON解析
                    try {
                        premiumGenerals = JSON.parse(epicGeneralsData);
                    } catch (e) {
                        // 如果JSON解析失败，作为单个项目处理
                        premiumGenerals = [epicGeneralsData.trim()].filter(s => s);
                    }
                }
            } else if (Array.isArray(epicGeneralsData)) {
                premiumGenerals = epicGeneralsData;
            }
        }

        // 安全解析动态皮肤 - 优先使用dynamic_skin_details，回退到dynamic_skins
        const dynamicSkinsData = account.dynamic_skin_details || account.dynamic_skins;
        if (dynamicSkinsData) {
            if (typeof dynamicSkinsData === 'string') {
                // 如果是逗号分隔的字符串，先分割再处理
                if (dynamicSkinsData.includes(',')) {
                    dynamicSkins = dynamicSkinsData.split(/[,，、\n]/).map(s => s.trim()).filter(s => s);
                } else {
                    // 尝试作为JSON解析
                    try {
                        dynamicSkins = JSON.parse(dynamicSkinsData);
                    } catch (e) {
                        // 如果JSON解析失败，作为单个项目处理
                        dynamicSkins = [dynamicSkinsData.trim()].filter(s => s);
                    }
                }
            } else if (Array.isArray(dynamicSkinsData)) {
                dynamicSkins = dynamicSkinsData;
            }
        }

        // 确保是数组类型
        if (!Array.isArray(premiumGenerals)) premiumGenerals = [];
        if (!Array.isArray(dynamicSkins)) dynamicSkins = [];

        // 尝试解析皮肤详情 - 优先使用skin_details，回退到skin
        const skinData = account.skin_details || account.skin;
        if (skinData && skinData.trim()) {
            allSkins = skinData.split(/[,，、\n]/).map(s => s.trim()).filter(s => s);
        }
    } catch (e) {
        // 如果解析失败，使用空数组
        console.warn('解析账号详情数据失败:', e);
        premiumGenerals = [];
        dynamicSkins = [];
        allSkins = [];
    }

    const normalSkins = allSkins.filter(skin => !dynamicSkins.includes(skin));

    content.innerHTML = `
        <div class="sgs-details-content">
            <!-- 账号头部信息 -->
            <div style="background: linear-gradient(135deg, rgba(0, 255, 255, 0.2), rgba(255, 0, 128, 0.2)); border: 2px solid var(--cyber-cyan); border-radius: 12px; padding: 20px; margin-bottom: 25px; text-align: center;">
                <h2 style="color: var(--cyber-cyan); margin-bottom: 15px; text-shadow: 0 0 10px var(--cyber-cyan-glow);">
                    <i class="bi bi-person-badge-fill me-2"></i>${account.account_name || '神秘账号'}
                </h2>
                <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                    <span style="background: var(--cyber-cyan); color: var(--bg-primary); padding: 8px 16px; border-radius: 20px; font-weight: 600; font-size: 1.1rem;">
                        Lv.${account.level || 0}
                    </span>
                    <span style="background: var(--cyber-pink); color: var(--text-primary); padding: 8px 16px; border-radius: 20px; font-weight: 600; font-size: 1.1rem;">
                        ${getVipDisplay(account.vip_level || 0)}
                    </span>
                    ${account.rank ? `<span style="background: var(--cyber-purple); color: var(--text-primary); padding: 8px 16px; border-radius: 20px; font-weight: 600; font-size: 1.1rem;">
                        ${getRankDisplay(account.rank)}
                    </span>` : ''}
                    <span style="background: ${getNationWarColor(account.nation_war || 0)}; color: var(--text-primary); padding: 8px 16px; border-radius: 20px; font-weight: 600; font-size: 1.1rem;">
                        ${getPoolLevelDesc(account.nation_war || 0)} (${parseInt(account.nation_war) || 0}个)
                    </span>
                </div>
            </div>

            <!-- 账号统计信息 -->
            <div class="sgs-stats-container" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(60px, 1fr)); gap: 2px; margin-bottom: 4px;">
                <div style="background: rgba(0, 255, 255, 0.1); border: 1px solid var(--cyber-cyan); border-radius: 3px; padding: 3px; text-align: center;">
                    <div style="font-size: 0.8rem; color: var(--cyber-cyan); margin-bottom: 1px;">👥</div>
                    <div style="font-size: 0.6rem; font-weight: 700; color: var(--cyber-cyan); margin-bottom: 1px;">${account.general_count || 0}</div>
                    <div style="color: var(--text-secondary); font-size: 0.4rem; font-weight: 600;">武将总数</div>
                </div>
                <div style="background: rgba(255, 0, 128, 0.1); border: 1px solid var(--cyber-pink); border-radius: 3px; padding: 3px; text-align: center;">
                    <div style="font-size: 0.8rem; color: var(--cyber-pink); margin-bottom: 1px;">🎨</div>
                    <div style="font-size: 0.6rem; font-weight: 700; color: var(--cyber-pink); margin-bottom: 1px;">${account.skin_count || 0}</div>
                    <div style="color: var(--text-secondary); font-size: 0.4rem; font-weight: 600;">皮肤总数</div>
                </div>
                <div style="background: rgba(138, 43, 226, 0.1); border: 1px solid var(--cyber-purple); border-radius: 3px; padding: 3px; text-align: center;">
                    <div style="font-size: 0.8rem; color: var(--cyber-purple); margin-bottom: 1px;">⚡</div>
                    <div style="font-size: 0.6rem; font-weight: 700; color: var(--cyber-purple); margin-bottom: 1px;">${dynamicSkins.length}</div>
                    <div style="color: var(--text-secondary); font-size: 0.4rem; font-weight: 600;">动态皮肤</div>
                </div>
                <div style="background: rgba(255, 215, 0, 0.1); border: 1px solid #ffd700; border-radius: 3px; padding: 3px; text-align: center;">
                    <div style="font-size: 0.8rem; color: #ffd700; margin-bottom: 1px;">⭐</div>
                    <div style="font-size: 0.6rem; font-weight: 700; color: #ffd700; margin-bottom: 1px;">${premiumGenerals.length}</div>
                    <div style="color: var(--text-secondary); font-size: 0.4rem; font-weight: 600;">史诗武将</div>
                </div>
            </div>

            <!-- 登录信息 -->
            <h3><i class="bi bi-key-fill me-2"></i>登录信息</h3>
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="sgs-details-item">
                        <div class="sgs-details-item-label">游戏账号</div>
                        <div class="sgs-details-item-value" style="display: flex; align-items: center; gap: 10px;">
                            <span style="flex: 1; background: rgba(0, 255, 255, 0.2); padding: 8px 12px; border-radius: 4px; color: var(--text-primary); font-family: 'Share Tech Mono', monospace;">${account.account}</span>
                            <button class="sgs-btn sgs-btn-outline" data-copy="${escapeHtml(account.account)}" onclick="copyFromButton(this, '账号')" style="padding: 6px 12px;">
                                <i class="bi bi-clipboard"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="sgs-details-item">
                        <div class="sgs-details-item-label">游戏密码</div>
                        <div class="sgs-details-item-value" style="display: flex; align-items: center; gap: 10px;">
                            <span style="flex: 1; background: rgba(0, 255, 255, 0.2); padding: 8px 12px; border-radius: 4px; color: var(--text-primary); font-family: 'Share Tech Mono', monospace;">${account.password}</span>
                            <button class="sgs-btn sgs-btn-outline" data-copy="${escapeHtml(account.password)}" onclick="copyFromButton(this, '密码')" style="padding: 6px 12px;">
                                <i class="bi bi-clipboard"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div style="background: rgba(0, 255, 255, 0.1); border: 2px solid var(--cyber-cyan); border-radius: 8px; padding: 12px; margin-bottom: 20px;">
                <i class="bi bi-info-circle me-2" style="color: var(--cyber-cyan);"></i>
                <span style="color: var(--text-primary);">点击复制按钮可将账号密码复制到剪贴板</span>
            </div>

            <!-- 史诗武将收藏 -->
            ${premiumGenerals.length > 0 ? `
            <div style="margin-bottom: 25px;">
                <h3 style="color: #ffd700; margin-bottom: 15px; text-shadow: 0 0 10px #ffd700;">
                    <i class="bi bi-star-fill me-2"></i>史诗武将收藏 (${premiumGenerals.length}个)
                </h3>
                <div style="background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 140, 0, 0.1)); border: 1px solid #ffd700; border-radius: 3px; padding: 3px;">
                    <div class="sgs-tag-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(35px, 1fr)); gap: 1px;">
                        ${premiumGenerals.map(general =>
                            `<div class="sgs-tag sgs-general-tag" style="background: linear-gradient(135deg, #ffd700, #ffb347); color: #000; padding: 1px; border-radius: 1px; text-align: center; font-weight: 600; font-size: 0.4rem; box-shadow: 0 1px 2px rgba(255, 215, 0, 0.3); border: 1px solid #ffb347; line-height: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                <i class="bi bi-gem" style="font-size: 0.35rem;"></i>${general}
                            </div>`
                        ).join('')}
                    </div>
                </div>
            </div>
            ` : ''}

            <!-- 动态皮肤收藏 -->
            ${dynamicSkins.length > 0 ? `
            <div style="margin-bottom: 25px;">
                <h3 style="color: var(--cyber-purple); margin-bottom: 15px; text-shadow: 0 0 10px var(--cyber-purple);">
                    <i class="bi bi-lightning-fill me-2"></i>动态皮肤收藏 (${dynamicSkins.length}个)
                </h3>
                <div style="background: linear-gradient(135deg, rgba(138, 43, 226, 0.2), rgba(75, 0, 130, 0.1)); border: 1px solid var(--cyber-purple); border-radius: 3px; padding: 3px;">
                    <div class="sgs-tag-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(35px, 1fr)); gap: 1px;">
                        ${dynamicSkins.map(skin =>
                            `<div class="sgs-tag sgs-dynamic-skin-tag" style="background: linear-gradient(135deg, var(--cyber-purple), #9932cc); color: var(--text-primary); padding: 1px; border-radius: 1px; text-align: center; font-weight: 600; font-size: 0.4rem; box-shadow: 0 1px 2px rgba(138, 43, 226, 0.3); border: 1px solid #9932cc; line-height: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                <i class="bi bi-lightning" style="font-size: 0.35rem;"></i>${skin}
                            </div>`
                        ).join('')}
                    </div>
                </div>
            </div>
            ` : ''}

            <!-- 普通皮肤收藏 -->
            ${normalSkins.length > 0 ? `
            <div style="margin-bottom: 25px;">
                <h3 style="color: var(--cyber-green); margin-bottom: 15px; text-shadow: 0 0 10px var(--cyber-green);">
                    <i class="bi bi-palette-fill me-2"></i>普通皮肤收藏 (${normalSkins.length}个)
                </h3>
                <div style="background: linear-gradient(135deg, rgba(0, 255, 65, 0.2), rgba(34, 139, 34, 0.1)); border: 1px solid var(--cyber-green); border-radius: 3px; padding: 3px;">
                    <div class="sgs-tag-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(35px, 1fr)); gap: 1px;">
                        ${normalSkins.map(skin =>
                            `<div class="sgs-tag sgs-normal-skin-tag" style="background: linear-gradient(135deg, var(--cyber-green), #32cd32); color: var(--bg-primary); padding: 1px; border-radius: 1px; text-align: center; font-weight: 600; font-size: 0.4rem; box-shadow: 0 1px 2px rgba(0, 255, 65, 0.3); border: 1px solid #32cd32; line-height: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                <i class="bi bi-palette" style="font-size: 0.35rem;"></i>${skin}
                            </div>`
                        ).join('')}
                    </div>
                </div>
            </div>
            ` : ''}

            <!-- 操作按钮 -->
            <div class="text-center" style="margin-top: 30px;">
                <div style="background: rgba(0, 255, 255, 0.1); border: 2px solid var(--cyber-cyan); border-radius: 8px; padding: 20px;">
                    <button class="sgs-btn sgs-btn-warning me-3" onclick="backToAccountList()" style="font-size: 1.1rem; padding: 12px 32px;">
                        <i class="bi bi-arrow-repeat me-2"></i>更换账号
                    </button>
                    <button class="sgs-btn sgs-btn-success" onclick="releaseCurrentAccount()" style="font-size: 1.1rem; padding: 12px 32px;">
                        <i class="bi bi-shield-x me-2"></i>释放账号
                    </button>
                </div>
            </div>
        </div>
    `;
}

/**
 * 释放当前账号
 */
async function releaseCurrentAccount() {
    if (!confirm('确定要释放当前账号吗？')) {
        return;
    }

    try {
        // 获取当前账号信息
        const response = await fetch('api/customer_new.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_current_account',
                session_token: sessionToken
            })
        });

        const result = await response.json();

        if (result.success) {
            const accountId = result.data.game_account.id;
            await releaseAccount(accountId);
            // 释放成功后返回账号列表
            backToAccountList();
        } else {
            showAlert('danger', '获取当前账号信息失败');
        }
    } catch (error) {
        console.error('释放账号失败:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

/**
 * 显示账号详情（保留原有的模态框功能）
 */
async function showAccountDetails(accountId) {
    try {
        const response = await fetch('api/customer_new.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_account_details',
                session_token: sessionToken,
                game_account_id: accountId
            })
        });

        const result = await response.json();

        if (result.success) {
            const account = result.data.game_account;
            displayAccountDetailsModal(account);
        } else {
            showAlert('danger', result.message || '获取账号详情失败');
        }
    } catch (error) {
        console.error('获取账号详情失败:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

/**
 * 显示账号详情模态框
 */
function displayAccountDetailsModal(account) {
    const content = document.getElementById('modalAccountDetailsContent');

    const premiumGenerals = account.premium_generals || [];
    const dynamicSkins = account.dynamic_skins || [];

    content.innerHTML = `
        <div class="row">
            <div class="col-lg-6 col-md-12 col-12">
                <h6 style="color: var(--cyber-cyan); font-weight: 600; margin-bottom: 15px;">
                    <i class="bi bi-info-circle me-2"></i>基本信息
                </h6>
                <div class="card" style="background: rgba(0, 255, 255, 0.1); border: 1px solid var(--cyber-cyan); border-radius: 10px;">
                    <div class="card-body">
                        <div class="mb-2"><strong style="color: var(--text-primary);">昵称：</strong><span style="color: var(--text-secondary); word-break: break-all;">${account.account_name || '未设置'}</span></div>
                        <div class="mb-2"><strong style="color: var(--text-primary);">等级：</strong><span style="color: var(--cyber-cyan); font-weight: 600;">Lv.${account.level}</span></div>
                        <div class="mb-2"><strong style="color: var(--text-primary);">VIP等级：</strong><span style="color: var(--cyber-yellow); font-weight: 600;">VIP${account.vip_level}</span></div>
                        <div class="mb-2"><strong style="color: var(--text-primary);">段位：</strong><span style="color: var(--text-secondary); word-break: break-all;">${account.rank || '未设置'}</span></div>
                        <div class="mb-2"><strong style="color: var(--text-primary);">国战将池：</strong><span style="color: ${getNationWarColor(account.nation_war || 0)}; font-weight: 600;">${getPoolLevelDesc(account.nation_war || 0)} (${parseInt(account.nation_war) || 0}个)</span></div>
                        <div class="mb-2"><strong style="color: var(--text-primary);">性别：</strong><span style="color: var(--text-secondary);">${account.gender === 'male' ? '男' : '女'}</span></div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-12 col-12">
                <h6 style="color: var(--cyber-cyan); font-weight: 600; margin-bottom: 15px;">
                    <i class="bi bi-star me-2"></i>账号价值
                </h6>
                <div class="card" style="background: rgba(0, 255, 255, 0.1); border: 1px solid var(--cyber-cyan); border-radius: 10px;">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <div style="color: var(--cyber-yellow); font-weight: 700; font-size: 2.5rem; text-shadow: 0 0 10px var(--cyber-yellow);">¥${account.price}</div>
                            <div style="color: var(--text-secondary); font-size: 0.9rem;">账号估值</div>
                        </div>
                        <div class="mb-2">
                            <span class="badge" style="background: ${account.account_type === 'premium' ? 'linear-gradient(135deg, #ff6b35, #f7931e)' : 'linear-gradient(135deg, #667eea, #764ba2)'}; padding: 8px 15px; font-size: 12px; border-radius: 20px;">
                                ${account.account_type === 'premium' ? '高级账号' : '普通账号'}
                            </span>
                        </div>
                        <div style="color: var(--text-secondary); font-size: 0.8rem; word-break: break-all;">
                            取用时间：${formatDateTime(account.taken_at)}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-6 col-12">
                <h6 style="color: var(--cyber-cyan); font-weight: 600; margin-bottom: 15px;">
                    <i class="bi bi-people me-2"></i>拥有武将 (${premiumGenerals.length}个)
                </h6>
                <div class="generals-list" style="max-height: 200px; overflow-y: auto; padding: 10px; background: rgba(0, 255, 255, 0.05); border-radius: 8px; border: 1px solid rgba(0, 255, 255, 0.3);">
                    ${premiumGenerals.length > 0 ? premiumGenerals.map(general =>
                        `<span class="general-tag premium" style="display: inline-block; margin: 3px; padding: 5px 10px; background: linear-gradient(135deg, #ff6b35, #f7931e); color: white; border-radius: 15px; font-size: 12px; font-weight: 600; box-shadow: 0 2px 4px rgba(0,0,0,0.3); word-break: keep-all; white-space: nowrap;">${general}</span>`
                    ).join('') : '<p style="color: var(--text-secondary); margin: 0; text-align: center; padding: 20px;">暂无高级武将</p>'}
                </div>
            </div>
            <div class="col-md-6 col-12">
                <h6 style="color: var(--cyber-cyan); font-weight: 600; margin-bottom: 15px;">
                    <i class="bi bi-palette me-2"></i>拥有皮肤 (${dynamicSkins.length}个)
                </h6>
                <div class="skins-list" style="max-height: 200px; overflow-y: auto; padding: 10px; background: rgba(0, 255, 255, 0.05); border-radius: 8px; border: 1px solid rgba(0, 255, 255, 0.3);">
                    ${dynamicSkins.length > 0 ? dynamicSkins.map(skin =>
                        `<span class="skin-tag dynamic" style="display: inline-block; margin: 3px; padding: 5px 10px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border-radius: 15px; font-size: 12px; font-weight: 600; box-shadow: 0 2px 4px rgba(0,0,0,0.3); word-break: keep-all; white-space: nowrap;">${skin}</span>`
                    ).join('') : '<p style="color: var(--text-secondary); margin: 0; text-align: center; padding: 20px;">暂无动态皮肤</p>'}
                </div>
            </div>
        </div>


    `;

    new bootstrap.Modal(document.getElementById('accountDetailsModal')).show();
}

/**
 * 搜索账号
 */
async function searchAccounts() {
    const searchTerm = document.getElementById('searchInput').value.trim();
    const accountType = document.getElementById('accountTypeFilter').value;

    if (!searchTerm && !accountType) {
        loadAvailableAccounts();
        return;
    }

    try {
        const response = await fetch('api/customer_new.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'search_accounts',
                session_token: sessionToken,
                search_term: searchTerm,
                account_type: accountType
            })
        });

        const result = await response.json();

        if (result.success) {
            availableAccounts = result.data.accounts || [];
            inUseAccounts = []; // 搜索结果只显示可用账号

            displayAvailableAccounts();
            displayInUseAccounts();
            updateAccountCounts();

            const total = (result.data.total_found ?? result.data.total ?? (result.data.accounts ? result.data.accounts.length : 0) ?? 0);
            showAlert('success', `找到 ${total} 个匹配的账号`);
        } else {
            showAlert('danger', result.message || '搜索失败');
        }
    } catch (error) {
        console.error('搜索失败:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

/**
 * 更新账号数量显示
 */
function updateAccountCounts() {
    document.getElementById('availableCount').textContent = availableAccounts.length;
    document.getElementById('inUseCount').textContent = inUseAccounts.length;
}

/**
 * 开始时间更新
 */
function startTimeUpdates() {
    // 基于首次登录时间和总时长计算剩余时间
    const durationHours = parseFloat(customerInfo.duration_hours || 0);
    const firstLoginTime = customerInfo.first_login_time;

    // 每秒更新倒计时
    setInterval(() => {
        let currentRemainingHours = 0;

        if (firstLoginTime) {
            // 如果有首次登录时间，基于首次登录时间计算
            const firstLogin = new Date(firstLoginTime);
            const now = new Date();
            const elapsedHours = (now.getTime() - firstLogin.getTime()) / (1000 * 60 * 60);
            currentRemainingHours = Math.max(0, durationHours - elapsedHours);
        } else {
            // 如果没有首次登录时间，使用remaining_hours
            currentRemainingHours = parseFloat(customerInfo.remaining_hours || 0);
        }

        // 更新客户信息中的剩余时间
        customerInfo.remaining_hours = currentRemainingHours;

        // 更新显示
        updateRemainingTime();

        // 如果时间用完，自动登出
        if (currentRemainingHours <= 0) {
            showAlert('warning', '时间已用完，即将自动退出');
            setTimeout(() => {
                logout();
            }, 3000);
        }
    }, 1000);
}

/**
 * 开始自动刷新
 */
function startAutoRefresh() {
    // 每30秒刷新一次账号列表
    refreshInterval = setInterval(() => {
        loadAvailableAccounts();
    }, 30000);

    // 每60秒检查一次会话状态
    sessionCheckInterval = setInterval(() => {
        checkSessionStatus();
    }, 60000);
}

/**
 * 检查会话状态
 */
async function checkSessionStatus() {
    try {
        const response = await fetch('api/customer_new.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'check_session',
                session_token: sessionToken
            })
        });

        const result = await response.json();

        if (!result.success) {
            // 会话无效，处理错误
            handleApiError(result);
        }
    } catch (error) {
        console.error('会话检查失败:', error);
        // 网络错误时不强制登出，避免误判
    }
}

/**
 * 刷新数据
 */
function refreshData() {
    loadAvailableAccounts();
    showAlert('success', '数据已刷新');
}

/**
 * 退出登录
 */
async function logout() {
    try {
        // 通知服务器登出
        await fetch('api/customer_new.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'logout',
                session_token: sessionToken
            })
        });
    } catch (error) {
        console.error('登出请求失败:', error);
    }

    // 清除本地存储
    localStorage.removeItem('customer_session_token');
    localStorage.removeItem('customer_info');

    // 清除定时器
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
    if (sessionCheckInterval) {
        clearInterval(sessionCheckInterval);
    }

    // 跳转到登录页
    window.location.href = '/customer-login.html';
}

/**
 * 万能复制功能 - 确保在任何设备上都能成功复制
 */
function copyToClipboard(text, type = '') {
    console.log(`尝试复制${type}:`, text);

    // 立即显示复制对话框，确保100%成功
    showUniversalCopyDialog(text, type);
}

/**
 * 桌面端复制方案
 */
function copyToClipboardDesktop(text, type = '') {
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
            showAlert('success', `${type}已复制到剪贴板`);
        }).catch(() => {
            fallbackCopy(text, type);
        });
    } else {
        fallbackCopy(text, type);
    }
}

/**
 * 移动端复制方案
 */
function copyToClipboardMobile(text, type = '') {
    try {
        // 方案1: 尝试现代API
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text).then(() => {
                showAlert('success', `${type}已复制到剪贴板`);
            }).catch(() => {
                // 如果现代API失败，使用移动端优化的降级方案
                mobileFallbackCopy(text, type);
            });
        } else {
            // 直接使用移动端优化的降级方案
            mobileFallbackCopy(text, type);
        }
    } catch (error) {
        console.error('Mobile copy error:', error);
        mobileFallbackCopy(text, type);
    }
}

/**
 * 移动端优化的降级复制方案
 */
function mobileFallbackCopy(text, type = '') {
    try {
        // 创建一个可见的文本区域，便于移动端选择
        const textArea = document.createElement('textarea');
        textArea.value = text;

        // 移动端优化样式
        textArea.style.position = 'fixed';
        textArea.style.top = '50%';
        textArea.style.left = '50%';
        textArea.style.transform = 'translate(-50%, -50%)';
        textArea.style.width = '80%';
        textArea.style.height = '100px';
        textArea.style.padding = '10px';
        textArea.style.fontSize = '16px'; // 防止iOS缩放
        textArea.style.border = '2px solid #00ffff';
        textArea.style.borderRadius = '8px';
        textArea.style.background = 'rgba(0, 0, 0, 0.9)';
        textArea.style.color = '#00ffff';
        textArea.style.zIndex = '10000';
        textArea.style.fontFamily = 'monospace';
        textArea.readOnly = true;

        document.body.appendChild(textArea);

        // 选择文本
        textArea.focus();
        textArea.select();
        textArea.setSelectionRange(0, text.length);

        // 尝试复制
        let copySuccess = false;
        try {
            copySuccess = document.execCommand('copy');
        } catch (err) {
            console.error('execCommand copy failed:', err);
        }

        if (copySuccess) {
            showAlert('success', `${type}已复制到剪贴板`);
            document.body.removeChild(textArea);
        } else {
            // 如果自动复制失败，显示手动复制提示
            showMobileCopyDialog(textArea, text, type);
        }
    } catch (error) {
        console.error('Mobile fallback copy error:', error);
        // 最后的降级方案：显示文本让用户手动复制
        showManualCopyDialog(text, type);
    }
}

/**
 * 传统降级复制方案
 */
function fallbackCopy(text, type = '') {
    try {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
            showAlert('success', `${type}已复制到剪贴板`);
        } else {
            showManualCopyDialog(text, type);
        }
    } catch (err) {
        console.error('Fallback copy failed:', err);
        showManualCopyDialog(text, type);
    }
}

/**
 * 显示移动端复制对话框
 */
function showMobileCopyDialog(textArea, text, type) {
    // 创建提示覆盖层
    const overlay = document.createElement('div');
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.background = 'rgba(0, 0, 0, 0.8)';
    overlay.style.zIndex = '9999';
    overlay.style.display = 'flex';
    overlay.style.flexDirection = 'column';
    overlay.style.justifyContent = 'center';
    overlay.style.alignItems = 'center';
    overlay.style.padding = '20px';

    // 创建提示文本
    const instruction = document.createElement('div');
    instruction.innerHTML = `
        <div style="color: #00ffff; font-size: 18px; text-align: center; margin-bottom: 20px; font-weight: bold;">
            📋 请手动复制${type}
        </div>
        <div style="color: #ffffff; font-size: 14px; text-align: center; margin-bottom: 20px;">
            文本已选中，请使用以下方式复制：<br>
            • 长按文本框选择"复制"<br>
            • 或使用 Ctrl+C (Android) / Command+C (iOS)
        </div>
    `;

    // 创建关闭按钮
    const closeBtn = document.createElement('button');
    closeBtn.textContent = '完成';
    closeBtn.style.padding = '10px 20px';
    closeBtn.style.fontSize = '16px';
    closeBtn.style.background = '#00ffff';
    closeBtn.style.color = '#000';
    closeBtn.style.border = 'none';
    closeBtn.style.borderRadius = '5px';
    closeBtn.style.marginTop = '20px';
    closeBtn.style.cursor = 'pointer';

    closeBtn.onclick = () => {
        document.body.removeChild(overlay);
        document.body.removeChild(textArea);
    };

    overlay.appendChild(instruction);
    overlay.appendChild(closeBtn);
    document.body.appendChild(overlay);

    // 确保文本区域在最上层
    textArea.style.zIndex = '10001';
}

/**
 * 万能复制对话框 - 支持所有设备的复制方案
 */
function showUniversalCopyDialog(text, type) {
    // 移除已存在的复制对话框
    const existingModal = document.querySelector('.universal-copy-modal');
    if (existingModal) {
        existingModal.remove();
    }

    const modal = document.createElement('div');
    modal.className = 'universal-copy-modal';
    modal.style.position = 'fixed';
    modal.style.top = '0';
    modal.style.left = '0';
    modal.style.width = '100%';
    modal.style.height = '100%';
    modal.style.background = 'rgba(0, 0, 0, 0.9)';
    modal.style.zIndex = '99999';
    modal.style.display = 'flex';
    modal.style.justifyContent = 'center';
    modal.style.alignItems = 'center';
    modal.style.padding = '20px';
    modal.style.fontFamily = 'Arial, sans-serif';

    const content = document.createElement('div');
    content.style.background = 'linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 100%)';
    content.style.border = '3px solid #00ffff';
    content.style.borderRadius = '15px';
    content.style.padding = '25px';
    content.style.maxWidth = '95%';
    content.style.maxHeight = '90%';
    content.style.overflow = 'auto';
    content.style.boxShadow = '0 0 30px rgba(0, 255, 255, 0.5)';
    content.style.position = 'relative';

    // 检测设备类型
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

    content.innerHTML = `
        <div style="color: #00ffff; font-size: 20px; text-align: center; margin-bottom: 20px; font-weight: bold; text-shadow: 0 0 10px #00ffff;">
            📋 复制${type}
        </div>

        <div style="background: rgba(0, 255, 255, 0.1); border: 2px solid #00ffff; border-radius: 10px; padding: 15px; margin-bottom: 20px;">
            <div style="color: #ffffff; font-size: 14px; margin-bottom: 10px; font-weight: bold;">
                📱 操作说明：
            </div>
            <div style="color: #b8c5d6; font-size: 13px; line-height: 1.5;">
                ${isMobile ? `
                    • <strong>方法1：</strong>长按下方文本框，选择"全选"，然后选择"复制"<br>
                    • <strong>方法2：</strong>双击文本框选中全部内容，然后复制<br>
                    • <strong>方法3：</strong>手动选择文本，使用系统复制功能<br>
                    ${isIOS ? '• <strong>iOS用户：</strong>选中文本后点击"拷贝"' : '• <strong>Android用户：</strong>选中文本后点击"复制"'}
                ` : `
                    • <strong>方法1：</strong>点击"一键复制"按钮<br>
                    • <strong>方法2：</strong>选中文本后按 Ctrl+C 复制<br>
                    • <strong>方法3：</strong>右键点击文本选择"复制"
                `}
            </div>
        </div>

        <div style="margin-bottom: 20px;">
            <div style="color: #00ff41; font-size: 14px; margin-bottom: 8px; font-weight: bold;">
                ${type}内容：
            </div>
            <textarea id="copyTextArea" readonly style="
                width: 100%;
                height: 120px;
                padding: 15px;
                font-size: 16px;
                font-family: 'Courier New', monospace;
                background: rgba(0, 0, 0, 0.7);
                border: 2px solid #00ff41;
                border-radius: 8px;
                color: #00ff41;
                resize: none;
                line-height: 1.4;
                word-break: break-all;
                user-select: text;
                -webkit-user-select: text;
                -moz-user-select: text;
                -ms-user-select: text;
                outline: none;
                box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
            ">${text}</textarea>
        </div>

        <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
            <button id="autoSelectBtn" style="
                padding: 12px 20px;
                font-size: 16px;
                background: linear-gradient(135deg, #00ff41, #32cd32);
                color: #000;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-weight: bold;
                min-width: 120px;
                box-shadow: 0 4px 15px rgba(0, 255, 65, 0.3);
                transition: all 0.3s ease;
            ">🎯 自动选择</button>

            <button id="oneCopyBtn" style="
                padding: 12px 20px;
                font-size: 16px;
                background: linear-gradient(135deg, #00ffff, #0080ff);
                color: #000;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-weight: bold;
                min-width: 120px;
                box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);
                transition: all 0.3s ease;
            ">⚡ 一键复制</button>

            <button id="closeBtn" style="
                padding: 12px 20px;
                font-size: 16px;
                background: linear-gradient(135deg, #ff6b6b, #ff5252);
                color: #fff;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-weight: bold;
                min-width: 120px;
                box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
                transition: all 0.3s ease;
            ">❌ 关闭</button>
        </div>

        <div id="copyStatus" style="
            margin-top: 15px;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            min-height: 20px;
        "></div>
    `;

    modal.appendChild(content);
    document.body.appendChild(modal);

    // 获取元素
    const textarea = content.querySelector('#copyTextArea');
    const autoSelectBtn = content.querySelector('#autoSelectBtn');
    const oneCopyBtn = content.querySelector('#oneCopyBtn');
    const closeBtn = content.querySelector('#closeBtn');
    const copyStatus = content.querySelector('#copyStatus');

    // 自动选择功能
    autoSelectBtn.onclick = () => {
        try {
            textarea.focus();
            textarea.select();
            textarea.setSelectionRange(0, text.length);
            copyStatus.innerHTML = '<span style="color: #00ff41;">✅ 文本已选中，请使用系统复制功能</span>';

            // 添加视觉效果
            textarea.style.background = 'rgba(0, 255, 65, 0.2)';
            setTimeout(() => {
                textarea.style.background = 'rgba(0, 0, 0, 0.7)';
            }, 1000);
        } catch (error) {
            console.error('Auto select failed:', error);
            copyStatus.innerHTML = '<span style="color: #ff6b6b;">❌ 自动选择失败，请手动选择文本</span>';
        }
    };

    // 一键复制功能（多种方案）
    oneCopyBtn.onclick = async () => {
        let success = false;
        copyStatus.innerHTML = '<span style="color: #ffff00;">⏳ 正在尝试复制...</span>';

        // 方案1: 现代剪贴板API
        if (navigator.clipboard && window.isSecureContext) {
            try {
                await navigator.clipboard.writeText(text);
                success = true;
                copyStatus.innerHTML = '<span style="color: #00ff41;">✅ 复制成功！可以去粘贴了</span>';
            } catch (error) {
                console.log('Clipboard API failed:', error);
            }
        }

        // 方案2: 传统方法
        if (!success) {
            try {
                textarea.focus();
                textarea.select();
                textarea.setSelectionRange(0, text.length);

                const result = document.execCommand('copy');
                if (result) {
                    success = true;
                    copyStatus.innerHTML = '<span style="color: #00ff41;">✅ 复制成功！可以去粘贴了</span>';
                }
            } catch (error) {
                console.log('ExecCommand failed:', error);
            }
        }

        // 方案3: 如果都失败了，提示手动复制
        if (!success) {
            textarea.focus();
            textarea.select();
            copyStatus.innerHTML = '<span style="color: #ffff00;">⚠️ 自动复制失败，文本已选中，请手动复制 (Ctrl+C 或右键复制)</span>';
        }

        // 添加成功动画
        if (success) {
            oneCopyBtn.innerHTML = '✅ 已复制';
            oneCopyBtn.style.background = 'linear-gradient(135deg, #00ff41, #32cd32)';
            setTimeout(() => {
                oneCopyBtn.innerHTML = '⚡ 一键复制';
                oneCopyBtn.style.background = 'linear-gradient(135deg, #00ffff, #0080ff)';
            }, 2000);
        }
    };

    // 关闭功能
    closeBtn.onclick = () => {
        modal.remove();
    };

    // 点击背景关闭
    modal.onclick = (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    };

    // 自动聚焦到文本区域
    setTimeout(() => {
        textarea.focus();
        textarea.select();
    }, 300);

    // 添加键盘快捷键
    document.addEventListener('keydown', function copyKeyHandler(e) {
        if (e.key === 'Escape') {
            modal.remove();
            document.removeEventListener('keydown', copyKeyHandler);
        }
        if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
            e.preventDefault();
            textarea.focus();
            textarea.select();
        }
    });
}

/**
 * 显示手动复制对话框（保留作为备用）
 */
function showManualCopyDialog(text, type) {
    showUniversalCopyDialog(text, type);
}


function escapeHtml(unsafe) {
    if (unsafe == null) return '';
    return String(unsafe)
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
}

function copyFromButton(btn, type='') {
    const val = btn.getAttribute('data-copy') || '';

    // 添加视觉反馈
    const originalText = btn.innerHTML;
    const originalBg = btn.style.background;
    const originalColor = btn.style.color;

    btn.innerHTML = '<i class="bi bi-check-circle-fill"></i>';
    btn.style.background = '#00ff41';
    btn.style.color = '#000';
    btn.style.transform = 'scale(1.1)';

    // 执行复制
    copyToClipboard(val, type);

    // 恢复按钮状态
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.style.background = originalBg;
        btn.style.color = originalColor;
        btn.style.transform = '';
    }, 1500);
}

/**
 * 检测设备类型和复制能力
 */
function getDeviceInfo() {
    const userAgent = navigator.userAgent;
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
    const isIOS = /iPad|iPhone|iPod/.test(userAgent);
    const isAndroid = /Android/.test(userAgent);
    const hasClipboardAPI = navigator.clipboard && window.isSecureContext;

    return {
        isMobile,
        isIOS,
        isAndroid,
        hasClipboardAPI,
        supportsExecCommand: document.queryCommandSupported && document.queryCommandSupported('copy')
    };
}

/**
 * 为移动端优化复制按钮
 */
function optimizeCopyButtonsForMobile() {
    const deviceInfo = getDeviceInfo();

    if (deviceInfo.isMobile) {
        // 为所有复制按钮添加移动端优化
        const copyButtons = document.querySelectorAll('[onclick*="copyFromButton"]');
        copyButtons.forEach(btn => {
            // 增大点击区域
            btn.style.minWidth = '44px';
            btn.style.minHeight = '44px';
            btn.style.padding = '8px';

            // 添加触摸反馈
            btn.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
                this.style.opacity = '0.8';
            });

            btn.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
                this.style.opacity = '1';
            });

            // 防止双击缩放
            btn.addEventListener('touchend', function(e) {
                e.preventDefault();
            });
        });

        // 添加移动端复制提示
        if (!deviceInfo.hasClipboardAPI) {
            const copyHint = document.createElement('div');
            copyHint.style.position = 'fixed';
            copyHint.style.bottom = '20px';
            copyHint.style.left = '50%';
            copyHint.style.transform = 'translateX(-50%)';
            copyHint.style.background = 'rgba(255, 165, 0, 0.9)';
            copyHint.style.color = '#000';
            copyHint.style.padding = '8px 16px';
            copyHint.style.borderRadius = '20px';
            copyHint.style.fontSize = '12px';
            copyHint.style.zIndex = '1000';
            copyHint.style.display = 'none';
            copyHint.textContent = '💡 点击复制按钮后，长按文本选择复制';
            copyHint.id = 'mobile-copy-hint';

            document.body.appendChild(copyHint);

            // 首次点击复制按钮时显示提示
            let hintShown = localStorage.getItem('mobile-copy-hint-shown');
            if (!hintShown) {
                copyButtons.forEach(btn => {
                    btn.addEventListener('click', function() {
                        if (!hintShown) {
                            copyHint.style.display = 'block';
                            setTimeout(() => {
                                copyHint.style.display = 'none';
                            }, 5000);
                            localStorage.setItem('mobile-copy-hint-shown', 'true');
                            hintShown = true;
                        }
                    }, { once: true });
                });
            }
        }
    }
}

/**
 * 检查并显示租号必读
 */
function checkAndShowRentalTerms() {
    // 检查用户是否已经阅读过条款
    const termsAccepted = localStorage.getItem('rental_terms_accepted');
    const currentSession = sessionToken;
    const lastAcceptedSession = localStorage.getItem('rental_terms_session');

    // 如果是新会话或者从未接受过条款，显示必读
    if (!termsAccepted || lastAcceptedSession !== currentSession) {
        setTimeout(() => {
            showRentalTermsModal();
        }, 1000); // 延迟1秒显示，让页面先加载完成
    }
}

/**
 * 显示租号必读弹窗
 */
function showRentalTermsModal() {
    // 移除已存在的弹窗
    const existingModal = document.querySelector('.rental-terms-modal');
    if (existingModal) {
        existingModal.remove();
    }

    const modal = document.createElement('div');
    modal.className = 'rental-terms-modal';
    modal.style.position = 'fixed';
    modal.style.top = '0';
    modal.style.left = '0';
    modal.style.width = '100%';
    modal.style.height = '100%';
    modal.style.background = 'rgba(0, 0, 0, 0.95)';
    modal.style.zIndex = '99999';
    modal.style.display = 'flex';
    modal.style.justifyContent = 'center';
    modal.style.alignItems = 'center';
    modal.style.padding = '20px';
    modal.style.fontFamily = 'Arial, sans-serif';
    modal.style.backdropFilter = 'blur(10px)';

    const content = document.createElement('div');
    content.style.background = 'linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 100%)';
    content.style.border = '3px solid #ff0080';
    content.style.borderRadius = '15px';
    content.style.padding = '30px';
    content.style.maxWidth = '90%';
    content.style.maxHeight = '90%';
    content.style.overflow = 'auto';
    content.style.boxShadow = '0 0 50px rgba(255, 0, 128, 0.5)';
    content.style.position = 'relative';
    content.style.animation = 'modalSlideIn 0.5s ease-out';

    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.8) translateY(-50px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        @keyframes pulseGlow {
            0%, 100% {
                box-shadow: 0 0 20px rgba(255, 0, 128, 0.5);
            }
            50% {
                box-shadow: 0 0 40px rgba(255, 0, 128, 0.8);
            }
        }

        .rental-terms-modal .agree-btn {
            animation: pulseGlow 2s ease-in-out infinite;
        }
    `;
    document.head.appendChild(style);

    content.innerHTML = `
        <div style="text-align: center; margin-bottom: 25px;">
            <div style="color: #ff0080; font-size: 2.2rem; font-weight: bold; margin-bottom: 10px; text-shadow: 0 0 15px #ff0080;">
                🚨 租号必读 🚨
            </div>
            <div style="color: #ffff00; font-size: 1.1rem; font-weight: bold; text-shadow: 0 0 10px #ffff00;">
                违规必封，请务必遵守！
            </div>
        </div>

        <div style="background: rgba(255, 0, 128, 0.1); border: 2px solid #ff0080; border-radius: 10px; padding: 20px; margin-bottom: 25px;">
            <div style="color: #ffffff; font-size: 1rem; line-height: 1.8; text-align: left;">
                <p style="color: #00ffff; font-size: 1.1rem; margin-bottom: 15px;">
                    尊敬的客户，感谢您租用本店账号。为保障账号安全和您的体验，请务必遵守以下规则，任何违规行为都将导致账号被封禁，且不予退款！
                </p>

                <div style="margin: 15px 0;">
                    <div style="color: #ff6b6b; font-weight: bold; margin-bottom: 8px;">🚫 严禁挂机、逃跑、送人头！</div>
                    <div style="color: #b8c5d6; font-size: 0.9rem; margin-left: 20px; margin-bottom: 10px;">—— 文明游戏，保持良好的竞技心态。</div>
                </div>

                <div style="margin: 15px 0;">
                    <div style="color: #ff6b6b; font-weight: bold; margin-bottom: 8px;">🚫 严禁开外挂、脚本、第三方软件！</div>
                    <div style="color: #b8c5d6; font-size: 0.9rem; margin-left: 20px; margin-bottom: 10px;">—— 一经检测，永久封禁！</div>
                </div>

                <div style="margin: 15px 0;">
                    <div style="color: #ff6b6b; font-weight: bold; margin-bottom: 8px;">🚫 严禁随意使用账号财产！</div>
                    <div style="color: #b8c5d6; font-size: 0.9rem; margin-left: 20px; margin-bottom: 10px;">（如元宝、宝箱、游戏币等）</div>
                </div>

                <div style="margin: 15px 0;">
                    <div style="color: #ff6b6b; font-weight: bold; margin-bottom: 8px;">🚫 严禁删除账号上带sgs前缀的游戏好友！</div>
                </div>

                <div style="margin: 15px 0;">
                    <div style="color: #ff6b6b; font-weight: bold; margin-bottom: 8px;">🚫 严禁共享账号、借给他人使用！</div>
                    <div style="color: #b8c5d6; font-size: 0.9rem; margin-left: 20px; margin-bottom: 10px;">—— 一号一人，谁租谁用。</div>
                </div>

                <div style="margin: 15px 0;">
                    <div style="color: #ff6b6b; font-weight: bold; margin-bottom: 8px;">🚫 严禁在游戏内发言违规、辱骂、骚扰他人！</div>
                    <div style="color: #b8c5d6; font-size: 0.9rem; margin-left: 20px; margin-bottom: 10px;">—— 做个高素质玩家。</div>
                </div>

                <div style="background: rgba(255, 165, 0, 0.2); border: 1px solid #ffa500; border-radius: 8px; padding: 15px; margin: 20px 0;">
                    <div style="color: #ffa500; font-weight: bold; margin-bottom: 8px;">💡 温馨提醒：</div>
                    <div style="color: #ffffff; font-size: 0.95rem;">如遇账号顶号或异常，请立即联系客服，切勿自行操作。</div>
                </div>

                <div style="color: #00ff41; font-size: 1rem; text-align: center; margin-top: 20px; font-weight: bold;">
                    请您珍惜账号，共同维护良好的游戏环境！祝您游戏愉快！
                </div>
            </div>
        </div>

        <div style="text-align: center;">
            <button id="agreeTermsBtn" class="agree-btn" style="
                background: linear-gradient(135deg, #00ff41, #32cd32);
                color: #000;
                border: none;
                border-radius: 10px;
                padding: 15px 40px;
                font-size: 1.2rem;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                min-width: 250px;
                text-transform: uppercase;
                letter-spacing: 1px;
            ">✅ 我已阅读并同意以上条款</button>
        </div>

        <div style="text-align: center; margin-top: 15px; color: #888; font-size: 0.9rem;">
            点击同意后即可开始使用租号服务
        </div>
    `;

    modal.appendChild(content);
    document.body.appendChild(modal);

    // 获取同意按钮
    const agreeBtn = content.querySelector('#agreeTermsBtn');

    // 添加按钮悬停效果
    agreeBtn.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.05)';
        this.style.boxShadow = '0 8px 25px rgba(0, 255, 65, 0.4)';
    });

    agreeBtn.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1)';
        this.style.boxShadow = '';
    });

    // 同意按钮点击事件
    agreeBtn.onclick = () => {
        // 保存用户已同意条款
        localStorage.setItem('rental_terms_accepted', 'true');
        localStorage.setItem('rental_terms_session', sessionToken);
        localStorage.setItem('rental_terms_accepted_time', new Date().toISOString());

        // 添加成功动画
        agreeBtn.innerHTML = '✅ 已同意条款';
        agreeBtn.style.background = 'linear-gradient(135deg, #00ff41, #32cd32)';
        agreeBtn.style.transform = 'scale(1.1)';

        // 显示成功提示
        showAlert('success', '感谢您的配合！请遵守租号条款，祝您游戏愉快！');

        // 1秒后关闭弹窗
        setTimeout(() => {
            modal.style.opacity = '0';
            modal.style.transform = 'scale(0.8)';
            setTimeout(() => {
                modal.remove();
            }, 300);
        }, 1000);
    };

    // 防止点击背景关闭（必须同意条款）
    modal.onclick = (e) => {
        if (e.target === modal) {
            // 摇晃动画提示必须同意
            content.style.animation = 'shake 0.5s ease-in-out';
            setTimeout(() => {
                content.style.animation = 'modalSlideIn 0.5s ease-out';
            }, 500);
        }
    };

    // 添加摇晃动画
    const shakeStyle = document.createElement('style');
    shakeStyle.textContent = `
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }
    `;
    document.head.appendChild(shakeStyle);

    // 禁用ESC键关闭
    document.addEventListener('keydown', function termsKeyHandler(e) {
        if (e.key === 'Escape') {
            e.preventDefault();
            // 摇晃提示
            content.style.animation = 'shake 0.5s ease-in-out';
            setTimeout(() => {
                content.style.animation = 'modalSlideIn 0.5s ease-out';
            }, 500);
        }
    });
}

/**
 * 页面加载完成后初始化移动端优化
 */
document.addEventListener('DOMContentLoaded', function() {
    optimizeCopyButtonsForMobile();
});

// 如果页面已经加载完成，立即执行优化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', optimizeCopyButtonsForMobile);
} else {
    optimizeCopyButtonsForMobile();
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateString) {
    if (!dateString) return '未知';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

/**
 * 显示提示信息
 */
function showAlert(type, message) {
    // 创建提示框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // 3秒后自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}

/**
 * 显示设备信息
 */
async function showDeviceInfo() {
    try {
        const response = await fetch('api/customer_new.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_device_info',
                session_token: sessionToken
            })
        });

        const result = await response.json();

        if (result.success) {
            displayDeviceInfo(result.data);
            new bootstrap.Modal(document.getElementById('deviceInfoModal')).show();
        } else {
            if (!handleApiError(result)) {
                showAlert('danger', result.message || '获取设备信息失败');
            }
        }
    } catch (error) {
        console.error('获取设备信息失败:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

/**
 * 显示设备信息内容
 */
function displayDeviceInfo(data) {
    const content = document.getElementById('deviceInfoContent');
    const currentDevice = data.current_device;
    const activeSessions = data.active_sessions || [];
    const recentLogins = data.recent_logins || [];
    const singleDeviceEnabled = data.single_device_enabled;

    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6 style="color: var(--cyber-cyan); font-weight: 600; margin-bottom: 15px;">
                    <i class="bi bi-laptop me-2"></i>当前设备信息
                </h6>
                <div class="card" style="background: rgba(0, 255, 255, 0.1); border: 1px solid var(--cyber-cyan); border-radius: 10px;">
                    <div class="card-body">
                        <div class="mb-2">
                            <strong style="color: var(--text-primary);">IP地址：</strong>
                            <span style="color: var(--cyber-cyan); font-weight: 600;">${currentDevice.ip_address}</span>
                        </div>
                        <div class="mb-2">
                            <strong style="color: var(--text-primary);">设备指纹：</strong>
                            <span style="color: var(--text-secondary); font-family: monospace; font-size: 0.8rem;">${currentDevice.device_fingerprint}</span>
                        </div>
                        <div class="mb-2">
                            <strong style="color: var(--text-primary);">单设备模式：</strong>
                            <span class="badge" style="background: ${singleDeviceEnabled ? 'linear-gradient(135deg, #28a745, #20c997)' : 'linear-gradient(135deg, #6c757d, #5a6268)'}; padding: 4px 8px; font-size: 10px;">
                                ${singleDeviceEnabled ? '已启用' : '已禁用'}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <h6 style="color: var(--cyber-cyan); font-weight: 600; margin-bottom: 15px;">
                    <i class="bi bi-activity me-2"></i>活跃会话 (${activeSessions.length}个)
                </h6>
                <div class="card" style="background: rgba(0, 255, 255, 0.1); border: 1px solid var(--cyber-cyan); border-radius: 10px; max-height: 200px; overflow-y: auto;">
                    <div class="card-body">
                        ${activeSessions.length > 0 ? activeSessions.map(session => `
                            <div class="mb-2 p-2" style="background: rgba(0, 255, 255, 0.05); border-radius: 5px; border-left: 3px solid ${session.ip_address === currentDevice.ip_address ? 'var(--cyber-green)' : 'var(--cyber-yellow)'};">
                                <div style="font-size: 0.8rem;">
                                    <strong>IP:</strong> ${session.ip_address}
                                    ${session.ip_address === currentDevice.ip_address ? '<span style="color: var(--cyber-green); font-weight: 600;"> (当前)</span>' : ''}
                                </div>
                                <div style="font-size: 0.7rem; color: var(--text-secondary);">
                                    最后活跃: ${formatDateTime(session.last_activity)}
                                </div>
                            </div>
                        `).join('') : '<p style="color: var(--text-secondary); text-align: center; margin: 0;">暂无其他活跃会话</p>'}
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <h6 style="color: var(--cyber-cyan); font-weight: 600; margin-bottom: 15px;">
                    <i class="bi bi-clock-history me-2"></i>最近登录记录
                </h6>
                <div class="card" style="background: rgba(0, 255, 255, 0.1); border: 1px solid var(--cyber-cyan); border-radius: 10px; max-height: 250px; overflow-y: auto;">
                    <div class="card-body">
                        ${recentLogins.length > 0 ? recentLogins.map(log => `
                            <div class="mb-2 p-2" style="background: rgba(0, 255, 255, 0.05); border-radius: 5px; border-left: 3px solid ${getActionColor(log.action)};">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <span style="color: ${getActionColor(log.action)}; font-weight: 600;">
                                            ${getActionText(log.action)}
                                        </span>
                                        <span style="color: var(--text-primary); margin-left: 10px;">
                                            IP: ${log.ip_address}
                                        </span>
                                    </div>
                                    <div style="font-size: 0.7rem; color: var(--text-secondary);">
                                        ${formatDateTime(log.created_at)}
                                    </div>
                                </div>
                                ${log.reason ? `<div style="font-size: 0.7rem; color: var(--text-secondary); margin-top: 4px;">原因: ${log.reason}</div>` : ''}
                            </div>
                        `).join('') : '<p style="color: var(--text-secondary); text-align: center; margin: 0;">暂无登录记录</p>'}
                    </div>
                </div>
            </div>
        </div>

        ${singleDeviceEnabled ? `
        <div class="row mt-3">
            <div class="col-12">
                <div class="alert" style="background: rgba(255, 193, 7, 0.1); border: 1px solid var(--cyber-yellow); color: var(--text-primary); border-radius: 8px;">
                    <i class="bi bi-shield-check me-2" style="color: var(--cyber-yellow);"></i>
                    <strong>安全提示：</strong>您的账号已启用单设备登录保护。如果在其他设备登录，当前设备的会话将被自动终止。
                </div>
            </div>
        </div>
        ` : ''}
    `;
}

/**
 * 获取操作对应的颜色
 */
function getActionColor(action) {
    switch (action) {
        case 'login': return 'var(--cyber-green)';
        case 'logout': return 'var(--cyber-cyan)';
        case 'kicked': return 'var(--cyber-pink)';
        default: return 'var(--text-secondary)';
    }
}

/**
 * 获取操作对应的文本
 */
function getActionText(action) {
    switch (action) {
        case 'login': return '登录';
        case 'logout': return '登出';
        case 'kicked': return '被踢出';
        default: return action;
    }
}

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
    if (sessionCheckInterval) {
        clearInterval(sessionCheckInterval);
    }
});
