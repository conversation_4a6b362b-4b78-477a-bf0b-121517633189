# 三国杀取号系统 Apache配置文件

# 启用重写引擎
RewriteEngine On

# 安全配置
# 隐藏服务器信息
ServerTokens Prod
ServerSignature Off

# 防止访问敏感文件
<Files "*.sql">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.md">
    Order Allow,Deny
    Deny from all
</Files>

<Files "test.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "setup.php">
    Order Allow,Deny
    Deny from all
</Files>

# 防止访问配置文件
<FilesMatch "^(config|\.env|\.git)">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# 防止目录浏览
Options -Indexes

# 性能优化
# 启用压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# 设置缓存
<IfModule mod_expires.c>
    ExpiresActive On
    
    # 图片缓存1个月
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS和JS缓存1周
    ExpiresByType text/css "access plus 1 week"
    ExpiresByType application/javascript "access plus 1 week"
    ExpiresByType application/x-javascript "access plus 1 week"
    
    # HTML文件缓存1小时
    ExpiresByType text/html "access plus 1 hour"
    
    # 字体文件缓存1个月
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
    ExpiresByType application/font-woff "access plus 1 month"
    ExpiresByType application/font-woff2 "access plus 1 month"
</IfModule>

# 设置缓存控制头
<IfModule mod_headers.c>
    # 静态资源缓存
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2)$">
        Header set Cache-Control "public, max-age=2592000"
    </FilesMatch>
    
    # HTML文件缓存
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "public, max-age=3600"
    </FilesMatch>
    
    # API接口不缓存
    <FilesMatch "\.(php)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
    
    # 安全头
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # CORS设置（如果需要）
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
</IfModule>

# URL重写规则
# API路由
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/$1 [L,QSA]

# 友好URL（可选）
# RewriteRule ^admin/?$ admin/login.html [L]
# RewriteRule ^login/?$ index.html [L]

# 错误页面
ErrorDocument 404 /404.html
ErrorDocument 403 /403.html
ErrorDocument 500 /500.html

# 字符编码
AddDefaultCharset UTF-8

# MIME类型
AddType application/javascript .js
AddType text/css .css
AddType application/json .json

# 安全限制
# 限制文件上传大小
LimitRequestBody 10485760

# 防止SQL注入和XSS攻击
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|DROP|DELETE|UPDATE|CREATE|ALTER|EXEC) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# 防止热链接（可选）
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?sanguosha\.lanjuan\.xin [NC]
    RewriteCond %{REQUEST_URI} \.(jpg|jpeg|png|gif|css|js)$ [NC]
    RewriteRule \.(jpg|jpeg|png|gif|css|js)$ - [F]
</IfModule>

# 日志配置
# CustomLog logs/access.log combined
# ErrorLog logs/error.log

# PHP配置优化
<IfModule mod_php7.c>
    php_value memory_limit 128M
    php_value max_execution_time 30
    php_value max_input_time 60
    php_value post_max_size 10M
    php_value upload_max_filesize 10M
    php_value session.gc_maxlifetime 3600
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_strict_mode 1
</IfModule>

# 启用KeepAlive
<IfModule mod_headers.c>
    Header set Connection keep-alive
</IfModule>
