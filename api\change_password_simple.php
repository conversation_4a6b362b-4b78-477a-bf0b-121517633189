<?php
/**
 * 极简版管理员密码修改API
 * 专注于核心功能，减少复杂性
 */

// 开启输出缓冲和错误处理
ob_start();
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 简单的JSON响应函数
function respond($success, $message, $data = null) {
    if (ob_get_level()) ob_clean();
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 错误处理
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR])) {
        if (ob_get_level()) ob_clean();
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'success' => false,
            'message' => 'PHP错误: ' . $error['message'],
            'error_line' => $error['line'],
            'timestamp' => time()
        ]);
    }
});

try {
    // 开始会话
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // 处理OPTIONS请求
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        respond(true, 'OPTIONS OK');
    }

    // 处理GET请求（测试用）
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        respond(true, 'API正常工作', [
            'method' => 'GET',
            'session_id' => session_id(),
            'admin_id' => $_SESSION['admin_id'] ?? null
        ]);
    }

    // 只处理POST请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        respond(false, '只支持POST请求');
    }

    // 验证管理员会话
    if (!isset($_SESSION['admin_id']) || $_SESSION['admin_id'] <= 0) {
        respond(false, '需要管理员权限，请先登录');
    }

    // 获取POST数据
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        respond(false, '无效的请求数据');
    }

    $currentPassword = trim($input['current_password'] ?? '');
    $newPassword = trim($input['new_password'] ?? '');
    $confirmPassword = trim($input['confirm_password'] ?? '');

    // 验证输入
    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        respond(false, '请填写所有密码字段');
    }

    if ($newPassword !== $confirmPassword) {
        respond(false, '新密码和确认密码不匹配');
    }

    if (strlen($newPassword) < 6) {
        respond(false, '新密码长度至少6位');
    }

    // 连接数据库
    require_once '../config/database.php';
    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        respond(false, '数据库连接失败');
    }

    $adminId = $_SESSION['admin_id'];

    // 根据测试结果，系统有admin_accounts表，优先使用
    $tableName = 'admin_accounts';
    
    // 查询管理员信息
    $stmt = $db->prepare("SELECT * FROM $tableName WHERE id = ?");
    $stmt->execute([$adminId]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$admin) {
        respond(false, '管理员账号不存在');
    }

    // 验证当前密码
    if (!password_verify($currentPassword, $admin['password'])) {
        respond(false, '当前密码错误');
    }

    // 更新密码
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    $stmt = $db->prepare("UPDATE $tableName SET password = ?, updated_at = NOW() WHERE id = ?");
    $stmt->execute([$hashedPassword, $adminId]);

    if ($stmt->rowCount() > 0) {
        // 记录操作日志（可选）
        try {
            $stmt = $db->prepare("INSERT INTO operation_logs (user_type, user_id, action, description, ip_address, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            $stmt->execute(['admin', $adminId, 'change_password', "管理员 {$admin['username']} 修改密码", $_SERVER['REMOTE_ADDR'] ?? '']);
        } catch (Exception $e) {
            // 日志记录失败不影响主要功能
            error_log("Log operation failed: " . $e->getMessage());
        }

        respond(true, '密码修改成功');
    } else {
        respond(false, '密码修改失败，请稍后重试');
    }

} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    respond(false, '数据库操作失败');
} catch (Exception $e) {
    error_log("General error: " . $e->getMessage());
    respond(false, '系统错误: ' . $e->getMessage());
}
?>
