<?php
/**
 * 客户端API - 完整版
 */

header("Content-Type: application/json; charset=utf-8");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// 处理预检请求
if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    exit(0);
}

// 引入全局配置（用于统一错误日志与DB常量）
require_once __DIR__ . '/../config.php';


// 错误处理
error_reporting(E_ALL);
ini_set("display_errors", 0);
ini_set("log_errors", 1);


// 安全随机Token
function secure_token($bytes = 32) {
    if (function_exists('random_bytes')) {
        try { return bin2hex(random_bytes($bytes)); } catch (Exception $e) {}
    }
    if (function_exists('openssl_random_pseudo_bytes')) {
        $bytestr = @openssl_random_pseudo_bytes($bytes);
        if ($bytestr !== false) return bin2hex($bytestr);
    }
    // 回退方案（足够用于会话token）
    return bin2hex(substr(hash('sha256', uniqid((string)mt_rand(), true), true), 0, $bytes));
}

// 启动会话
if (session_status() === PHP_SESSION_NONE) { session_start(); }

// 数据库连接（统一走 config/database.php 的 Database 类）
try {
    require_once __DIR__ . '/../config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    if (!$db) { throw new Exception('DB connect failed'); }
} catch (Exception $e) {
    error_log('customer_new DB error: '.$e->getMessage());
    jsonResponse(false, "数据库连接失败");
    exit;
}

// 获取输入数据
$input = json_decode(file_get_contents("php://input"), true);
if (!$input) {
    jsonResponse(false, "无效的请求数据");
    exit;
}

$action = $input["action"] ?? "";

// 路由处理
switch ($action) {
    case "login":
        customerLogin($db, $input);
        break;
    case "get_available_accounts":
        getAvailableAccounts($db, $input);
        break;
    case "search_accounts":
        searchAccounts($db, $input);
        break;
    case "take_account":
        takeAccount($db, $input);
        break;
    case "release_account":
        releaseAccount($db, $input);
        break;
    case "get_current_account":
        getCurrentAccount($db, $input);
        break;
    case "get_account_details":
        getAccountDetails($db, $input);
        break;
    case "logout":
        customerLogout($db, $input);
        break;
    case "check_session":
        checkCustomerSession($db, $input);
        break;
    case "get_device_info":
        getDeviceInfo($db, $input);
        break;
    case "test":
        jsonResponse(true, "API连接正常", ["timestamp" => time(), "status" => "ok"]);
        break;
    case "create_test_data":
        createTestData($db, $input);
        break;
    default:
        jsonResponse(false, "未知的操作");
}



// 客户登录
function customerLogin($db, $input) {
    try {
        $customerAccount = trim($input["customer_account"] ?? "");

        if (empty($customerAccount)) {
            jsonResponse(false, "请输入客户账号");
        }

        // 获取客户端信息
        $clientIp = $_SERVER['REMOTE_ADDR'] ?? '';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $deviceFingerprint = generateDeviceFingerprint($clientIp, $userAgent);

        // 查询客户账号
        $stmt = $db->prepare("SELECT * FROM customer_accounts WHERE account_number = ?");
        $stmt->execute([$customerAccount]);
        $customer = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$customer) {
            jsonResponse(false, "客户账号不存在");
        }

        // 封禁状态拦截
        if ($customer["status"] === "banned") {
            jsonResponse(false, "账号已被封禁，如需解封请联系客服");
        }
        // 到期状态拦截
        if ($customer["status"] === "expired") {
            jsonResponse(false, "账号时长已到期，如需延时请联系客服");
        }
        // 其他非 active 状态拦截
        if ($customer["status"] !== "active") {
            jsonResponse(false, "账号已被禁用或过期");
        }

        // 剩余时长校验
        if (floatval($customer["remaining_hours"]) <= 0) {
            jsonResponse(false, "账号时长已到期，如需延时请联系客服");
        }

        // 检查单设备登录限制
        $singleDeviceEnabled = getSingleDeviceLoginSetting($db);
        if ($singleDeviceEnabled) {
            $kickResult = kickExistingSessions($db, $customer["id"], $clientIp, $deviceFingerprint);
            if ($kickResult['kicked_sessions'] > 0) {
                // 记录踢出日志
                logDeviceAction($db, $customer["id"], $clientIp, $userAgent, $deviceFingerprint, 'kicked', null,
                    "新设备登录，踢出 {$kickResult['kicked_sessions']} 个旧会话");
            }
        }

        // 生成会话token
        $sessionToken = secure_token(32);
        $sessionExpires = date("Y-m-d H:i:s", time() + 7200); // 2小时后过期

        // 更新客户账号的会话信息
        $stmt = $db->prepare("
            UPDATE customer_accounts
            SET session_token = ?, session_expires = ?, last_login = NOW(), login_count = login_count + 1,
                first_login_time = IFNULL(first_login_time, NOW()),
                current_ip_address = ?, current_user_agent = ?, login_ip_address = ?, device_fingerprint = ?
            WHERE id = ?
        ");
        $stmt->execute([
            $sessionToken, $sessionExpires, $clientIp, $userAgent, $clientIp, $deviceFingerprint, $customer["id"]
        ]);

        // 创建新的会话记录
        $stmt = $db->prepare("
            INSERT INTO customer_sessions (customer_id, session_token, ip_address, user_agent, device_fingerprint, expires_at, last_activity)
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $customer["id"], $sessionToken, $clientIp, $userAgent, $deviceFingerprint, $sessionExpires
        ]);

        // 刷新首次登录时间
        if (empty($customer['first_login_time'])) {
            $customer['first_login_time'] = date('Y-m-d H:i:s');
        }

        // 设置会话
        $_SESSION["customer_id"] = $customer["id"];
        $_SESSION["customer_account"] = $customer["account_number"];
        $_SESSION["session_token"] = $sessionToken;

        // 记录操作日志
        logOperation($db, 'customer', $customer['id'], 'login', "客户登录 (IP: {$clientIp})", $clientIp);

        jsonResponse(true, "登录成功", [
            "customer_id" => $customer["id"],
            "account_number" => $customer["account_number"],
            "account_type" => $customer["account_type"],
            "duration_hours" => floatval($customer["duration_hours"]),
            "first_login_time" => $customer["first_login_time"],
            "remaining_hours" => floatval($customer["remaining_hours"]),
            "session_token" => $sessionToken,
            "device_info" => [
                "ip_address" => $clientIp,
                "single_device_mode" => $singleDeviceEnabled
            ]
        ]);

    } catch (Exception $e) {
        error_log("Customer login error: " . $e->getMessage());
        jsonResponse(false, "登录失败，请稍后重试");
    }
}

// 验证会话（基于数据库的无状态校验，增强版）
function validateSession($db, $sessionToken) {
    if (empty($sessionToken)) { return false; }

    // 获取客户端信息
    $clientIp = $_SERVER['REMOTE_ADDR'] ?? '';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    // 查询会话信息
    $stmt = $db->prepare("
        SELECT ca.id, ca.current_ip_address, ca.device_fingerprint, cs.ip_address as session_ip, cs.status
        FROM customer_accounts ca
        LEFT JOIN customer_sessions cs ON ca.session_token = cs.session_token AND cs.status = 'active'
        WHERE ca.session_token = ? AND ca.session_expires > NOW()
        LIMIT 1
    ");
    $stmt->execute([$sessionToken]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$session) {
        return false;
    }

    $customerId = (int)$session['id'];

    // 检查单设备登录限制
    $singleDeviceEnabled = getSingleDeviceLoginSetting($db);
    if ($singleDeviceEnabled) {
        // 检查IP地址是否匹配
        if ($session['current_ip_address'] !== $clientIp) {
            // IP地址不匹配，可能是其他设备登录，踢出当前会话
            kickSessionByToken($db, $sessionToken, "IP地址变更，疑似其他设备登录");
            return false;
        }
    }

    // 更新会话活跃时间
    updateSessionActivity($db, $sessionToken);

    return $customerId;
}

// 生成设备指纹
function generateDeviceFingerprint($ip, $userAgent) {
    $data = $ip . '|' . $userAgent;
    return substr(hash('sha256', $data), 0, 32);
}

// 获取单设备登录设置
function getSingleDeviceLoginSetting($db) {
    try {
        $stmt = $db->prepare("SELECT config_value FROM system_config WHERE config_key = 'single_device_login'");
        $stmt->execute();
        $value = $stmt->fetchColumn();
        return $value === '1';
    } catch (Exception $e) {
        return true; // 默认启用单设备登录
    }
}

// 踢出现有会话
function kickExistingSessions($db, $customerId, $currentIp, $currentFingerprint) {
    try {
        // 查找该客户的所有活跃会话
        $stmt = $db->prepare("
            SELECT session_token, ip_address, device_fingerprint
            FROM customer_sessions
            WHERE customer_id = ? AND status = 'active' AND expires_at > NOW()
        ");
        $stmt->execute([$customerId]);
        $existingSessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $kickedCount = 0;

        foreach ($existingSessions as $session) {
            // 如果IP或设备指纹不同，则踢出
            if ($session['ip_address'] !== $currentIp || $session['device_fingerprint'] !== $currentFingerprint) {
                kickSessionByToken($db, $session['session_token'], "新设备登录");
                $kickedCount++;
            }
        }

        return ['kicked_sessions' => $kickedCount];
    } catch (Exception $e) {
        error_log("踢出会话失败: " . $e->getMessage());
        return ['kicked_sessions' => 0];
    }
}

// 踢出指定会话
function kickSessionByToken($db, $sessionToken, $reason = '') {
    try {
        // 标记会话为被踢出
        $stmt = $db->prepare("UPDATE customer_sessions SET status = 'kicked' WHERE session_token = ?");
        $stmt->execute([$sessionToken]);

        // 清除客户账号表中的会话信息
        $stmt = $db->prepare("
            UPDATE customer_accounts
            SET session_token = NULL, session_expires = NULL, current_ip_address = NULL, current_user_agent = NULL
            WHERE session_token = ?
        ");
        $stmt->execute([$sessionToken]);

        // 记录踢出日志
        $stmt = $db->prepare("
            SELECT customer_id, ip_address, user_agent, device_fingerprint
            FROM customer_sessions
            WHERE session_token = ?
        ");
        $stmt->execute([$sessionToken]);
        $sessionInfo = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($sessionInfo) {
            logDeviceAction($db, $sessionInfo['customer_id'], $sessionInfo['ip_address'],
                $sessionInfo['user_agent'], $sessionInfo['device_fingerprint'], 'kicked', $sessionToken, $reason);
        }

        return true;
    } catch (Exception $e) {
        error_log("踢出会话失败: " . $e->getMessage());
        return false;
    }
}

// 更新会话活跃时间
function updateSessionActivity($db, $sessionToken) {
    try {
        $stmt = $db->prepare("UPDATE customer_sessions SET last_activity = NOW() WHERE session_token = ?");
        $stmt->execute([$sessionToken]);
    } catch (Exception $e) {
        error_log("更新会话活跃时间失败: " . $e->getMessage());
    }
}

// 记录设备操作日志
function logDeviceAction($db, $customerId, $ip, $userAgent, $deviceFingerprint, $action, $sessionToken = null, $reason = null) {
    try {
        $stmt = $db->prepare("
            INSERT INTO device_login_logs (customer_id, ip_address, user_agent, device_fingerprint, action, session_token, reason)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$customerId, $ip, $userAgent, $deviceFingerprint, $action, $sessionToken, $reason]);
    } catch (Exception $e) {
        error_log("记录设备日志失败: " . $e->getMessage());
    }
}

// 获取可用账号
function getAvailableAccounts($db, $input) {
    try {
        $sessionToken = $input["session_token"] ?? "";
        $customerId = validateSession($db, $sessionToken);

        if (!$customerId) {
            jsonResponse(false, "会话无效，请重新登录", null, 401);
            return;
        }

        // V2: 返回可用/在用账号并带权限标记
        // 获取客户类型
        $stmtC = $db->prepare('SELECT account_type FROM customer_accounts WHERE id=?');
        $stmtC->execute([$customerId]);
        $customerType = $stmtC->fetchColumn() ?: 'normal';

        // 拉取 available 与 in_use 账号
        $stmt = $db->prepare("SELECT id, account, password, account_name, level, vip_level, `rank`,
                                     nation_war, account_type, status, server_name, description, skin,
                                     premium_generals, dynamic_skins, general_count, skin_count,
                                     epic_general_count, dynamic_skin_count, epic_generals,
                                     dynamic_skin_details, general_details, skin_details, gender, price,
                                     current_user_id, taken_at
                              FROM game_accounts
                              WHERE status IN ('available','in_use')
                              ORDER BY account_type DESC, level DESC");
        $stmt->execute();
        $all = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $available = [];
        $inUseAll = [];
        $inUseMine = [];
        foreach ($all as $a) {
            // 权限标记：premium 账号仅 premium 客户可取
            $a['can_take'] = ($a['account_type'] === 'premium') ? ($customerType === 'premium') : true;
            if ($a['status'] === 'in_use') {
                $inUseAll[] = $a;
                if ((int)$a['current_user_id'] === (int)$customerId) {
                    $inUseMine[] = $a;
                }
            } elseif ($a['status'] === 'available') {
                $available[] = $a;
            }
        }

        jsonResponse(true, '获取成功', [
            'available_accounts' => $available,
            'in_use_accounts' => $inUseAll,
            'has_account_in_use' => count($inUseMine) > 0
        ]);

    } catch (Exception $e) {
        error_log("Get available accounts error: " . $e->getMessage());
        jsonResponse(false, "获取账号列表失败: " . $e->getMessage());
    }
}

// 搜索账号
function searchAccounts($db, $input) {
    try {
        $sessionToken = $input["session_token"] ?? "";
        $searchTerm = trim($input["search_term"] ?? "");
        $customerId = validateSession($db, $sessionToken);

        if (!$customerId) {
            jsonResponse(false, "会话无效，请重新登录", null, 401);
        }

        if (empty($searchTerm)) {
            jsonResponse(false, "请输入搜索关键词");
        }

        // 记录搜索关键词 + 操作日志
        try {
            $stmt = $db->prepare("
                INSERT INTO search_keywords (keyword, search_count, last_searched)
                VALUES (?, 1, NOW())
                ON DUPLICATE KEY UPDATE
                search_count = search_count + 1,
                last_searched = NOW()
            ");
            $stmt->execute([$searchTerm]);
            // 记录日志到 operation_logs，便于后台热搜/日志统计
            logOperation($db, 'customer', $customerId, 'search', $searchTerm);
        } catch (Exception $e) {
            // 忽略搜索记录错误
        }

        // 搜索账号（仅返回可用账号）- 增强搜索功能，包含武将和皮肤详情
        $stmt = $db->prepare("
            SELECT id, account, password, account_name, level, vip_level, rank,
                   account_type, status, server_name, description, skin,
                   premium_generals, dynamic_skins, general_count, skin_count,
                   epic_generals, dynamic_skin_details, skin_details, general_details
            FROM game_accounts
            WHERE status = 'available'
            AND (account LIKE ? OR account_name LIKE ? OR description LIKE ? OR server_name LIKE ?
                 OR skin LIKE ? OR premium_generals LIKE ? OR dynamic_skins LIKE ?
                 OR epic_generals LIKE ? OR dynamic_skin_details LIKE ? OR skin_details LIKE ?
                 OR general_details LIKE ?)
            ORDER BY account_type DESC, level DESC
        ");
        $searchPattern = "%" . $searchTerm . "%";
        $stmt->execute([
            $searchPattern, $searchPattern, $searchPattern, $searchPattern,
            $searchPattern, $searchPattern, $searchPattern, $searchPattern,
            $searchPattern, $searchPattern, $searchPattern
        ]);
        $accounts = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 根据客户类型标记 can_take（premium 客户可取 premium/normal；normal 客户仅可取 normal）
        $stmtC = $db->prepare('SELECT account_type FROM customer_accounts WHERE id=?');
        $stmtC->execute([$customerId]);
        $customerType = $stmtC->fetchColumn() ?: 'normal';
        foreach ($accounts as &$a) {
            $a['can_take'] = ($a['account_type'] === 'premium') ? ($customerType === 'premium') : true;
        }
        unset($a);

        jsonResponse(true, "搜索成功", [
            "accounts" => $accounts,
            "total_found" => count($accounts),
            "search_term" => $searchTerm
        ]);

    } catch (Exception $e) {
        error_log("Search accounts error: " . $e->getMessage());
        jsonResponse(false, "搜索失败");
    }
}

// 取用账号
function takeAccount($db, $input) {
    try {
        $sessionToken = $input['session_token'] ?? '';
        $gameAccountId = (int)($input['game_account_id'] ?? 0);
        $isSwitch = !empty($input['is_switch']);
        $customerId = validateSession($db, $sessionToken);
        if (!$customerId) { jsonResponse(false, '会话无效，请重新登录', null, 401); }
        if ($gameAccountId <= 0) { jsonResponse(false, '缺少game_account_id'); }

        // 检查客户时长与状态
        $stmt = $db->prepare('SELECT account_type, remaining_hours FROM customer_accounts WHERE id=?');
        $stmt->execute([$customerId]);
        $c = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$c) { jsonResponse(false, '客户不存在'); }
        if (($c['remaining_hours'] ?? 0) <= 0) { jsonResponse(false, '时长不足，无法取号'); }

        // 锁定要取用的账号
        $db->beginTransaction();
        $stmt = $db->prepare('SELECT * FROM game_accounts WHERE id=? FOR UPDATE');
        $stmt->execute([$gameAccountId]);
        $ga = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$ga) { $db->rollBack(); jsonResponse(false, '游戏账号不存在'); }
        if ($ga['status'] !== 'available' && !$isSwitch) { $db->rollBack(); jsonResponse(false, '该账号不可用'); }

        // 账号类型权限
        if ($ga['account_type'] === 'premium' && $c['account_type'] !== 'premium') {
            $db->rollBack(); jsonResponse(false, '普通客户无法使用高级账号');
        }

        // 若存在旧在用账号，标记并记录密码修改请求
        $stmt = $db->prepare("SELECT * FROM game_accounts WHERE status='in_use' AND current_user_id=? LIMIT 1");
        $stmt->execute([$customerId]);
        $old = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($old) {
            // 旧账号标记为 available
            $db->prepare("UPDATE game_accounts SET status='available', current_user_id=NULL, taken_at=NULL, updated_at=NOW() WHERE id=?")->execute([$old['id']]);
            // 待改密记录：pending_passwords 去重插入或更新时间
            try {
                $stmtPP = $db->prepare("SELECT id FROM pending_passwords WHERE game_id=? AND account_login=? AND old_password=? AND uploaded_by='customer_switch' LIMIT 1");
                $stmtPP->execute([$old['id'], $old['account'], $old['password']]);
                if ($stmtPP->fetchColumn()) {
                    $db->prepare("UPDATE pending_passwords SET uploaded_at=NOW() WHERE game_id=? AND account_login=? AND old_password=? AND uploaded_by='customer_switch'")
                       ->execute([$old['id'], $old['account'], $old['password']]);
                } else {
                    $db->prepare("INSERT INTO pending_passwords (game_id, account_login, old_password, uploaded_by, uploaded_at) VALUES (?,?,?,?, NOW())")
                       ->execute([$old['id'], $old['account'], $old['password'], 'customer_switch']);
                }
            } catch (Exception $e) { /* 忽略待改密记录错误 */ }
            // 同时记录到 password_change_records 以兼容新后台
            $db->prepare("INSERT INTO password_change_records (customer_id, game_account_id, old_password, status, reason, created_at) VALUES (?,?,?,?, 'switched', NOW())")
               ->execute([$customerId, $old['id'], $old['password'], 'pending']);
        }

        // 占用新账号
        $db->prepare("UPDATE game_accounts SET status='in_use', current_user_id=?, taken_at=NOW(), updated_at=NOW() WHERE id=?")
           ->execute([$customerId, $gameAccountId]);

        $db->commit();

        // 读取并返回该账号详情
        $stmt = $db->prepare('SELECT * FROM game_accounts WHERE id=?');
        $stmt->execute([$gameAccountId]);
        $ga = $stmt->fetch(PDO::FETCH_ASSOC);

        logOperation($db, 'customer', $customerId, 'take_account', '取用账号ID: '.$gameAccountId);

        jsonResponse(true, '取号成功', [ 'game_account' => $ga ]);
    } catch (Exception $e) {
        if ($db->inTransaction()) { $db->rollBack(); }
        error_log('takeAccount error: '.$e->getMessage());
        jsonResponse(false, '取号失败');
    }
}

// 释放账号
function releaseAccount($db, $input) {
    try {
        $sessionToken = $input['session_token'] ?? '';
        $gameAccountId = (int)($input['game_account_id'] ?? 0);
        $customerId = validateSession($db, $sessionToken);
        if (!$customerId) { jsonResponse(false, '会话无效，请重新登录', null, 401); }
        if ($gameAccountId <= 0) { jsonResponse(false, '缺少game_account_id'); }

        $db->beginTransaction();
        $stmt = $db->prepare('SELECT * FROM game_accounts WHERE id=? FOR UPDATE');
        $stmt->execute([$gameAccountId]);
        $ga = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$ga) { $db->rollBack(); jsonResponse(false, '游戏账号不存在'); }
        if ($ga['current_user_id'] != $customerId) { $db->rollBack(); jsonResponse(false, '无权释放该账号'); }

        // 标记为可用，并记录密码修改请求（释放后改密）
        $db->prepare("UPDATE game_accounts SET status='available', current_user_id=NULL, taken_at=NULL, updated_at=NOW() WHERE id=?")
           ->execute([$gameAccountId]);

        // pending_passwords 去重插入或更新时间
        try {
            $stmtPP = $db->prepare("SELECT id FROM pending_passwords WHERE game_id=? AND account_login=? AND old_password=? AND uploaded_by='customer_release' LIMIT 1");
            $stmtPP->execute([$gameAccountId, $ga['account'], $ga['password']]);
            if ($stmtPP->fetchColumn()) {
                $db->prepare("UPDATE pending_passwords SET uploaded_at=NOW() WHERE game_id=? AND account_login=? AND old_password=? AND uploaded_by='customer_release'")
                   ->execute([$gameAccountId, $ga['account'], $ga['password']]);
            } else {
                $db->prepare("INSERT INTO pending_passwords (game_id, account_login, old_password, uploaded_by, uploaded_at) VALUES (?,?,?,?, NOW())")
                   ->execute([$gameAccountId, $ga['account'], $ga['password'], 'customer_release']);
            }
        } catch (Exception $e) { /* 忽略 */ }

        // 兼容新后台记录
        $db->prepare("INSERT INTO password_change_records (customer_id, game_account_id, old_password, status, reason, created_at) VALUES (?,?,?,?, 'released', NOW())")
           ->execute([$customerId, $gameAccountId, $ga['password'], 'pending']);

        $db->commit();

        logOperation($db, 'customer', $customerId, 'release_account', '释放账号ID: '.$gameAccountId);

        jsonResponse(true, '释放成功');
    } catch (Exception $e) {
        if ($db->inTransaction()) { $db->rollBack(); }
        error_log('releaseAccount error: '.$e->getMessage());
        jsonResponse(false, '释放失败');
    }
}

// 获取当前账号
function getCurrentAccount($db, $input) {
    try {
        $sessionToken = $input['session_token'] ?? '';
        $customerId = validateSession($db, $sessionToken);
        if (!$customerId) { jsonResponse(false, '会话无效，请重新登录', null, 401); }

        $stmt = $db->prepare("SELECT * FROM game_accounts WHERE status='in_use' AND current_user_id=? LIMIT 1");
        $stmt->execute([$customerId]);
        $ga = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$ga) {
            jsonResponse(false, '当前没有使用中的账号');
        }

        jsonResponse(true, '获取成功', [ 'game_account' => $ga ]);
    } catch (Exception $e) {
        error_log('getCurrentAccount error: '.$e->getMessage());
        jsonResponse(false, '获取当前账号失败');
    }
}

// 获取账号详情
function getAccountDetails($db, $input) {
    try {
        $sessionToken = $input['session_token'] ?? '';
        $gameAccountId = (int)($input['game_account_id'] ?? 0);
        $customerId = validateSession($db, $sessionToken);
        if (!$customerId) { jsonResponse(false, '会话无效，请重新登录', null, 401); }
        if ($gameAccountId <= 0) { jsonResponse(false, '缺少game_account_id'); }

        $stmt = $db->prepare('SELECT * FROM game_accounts WHERE id=?');
        $stmt->execute([$gameAccountId]);
        $ga = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$ga) { jsonResponse(false, '账号不存在'); }

        jsonResponse(true, '获取成功', [ 'game_account' => $ga ]);
    } catch (Exception $e) {
        error_log('getAccountDetails error: '.$e->getMessage());
        jsonResponse(false, '获取账号详情失败');
    }
}

// 客户登出
function customerLogout($db, $input) {
    try {
        $sessionToken = $input['session_token'] ?? '';
        $clientIp = $_SERVER['REMOTE_ADDR'] ?? '';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        // 获取会话信息
        $stmt = $db->prepare("
            SELECT ca.id, ca.current_ip_address, ca.device_fingerprint
            FROM customer_accounts ca
            WHERE ca.session_token = ?
        ");
        $stmt->execute([$sessionToken]);
        $customer = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($customer) {
            // 清除客户账号表中的会话信息
            $stmt = $db->prepare("
                UPDATE customer_accounts
                SET session_token = NULL, session_expires = NULL, current_ip_address = NULL, current_user_agent = NULL
                WHERE id = ?
            ");
            $stmt->execute([$customer['id']]);

            // 标记会话为已登出
            $stmt = $db->prepare("UPDATE customer_sessions SET status = 'expired' WHERE session_token = ?");
            $stmt->execute([$sessionToken]);

            // 记录登出日志
            logDeviceAction($db, $customer['id'], $clientIp, $userAgent, $customer['device_fingerprint'], 'logout', $sessionToken);

            // 记录操作日志
            logOperation($db, 'customer', $customer['id'], 'logout', "客户登出 (IP: {$clientIp})", $clientIp);
        }

        // 清除PHP会话
        if (session_status() === PHP_SESSION_ACTIVE) {
            session_destroy();
        }

        jsonResponse(true, '已登出');
    } catch (Exception $e) {
        error_log("Customer logout error: " . $e->getMessage());
        jsonResponse(true, '已登出');
    }
}

// 检查客户会话
function checkCustomerSession($db, $input) {
    try {
        $sessionToken = $input['session_token'] ?? '';

        if (empty($sessionToken)) {
            jsonResponse(false, '无效的会话令牌');
        }

        $customerId = validateSession($db, $sessionToken);

        if (!$customerId) {
            jsonResponse(false, '会话已过期或无效');
        }

        // 获取客户信息
        $stmt = $db->prepare("
            SELECT account_number, account_type, remaining_hours, current_ip_address,
                   session_expires, last_login
            FROM customer_accounts
            WHERE id = ?
        ");
        $stmt->execute([$customerId]);
        $customer = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$customer) {
            jsonResponse(false, '客户信息不存在');
        }

        jsonResponse(true, '会话有效', [
            'customer_info' => [
                'id' => $customerId,
                'account_number' => $customer['account_number'],
                'account_type' => $customer['account_type'],
                'remaining_hours' => floatval($customer['remaining_hours']),
                'current_ip' => $customer['current_ip_address'],
                'session_expires' => $customer['session_expires'],
                'last_login' => $customer['last_login']
            ]
        ]);

    } catch (Exception $e) {
        error_log("Check session error: " . $e->getMessage());
        jsonResponse(false, '会话检查失败');
    }
}

// 获取设备信息
function getDeviceInfo($db, $input) {
    try {
        $sessionToken = $input['session_token'] ?? '';
        $customerId = validateSession($db, $sessionToken);

        if (!$customerId) {
            jsonResponse(false, '会话无效');
        }

        $clientIp = $_SERVER['REMOTE_ADDR'] ?? '';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        // 获取当前客户的所有活跃会话
        $stmt = $db->prepare("
            SELECT session_token, ip_address, user_agent, device_fingerprint,
                   last_activity, created_at, status
            FROM customer_sessions
            WHERE customer_id = ? AND status = 'active' AND expires_at > NOW()
            ORDER BY last_activity DESC
        ");
        $stmt->execute([$customerId]);
        $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 获取最近的设备登录日志
        $stmt = $db->prepare("
            SELECT ip_address, user_agent, action, created_at, reason
            FROM device_login_logs
            WHERE customer_id = ?
            ORDER BY created_at DESC
            LIMIT 10
        ");
        $stmt->execute([$customerId]);
        $loginLogs = $stmt->fetchAll(PDO::FETCH_ASSOC);

        jsonResponse(true, '设备信息获取成功', [
            'current_device' => [
                'ip_address' => $clientIp,
                'user_agent' => $userAgent,
                'device_fingerprint' => generateDeviceFingerprint($clientIp, $userAgent)
            ],
            'active_sessions' => $sessions,
            'recent_logins' => $loginLogs,
            'single_device_enabled' => getSingleDeviceLoginSetting($db)
        ]);

    } catch (Exception $e) {
        error_log("Get device info error: " . $e->getMessage());
        jsonResponse(false, '获取设备信息失败');
    }
}

// 创建测试数据
function createTestData($db, $input) {
    try {
        // 检查是否已存在测试客户账号
        $stmt = $db->prepare("SELECT id FROM customer_accounts WHERE account_number = 'test123'");
        $stmt->execute();
        $existing = $stmt->fetch();

        if (!$existing) {
            // 创建测试客户账号
            $stmt = $db->prepare("
                INSERT INTO customer_accounts (
                    account_number, account_type, duration_hours, remaining_hours,
                    status, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, NOW(), NOW())
            ");

            $stmt->execute(['test123', 'premium', 100, 50, 'active']);
        }

        // 检查游戏账号数量
        $stmt = $db->query("SELECT COUNT(*) FROM game_accounts");
        $gameAccountCount = $stmt->fetchColumn();

        if ($gameAccountCount == 0) {
            // 创建测试游戏账号
            $testAccounts = [
                ['game_test_001', 'test123456', '测试武将一', 45, 8, '王者官阶', 35, '男', 299.00, 'premium'],
                ['game_test_002', 'test789012', '测试武将二', 38, 6, '钻石官阶', 28, '女', 199.00, 'normal'],
                ['game_test_003', 'test345678', '测试武将三', 52, 10, '至尊官阶', 68, '男', 599.00, 'premium']
            ];

            foreach ($testAccounts as $account) {
                $stmt = $db->prepare("
                    INSERT INTO game_accounts (
                        account, password, account_name, level, vip_level, `rank`,
                        nation_war, gender, price, account_type, status,
                        general_count, skin_count, epic_general_count, dynamic_skin_count,
                        premium_generals, dynamic_skins, general_details, skin_details,
                        created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'available', ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                ");

                $generalCount = rand(8, 15);
                $skinCount = rand(5, 12);
                $epicGeneralCount = rand(3, 8);
                $dynamicSkinCount = rand(2, 6);

                $premiumGenerals = '["张飞", "关羽", "刘备", "赵云", "诸葛亮"]';
                $dynamicSkins = '["貂蝉-闭月", "大乔-国色", "小乔-天香"]';
                $generalDetails = '张飞,关羽,刘备,赵云,诸葛亮,司马懿,周瑜,孙权';
                $skinDetails = '貂蝉-闭月,大乔-国色,小乔-天香,甄姬-洛神';

                $stmt->execute([
                    $account[0], $account[1], $account[2], $account[3], $account[4], $account[5],
                    $account[6], $account[7], $account[8], $account[9],
                    $generalCount, $skinCount, $epicGeneralCount, $dynamicSkinCount,
                    $premiumGenerals, $dynamicSkins, $generalDetails, $skinDetails
                ]);
            }
        }

        jsonResponse(true, "测试数据创建成功");

    } catch (Exception $e) {
        error_log("创建测试数据失败: " . $e->getMessage());
        jsonResponse(false, "创建测试数据失败: " . $e->getMessage());
    }
}

// jsonResponse 函数已在 config/database.php 中定义