<?php
// 密码修改记录管理API
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

require_once '../config/database.php';

// 开始会话
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        jsonResponse(false, '数据库连接失败', null, 500);
        exit;
    }
} catch (Exception $e) {
    error_log("Database connection error: " . $e->getMessage());
    jsonResponse(false, '数据库连接错误', null, 500);
    exit;
}

// 处理请求
$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'POST') {
    try {
        $rawInput = file_get_contents('php://input');
        if (empty($rawInput)) {
            jsonResponse(false, '请求体为空');
            exit;
        }
        
        $input = json_decode($rawInput, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            jsonResponse(false, 'JSON解析错误：' . json_last_error_msg());
            exit;
        }
        
        if (!$input) {
            jsonResponse(false, '无效的请求数据');
            exit;
        }
        
        $action = $input['action'] ?? '';
        
        switch ($action) {
            case 'get_password_change_requests':
                getPasswordChangeRequests($db, $input);
                break;
            case 'process_password_change':
                processPasswordChange($db, $input);
                break;
            case 'get_request_details':
                getRequestDetails($db, $input);
                break;
            default:
                jsonResponse(false, '未知的操作：' . $action);
                break;
        }
    } catch (Exception $e) {
        error_log("Request processing error: " . $e->getMessage());
        jsonResponse(false, '请求处理错误', null, 500);
    }
} else {
    jsonResponse(false, '不支持的请求方法：' . $method);
}

/**
 * 获取密码修改请求列表
 */
function getPasswordChangeRequests($db, $input) {
    // 验证管理员权限
    if (!isset($_SESSION['admin_id'])) {
        jsonResponse(false, '需要管理员权限', null, 401);
        return;
    }
    
    try {
        $page = max(1, intval($input['page'] ?? 1));
        $limit = min(100, max(10, intval($input['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;
        
        $status = $input['status'] ?? '';
        
        // 构建查询条件
        $conditions = [];
        $params = [];
        
        if (!empty($status)) {
            $conditions[] = "pcr.status = ?";
            $params[] = $status;
        }
        
        $whereClause = empty($conditions) ? '' : 'WHERE ' . implode(' AND ', $conditions);
        
        // 获取总数
        $countSql = "SELECT COUNT(*) as total FROM password_change_requests pcr $whereClause";
        $stmt = $db->prepare($countSql);
        $stmt->execute($params);
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // 获取请求数据
        $sql = "SELECT pcr.*, 
                ga.account as game_account,
                ga.account_name as game_account_name,
                ca.account_number as customer_account,
                aa.username as admin_username
                FROM password_change_requests pcr
                LEFT JOIN game_accounts ga ON pcr.game_account_id = ga.id
                LEFT JOIN customer_accounts ca ON pcr.customer_id = ca.id
                LEFT JOIN admin_accounts aa ON pcr.admin_id = aa.id
                $whereClause
                ORDER BY pcr.created_at DESC
                LIMIT $limit OFFSET $offset";
        
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        jsonResponse(true, '获取成功', [
            'requests' => $requests,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
        
    } catch (Exception $e) {
        error_log("Get password change requests error: " . $e->getMessage());
        jsonResponse(false, '获取密码修改请求失败', null, 500);
    }
}

/**
 * 处理密码修改请求
 */
function processPasswordChange($db, $input) {
    // 验证管理员权限
    if (!isset($_SESSION['admin_id'])) {
        jsonResponse(false, '需要管理员权限', null, 401);
        return;
    }
    
    $requestId = $input['request_id'] ?? '';
    $newPassword = trim($input['new_password'] ?? '');
    $action = $input['process_action'] ?? ''; // 'complete' 或 'cancel'
    $notes = trim($input['notes'] ?? '');
    
    if (empty($requestId)) {
        jsonResponse(false, '请选择要处理的请求');
        return;
    }
    
    if ($action === 'complete' && empty($newPassword)) {
        jsonResponse(false, '请输入新密码');
        return;
    }
    
    try {
        // 开始事务
        $db->beginTransaction();
        
        // 获取请求信息
        $stmt = $db->prepare("SELECT * FROM password_change_requests WHERE id = ? AND status = 'pending'");
        $stmt->execute([$requestId]);
        $request = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$request) {
            $db->rollBack();
            jsonResponse(false, '请求不存在或已处理');
            return;
        }
        
        if ($action === 'complete') {
            // 更新游戏账号密码
            $stmt = $db->prepare("UPDATE game_accounts SET password = ? WHERE id = ?");
            $stmt->execute([$newPassword, $request['game_account_id']]);
            
            // 更新请求状态
            $stmt = $db->prepare("
                UPDATE password_change_requests 
                SET status = 'completed', admin_id = ?, processed_at = NOW(), notes = ?
                WHERE id = ?
            ");
            $stmt->execute([$_SESSION['admin_id'], $notes, $requestId]);
            
            // 记录操作日志
            logOperation($db, 'admin', $_SESSION['admin_id'], 'process_password_change', "完成密码修改请求ID：{$requestId}");
            
            $message = '密码修改完成';
        } else {
            // 取消请求
            $stmt = $db->prepare("
                UPDATE password_change_requests 
                SET status = 'cancelled', admin_id = ?, processed_at = NOW(), notes = ?
                WHERE id = ?
            ");
            $stmt->execute([$_SESSION['admin_id'], $notes, $requestId]);
            
            // 记录操作日志
            logOperation($db, 'admin', $_SESSION['admin_id'], 'cancel_password_change', "取消密码修改请求ID：{$requestId}");
            
            $message = '请求已取消';
        }
        
        $db->commit();
        
        jsonResponse(true, $message);
        
    } catch (Exception $e) {
        $db->rollBack();
        error_log("Process password change error: " . $e->getMessage());
        jsonResponse(false, '处理请求失败', null, 500);
    }
}

/**
 * 获取请求详情
 */
function getRequestDetails($db, $input) {
    // 验证管理员权限
    if (!isset($_SESSION['admin_id'])) {
        jsonResponse(false, '需要管理员权限', null, 401);
        return;
    }
    
    $requestId = $input['request_id'] ?? '';
    
    if (empty($requestId)) {
        jsonResponse(false, '请选择要查看的请求');
        return;
    }
    
    try {
        $sql = "SELECT pcr.*, 
                ga.account as game_account,
                ga.account_name as game_account_name,
                ga.level as game_level,
                ga.account_type as game_account_type,
                ca.account_number as customer_account,
                ca.account_type as customer_type,
                aa.username as admin_username
                FROM password_change_requests pcr
                LEFT JOIN game_accounts ga ON pcr.game_account_id = ga.id
                LEFT JOIN customer_accounts ca ON pcr.customer_id = ca.id
                LEFT JOIN admin_accounts aa ON pcr.admin_id = aa.id
                WHERE pcr.id = ?";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$requestId]);
        $request = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$request) {
            jsonResponse(false, '请求不存在');
            return;
        }
        
        jsonResponse(true, '获取成功', [
            'request' => $request
        ]);
        
    } catch (Exception $e) {
        error_log("Get request details error: " . $e->getMessage());
        jsonResponse(false, '获取请求详情失败', null, 500);
    }
}
?>
