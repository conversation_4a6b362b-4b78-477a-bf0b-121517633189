<?php
// 清除操作日志API
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    require_once '../config/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        echo json_encode(['success' => false, 'message' => '数据库连接失败'], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 获取清除前的记录数
    $stmt = $db->query("SELECT COUNT(*) as count FROM operation_logs");
    $beforeCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // 清空operation_logs表
    $stmt = $db->prepare("DELETE FROM operation_logs");
    $result = $stmt->execute();
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => "操作日志已清空，共删除{$beforeCount}条记录",
            'deleted_count' => $beforeCount
        ], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode(['success' => false, 'message' => '清空失败'], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'message' => '错误: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
