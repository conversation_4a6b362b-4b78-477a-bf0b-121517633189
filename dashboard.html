<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三国杀账号管理系统 - 管理后台</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎮</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #8b5cf6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-color: #e5e7eb;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --sidebar-width: 280px;
            --header-height: 70px;
            --border-radius: 12px;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.6;
        }

        /* 主容器 */
        .admin-container {
            display: flex;
            min-height: 100vh;
            background: var(--light-color);
            margin: 20px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-xl);
            overflow: hidden;
        }

        /* 侧边栏 */
        .sidebar {
            width: var(--sidebar-width);
            background: linear-gradient(180deg, var(--dark-color) 0%, #374151 100%);
            color: white;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.03)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.03)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.02)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .sidebar-header {
            padding: 24px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 700;
            margin: 0;
            background: linear-gradient(135deg, #fff 0%, #e5e7eb 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .sidebar-subtitle {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 4px;
        }

        .sidebar-nav {
            flex: 1;
            padding: 20px 0;
            position: relative;
            z-index: 1;
        }

        .nav-item {
            margin: 4px 16px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s ease;
        }

        .nav-link:hover::before {
            left: 100%;
        }

        .nav-link:hover,
        .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            transform: translateX(4px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .nav-link i {
            width: 20px;
            margin-right: 12px;
            font-size: 16px;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--light-color);
        }

        /* 顶部导航 */
        .top-nav {
            height: var(--header-height);
            background: white;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 32px;
            box-shadow: var(--shadow-sm);
        }

        .page-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0;
        }

        .top-nav-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 16px;
            background: var(--light-color);
            border-radius: 50px;
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-menu:hover {
            background: white;
            box-shadow: var(--shadow-md);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        /* 内容区域 */
        .content-area {
            flex: 1;
            padding: 32px;
            overflow-y: auto;
        }

        .content-section {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .content-section.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 卡片样式 */
        .premium-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .premium-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .card-header {
            padding: 24px 32px 0;
            border-bottom: none;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .card-body {
            padding: 24px 32px 32px;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 24px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 8px;
            line-height: 1;
        }

        .stat-label {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .stat-icon {
            position: absolute;
            top: 24px;
            right: 24px;
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            opacity: 0.8;
        }

        /* 按钮样式 */
        .btn-premium {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .btn-premium:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
            color: white;
        }

        .btn-premium:active {
            transform: translateY(0);
        }

        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .table {
            margin: 0;
            font-size: 14px;
        }

        .table thead th {
            background: var(--light-color);
            border: none;
            padding: 16px 20px;
            font-weight: 600;
            color: var(--text-primary);
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table tbody td {
            padding: 16px 20px;
            border-color: var(--border-color);
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background: rgba(99, 102, 241, 0.02);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .admin-container {
                margin: 10px;
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
            }

            .content-area {
                padding: 16px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }
        }

        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(99, 102, 241, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 徽章样式 */
        .badge {
            font-size: 11px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 6px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* 输入框样式 */
        .form-control {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        /* 模态框样式 */
        .modal-content {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-xl);
        }

        .modal-header {
            border-bottom: 1px solid var(--border-color);
            padding: 24px 32px 16px;
        }

        .modal-body {
            padding: 24px 32px;
        }

        .modal-footer {
            border-top: 1px solid var(--border-color);
            padding: 16px 32px 24px;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h1 class="sidebar-title">三国杀管理系统</h1>
                <p class="sidebar-subtitle">Account Management</p>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-item">
                    <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                        <i class="bi bi-speedometer2"></i>
                        <span>数据统计</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="#" onclick="showSection('customers')">
                        <i class="bi bi-people"></i>
                        <span>客户账号管理</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="#" onclick="showSection('gameAccounts')">
                        <i class="bi bi-joystick"></i>
                        <span>游戏账号管理</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="#" onclick="showSection('passwordManage')">
                        <i class="bi bi-key"></i>
                        <span>密码管理</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="#" onclick="showSection('logs')">
                        <i class="bi bi-journal-text"></i>
                        <span>操作日志</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="#" onclick="showSection('settings')">
                        <i class="bi bi-gear"></i>
                        <span>系统设置</span>
                    </a>
                </div>
            </nav>

            <!-- 侧边栏底部信息 -->
            <div class="mt-auto p-3 border-top" style="border-color: rgba(255, 255, 255, 0.1) !important;">
                <div class="text-center">
                    <div class="text-white fw-semibold mb-1" id="adminName">管理员</div>
                    <div class="text-white-50 small mb-2">在线时间：<span id="onlineTime">00:00:00</span></div>
                    <button class="btn btn-outline-light btn-sm" onclick="logout()">
                        <i class="bi bi-box-arrow-right me-1"></i>退出登录
                    </button>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航 -->
            <div class="top-nav">
                <h1 class="page-title" id="pageTitle">数据统计</h1>
                <div class="top-nav-actions">
                    <button class="btn btn-outline-primary btn-sm me-2" onclick="refreshData()">
                        <i class="bi bi-arrow-clockwise me-1"></i>刷新数据
                    </button>
                    <div class="user-menu" onclick="logout()">
                        <div class="user-avatar">A</div>
                        <span id="topAdminName">管理员</span>
                        <i class="bi bi-chevron-down"></i>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 数据统计面板 -->
                <div id="dashboard" class="content-section active">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="totalCustomers">0</div>
                            <div class="stat-label">客户账号总数</div>
                            <div class="stat-icon"><i class="bi bi-people"></i></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalGameAccounts">0</div>
                            <div class="stat-label">游戏账号总数</div>
                            <div class="stat-icon"><i class="bi bi-joystick"></i></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="availableGameAccounts">0</div>
                            <div class="stat-label">可用游戏账号</div>
                            <div class="stat-icon"><i class="bi bi-check-circle"></i></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="inUseGameAccounts">0</div>
                            <div class="stat-label">使用中游戏账号</div>
                            <div class="stat-icon"><i class="bi bi-play-circle"></i></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="activeCustomers">0</div>
                            <div class="stat-label">已激活客户账号</div>
                            <div class="stat-icon"><i class="bi bi-person-check"></i></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="expiredCustomers">0</div>
                            <div class="stat-label">时长到期客户</div>
                            <div class="stat-icon"><i class="bi bi-clock-history"></i></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="pendingPasswordChanges">0</div>
                            <div class="stat-label">待修改密码</div>
                            <div class="stat-icon"><i class="bi bi-key"></i></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="bannedCustomers">0</div>
                            <div class="stat-label">封禁客户账号</div>
                            <div class="stat-icon"><i class="bi bi-person-x"></i></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="premium-card">
                                <div class="card-header">
                                    <h3 class="card-title">实时操作日志</h3>
                                </div>
                                <div class="card-body">
                                    <div id="realtimeLogs">
                                        <div class="text-center">
                                            <div class="loading-spinner"></div>
                                            <p class="mt-2 text-muted">加载中...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="premium-card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h3 class="card-title mb-0">客户热搜词排行</h3>
                                    <button class="btn btn-outline-danger btn-sm" onclick="clearHotSearchWords()" title="清空热搜关键词">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div id="hotSearchWords">
                                        <div class="text-center">
                                            <div class="loading-spinner"></div>
                                            <p class="mt-2 text-muted">加载中...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="premium-card">
                                <div class="card-header">
                                    <h3 class="card-title">快速操作</h3>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-3">
                                        <button class="btn btn-premium" onclick="showSection('customers')">
                                            <i class="bi bi-plus-circle me-2"></i>生成客户账号
                                        </button>
                                        <button class="btn btn-premium" onclick="showSection('gameAccounts')">
                                            <i class="bi bi-plus-circle me-2"></i>添加游戏账号
                                        </button>
                                        <button class="btn btn-outline-primary" onclick="loadDashboardData()">
                                            <i class="bi bi-arrow-clockwise me-2"></i>刷新数据
                                        </button>
                                        <a href="/status.html" class="btn btn-outline-info">
                                            <i class="bi bi-activity me-2"></i>系统状态
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 客户账号管理 -->
                <div id="customers" class="content-section">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <button class="btn btn-premium" onclick="showGenerateModal()">
                                <i class="bi bi-plus-circle me-2"></i>生成客户账号
                            </button>
                            <button class="btn btn-outline-info ms-2" onclick="manualUpdateRemainingTime()" title="更新剩余时长">
                                <i class="bi bi-arrow-clockwise me-1"></i>更新时长
                            </button>
                        </div>
                        <div class="col-md-8">
                            <div class="input-group">
                                <input type="text" class="form-control" id="customerSearch" placeholder="搜索客户账号...">
                                <button class="btn btn-outline-secondary" onclick="searchCustomers()">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>账号</th>
                                    <th>类型</th>
                                    <th>时长</th>
                                    <th>剩余时长</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="customersTable">
                                <tr>
                                    <td colspan="7" class="text-center py-5">
                                        <div class="loading-spinner"></div>
                                        <p class="mt-2 text-muted">加载中...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <nav class="p-3" id="customersPagination"></nav>
                    </div>
                </div>

                <!-- 游戏账号管理 -->
                <div id="gameAccounts" class="content-section">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <button class="btn btn-premium" onclick="showAddGameAccountModal()">
                                <i class="bi bi-plus-circle me-2"></i>添加游戏账号
                            </button>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="gameAccountSearch" placeholder="搜索游戏账号...">
                                <button class="btn btn-outline-secondary" onclick="searchGameAccounts()">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>游戏账号</th>
                                    <th>账号名称</th>
                                    <th>等级/VIP</th>
                                    <th>官阶</th>
                                    <th>国战将池</th>
                                    <th>区域</th>
                                    <th>武将/皮肤</th>
                                    <th>史诗/动态</th>
                                    <th>价格</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="gameAccountsTable">
                                <tr>
                                    <td colspan="7" class="text-center py-5">
                                        <div class="loading-spinner"></div>
                                        <p class="mt-2 text-muted">加载中...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <nav class="p-3" id="gameAccountsPagination"></nav>
                    </div>
                </div>



                <!-- 操作日志 -->
                <div id="logs" class="content-section">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h2 class="mb-0">操作日志</h2>
                        <button class="btn btn-outline-danger btn-sm" onclick="clearOperationLogs()" title="清空操作日志">
                            <i class="bi bi-trash me-1"></i>清空日志
                        </button>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>操作时间</th>
                                    <th>操作类型</th>
                                    <th>操作用户</th>
                                    <th>操作详情</th>
                                    <th>IP地址</th>
                                </tr>
                            </thead>
                            <tbody id="logsTable">
                                <tr>
                                    <td colspan="5" class="text-center py-5">
                                        <div class="loading-spinner"></div>
                                        <p class="mt-2 text-muted">加载中...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <nav class="p-3" id="logsPagination"></nav>
                    </div>
                </div>

                <!-- 系统设置 -->
                <div id="settings" class="content-section">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="premium-card">
                                <div class="card-header">
                                    <h3 class="card-title">管理员密码</h3>
                                </div>
                                <div class="card-body">
                                    <form id="changePasswordForm">
                                        <div class="mb-3">
                                            <label class="form-label">当前密码</label>
                                            <input type="password" class="form-control" id="currentPassword" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">新密码</label>
                                            <input type="password" class="form-control" id="newPassword" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">确认新密码</label>
                                            <input type="password" class="form-control" id="confirmPassword" required>
                                        </div>
                                        <button type="button" class="btn btn-premium" onclick="changeAdminPassword()">
                                            <i class="bi bi-shield-check me-2"></i>修改密码
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="premium-card">
                                <div class="card-header">
                                    <h3 class="card-title">系统信息</h3>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="text-center">
                                                <div class="stat-number text-primary">v1.0</div>
                                                <div class="stat-label">系统版本</div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="text-center">
                                                <div class="stat-number text-success">运行中</div>
                                                <div class="stat-label">系统状态</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>





                <!-- 密码管理 -->
                <div id="passwordManage" class="content-section">
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>密码管理说明：</strong>
                                当客户时长到期、切换游戏账号或释放账号时，系统会自动将游戏账号提交到此处。
                                管理员可以手动修改密码并更新到游戏账号中。
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-primary" onclick="refreshPasswordRecords()">
                                <i class="bi bi-arrow-clockwise me-2"></i>刷新记录
                            </button>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th width="12%">游戏账号</th>
                                    <th width="18%">旧密码</th>
                                    <th width="12%">上传时间</th>
                                    <th width="12%">原因</th>
                                    <th width="18%">更改新密码</th>
                                    <th width="12%">状态</th>
                                    <th width="16%">操作</th>
                                </tr>
                            </thead>
                            <tbody id="passwordRecordsTable">
                                <tr>
                                    <td colspan="7" class="text-center py-5">
                                        <div class="loading-spinner"></div>
                                        <p class="mt-2 text-muted">加载中...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <nav class="p-3" id="passwordRecordsPagination"></nav>
                    </div>
                </div>

                <!-- 操作日志 -->
                <div id="logs" class="content-section">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>操作时间</th>
                                    <th>操作类型</th>
                                    <th>操作用户</th>
                                    <th>操作详情</th>
                                    <th>IP地址</th>
                                </tr>
                            </thead>
                            <tbody id="logsTable">
                                <tr>
                                    <td colspan="5" class="text-center py-5">
                                        <div class="loading-spinner"></div>
                                        <p class="mt-2 text-muted">加载中...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <nav class="p-3" id="logsPagination"></nav>
                    </div>
                </div>

                <!-- 系统设置 -->
                <div id="settings" class="content-section">
                    <div class="row">

                        <div class="col-md-6">
                            <div class="premium-card">
                                <div class="card-header">
                                    <h3 class="card-title">系统信息</h3>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="text-center">
                                                <div class="stat-number text-primary">v1.0</div>
                                                <div class="stat-label">系统版本</div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="text-center">
                                                <div class="stat-number text-success">运行中</div>
                                                <div class="stat-label">系统状态</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 生成客户账号模态框 -->
    <div class="modal fade" id="generateModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">生成客户账号</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="generateForm">
                        <div class="mb-3">
                            <label class="form-label">账号类型</label>
                            <select class="form-select" id="accountType">
                                <option value="normal">普通账号</option>
                                <option value="premium">高级账号</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">时长（小时）</label>
                            <input type="number" class="form-control" id="durationHours" min="1" value="24">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">生成数量</label>
                            <input type="number" class="form-control" id="generateCount" min="1" max="100" value="1">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-premium" onclick="generateAccounts()">生成账号</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加游戏账号模态框 -->
    <div class="modal fade" id="addGameAccountModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">🎮 添加游戏账号 - 详细录入</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" style="max-height: 80vh; overflow-y: auto;">
                    <form id="addGameAccountForm">
                        <!-- 基本信息区域 -->
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0"><i class="bi bi-person-circle me-2"></i>基本信息</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">游戏账号 *</label>
                                            <input type="text" class="form-control" id="gameAccount" required placeholder="请输入游戏登录账号">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">游戏密码 *</label>
                                            <input type="text" class="form-control" id="gamePassword" required placeholder="请输入游戏密码">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">账号名称</label>
                                            <input type="text" class="form-control" id="gameAccountName" placeholder="游戏内角色名称">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">官阶</label>
                                            <input type="text" class="form-control" id="gameRank" placeholder="如：王者官阶、钻石官阶等">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">国战将池数量</label>
                                            <input type="number" class="form-control" id="gameNationWar" min="0" max="999" placeholder="0" value="0">
                                            <small class="form-text text-muted">💡 国战中可用的武将数量，0表示未参与国战</small>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">性别</label>
                                            <select class="form-select" id="gameGender">
                                                <option value="未知">未知</option>
                                                <option value="男">男</option>
                                                <option value="女">女</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 等级信息区域 -->
                        <div class="card mb-4">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="bi bi-star me-2"></i>等级信息</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">等级</label>
                                            <input type="number" class="form-control" id="gameLevel" min="0" max="999" value="0" placeholder="0">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">VIP等级</label>
                                            <input type="number" class="form-control" id="gameVipLevel" min="0" max="15" value="0" placeholder="0">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">账号出售价格</label>
                                            <input type="number" class="form-control" id="gamePrice" min="0" step="0.01" value="0" placeholder="0.00">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">账号区域</label>
                                            <select class="form-select" id="gameAccountType">
                                                <option value="normal">普通区域</option>
                                                <option value="premium">高级区域</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 武将信息区域 -->
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="bi bi-people me-2"></i>武将信息</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">武将数量</label>
                                            <input type="number" class="form-control" id="gameGeneralCount" min="0" value="0" placeholder="请输入武将数量">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">武将详情</label>
                                            <textarea class="form-control" id="gameGeneralDetails" rows="4" placeholder="请输入所有武将名称，用逗号分隔，如：赵云,关羽,张飞,诸葛亮"></textarea>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">史诗武将数量</label>
                                            <input type="number" class="form-control" id="gameEpicGeneralCount" min="0" value="0" placeholder="请输入史诗武将数量">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">史诗武将详情</label>
                                            <textarea class="form-control" id="gamePremiumGenerals" rows="4" placeholder="请输入史诗武将名称，用逗号分隔，如：赵云,关羽,张飞"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 皮肤信息区域 -->
                        <div class="card mb-4">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="bi bi-palette me-2"></i>皮肤信息</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">皮肤数量</label>
                                            <input type="number" class="form-control" id="gameSkinCount" min="0" value="0" placeholder="请输入皮肤数量">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">皮肤详情</label>
                                            <textarea class="form-control" id="gameSkin" rows="4" placeholder="请输入所有皮肤名称，用逗号分隔，如：貂蝉-闭月,大乔-国色"></textarea>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">动态皮肤数量</label>
                                            <input type="number" class="form-control" id="gameDynamicSkinCount" min="0" value="0" placeholder="请输入动态皮肤数量">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">动态皮肤详情</label>
                                            <textarea class="form-control" id="gameDynamicSkins" rows="4" placeholder="请输入动态皮肤名称，用逗号分隔，如：赵云-引雷,关羽-武圣"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 提示信息 -->
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>录入提示：</strong>
                            <ul class="mb-0 mt-2">
                                <li>带 * 号的字段为必填项</li>
                                <li>详情字段请使用逗号分隔多个项目</li>
                                <li>数量字段可以手动输入具体数值</li>
                                <li>建议数量与详情内容保持一致</li>
                            </ul>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-premium" onclick="addGameAccount()">
                        <i class="bi bi-plus-circle me-2"></i>添加账号
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑游戏账号模态框 -->
    <div class="modal fade" id="editGameAccountModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">✏️ 编辑游戏账号 - 详细信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" style="max-height: 80vh; overflow-y: auto;">
                    <form id="editGameAccountForm">
                        <input type="hidden" id="editGameAccountId">

                        <!-- 基本信息区域 -->
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0"><i class="bi bi-person-circle me-2"></i>基本信息</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">游戏账号 *</label>
                                            <input type="text" class="form-control" id="editGameAccount" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">游戏密码 *</label>
                                            <input type="text" class="form-control" id="editGamePassword" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">账号名称</label>
                                            <input type="text" class="form-control" id="editGameAccountName">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">官阶</label>
                                            <input type="text" class="form-control" id="editGameRank">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">国战将池数量</label>
                                            <input type="number" class="form-control" id="editGameNationWar" min="0" max="999" placeholder="0">
                                            <small class="form-text text-muted">💡 国战中可用的武将数量，0表示未参与国战</small>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">性别</label>
                                            <select class="form-select" id="editGameGender">
                                                <option value="未知">未知</option>
                                                <option value="男">男</option>
                                                <option value="女">女</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 等级信息区域 -->
                        <div class="card mb-4">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="bi bi-star me-2"></i>等级信息</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="mb-3">
                                            <label class="form-label">等级</label>
                                            <input type="number" class="form-control" id="editGameLevel" min="0" max="999" value="0">
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="mb-3">
                                            <label class="form-label">VIP等级</label>
                                            <input type="number" class="form-control" id="editGameVipLevel" min="0" max="15" value="0">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">账号出售价格</label>
                                            <input type="number" class="form-control" id="editGamePrice" min="0" step="0.01" value="0">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">账号区域</label>
                                            <select class="form-select" id="editGameAccountType">
                                                <option value="normal">普通区域</option>
                                                <option value="premium">高级区域</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="mb-3">
                                            <label class="form-label">状态</label>
                                            <select class="form-select" id="editGameStatus">
                                                <option value="available">可用</option>
                                                <option value="in_use">使用中</option>
                                                <option value="banned">已封禁</option>
                                                <option value="reported">已举报</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 武将信息区域 -->
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="bi bi-people me-2"></i>武将信息</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">武将数量</label>
                                            <input type="number" class="form-control" id="editGameGeneralCount" min="0" value="0" placeholder="请输入武将数量">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">武将详情</label>
                                            <textarea class="form-control" id="editGameGeneralDetails" rows="4"></textarea>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">史诗武将数量</label>
                                            <input type="number" class="form-control" id="editGameEpicGeneralCount" min="0" value="0" placeholder="请输入史诗武将数量">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">史诗武将详情</label>
                                            <textarea class="form-control" id="editGamePremiumGenerals" rows="4"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 皮肤信息区域 -->
                        <div class="card mb-4">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="bi bi-palette me-2"></i>皮肤信息</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">皮肤数量</label>
                                            <input type="number" class="form-control" id="editGameSkinCount" min="0" value="0" placeholder="请输入皮肤数量">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">皮肤详情</label>
                                            <textarea class="form-control" id="editGameSkin" rows="4"></textarea>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">动态皮肤数量</label>
                                            <input type="number" class="form-control" id="editGameDynamicSkinCount" min="0" value="0" placeholder="请输入动态皮肤数量">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">动态皮肤详情</label>
                                            <textarea class="form-control" id="editGameDynamicSkins" rows="4"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-premium" onclick="updateGameAccount()">
                        <i class="bi bi-save me-2"></i>保存修改
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑客户账号模态框 -->
    <div class="modal fade" id="editCustomerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑客户账号</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editCustomerForm">
                        <input type="hidden" id="editCustomerId">
                        <div class="mb-3">
                            <label class="form-label">客户账号</label>
                            <input type="text" class="form-control" id="editCustomerAccount" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">账号类型</label>
                            <select class="form-select" id="editCustomerType">
                                <option value="normal">普通账号</option>
                                <option value="premium">高级账号</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">总时长(小时)</label>
                            <input type="number" class="form-control" id="editCustomerDuration" min="1">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">剩余时长(小时)</label>
                            <input type="number" class="form-control" id="editCustomerRemaining" min="0" step="0.1">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">账号状态</label>
                            <select class="form-select" id="editCustomerStatus">
                                <option value="active">正常</option>
                                <option value="expired">已过期</option>
                                <option value="banned">已封禁</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-premium" onclick="updateCustomer()">保存修改</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑游戏账号模态框 -->
    <div class="modal fade" id="editGameAccountModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑游戏账号</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editGameAccountForm">
                        <input type="hidden" id="editGameAccountId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">游戏账号 *</label>
                                    <input type="text" class="form-control" id="editGameAccount" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">密码 *</label>
                                    <input type="text" class="form-control" id="editGamePassword" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">账号名称</label>
                                    <input type="text" class="form-control" id="editGameAccountName">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">等级</label>
                                    <input type="number" class="form-control" id="editGameLevel" min="0">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">VIP等级</label>
                                    <input type="number" class="form-control" id="editGameVipLevel" min="0">
                                </div>
                            </div>
                        </div>


    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 游戏账号管理优化脚本 -->
    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('游戏账号管理页面加载完成');
        });

        // 更新"军衔"为"官阶"的显示
        function updateRankLabels() {
            // 更新所有军衔相关的标签文本
            const rankLabels = document.querySelectorAll('label');
            rankLabels.forEach(label => {
                if (label.textContent.includes('军衔')) {
                    label.textContent = label.textContent.replace('军衔', '官阶');
                }
            });
        }

        // 页面加载时更新标签
        document.addEventListener('DOMContentLoaded', updateRankLabels);

        // 显示添加游戏账号模态框时的处理
        function showAddGameAccountModal() {
            // 清空表单
            document.getElementById('addGameAccountForm').reset();

            // 重置所有数量字段为0
            document.getElementById('gameGeneralCount').value = 0;
            document.getElementById('gameSkinCount').value = 0;
            document.getElementById('gameEpicGeneralCount').value = 0;
            document.getElementById('gameDynamicSkinCount').value = 0;

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('addGameAccountModal'));
            modal.show();
        }

        // 添加一些演示数据的功能（可选）
        function fillDemoData() {
            if (confirm('是否填入演示数据？')) {
                document.getElementById('gameAccount').value = 'demo_account_001';
                document.getElementById('gamePassword').value = 'demo123456';
                document.getElementById('gameAccountName').value = '霸王项羽';
                document.getElementById('gameLevel').value = 35;
                document.getElementById('gameVipLevel').value = 8;
                document.getElementById('gameRank').value = '王者官阶';
                document.getElementById('gameNationWar').value = 45;
                document.getElementById('gameGender').value = '男';
                document.getElementById('gamePrice').value = 299.00;
                document.getElementById('gameAccountType').value = 'premium';

                // 填入武将和皮肤信息
                document.getElementById('gameGeneralDetails').value = '赵云,关羽,张飞,诸葛亮,司马懿,周瑜,孙权,曹操,刘备,吕布';
                document.getElementById('gameGeneralCount').value = 10;

                document.getElementById('gamePremiumGenerals').value = '赵云,关羽,张飞,诸葛亮,司马懿';
                document.getElementById('gameEpicGeneralCount').value = 5;

                document.getElementById('gameSkin').value = '貂蝉-闭月,大乔-国色,小乔-天香,甄姬-洛神,孙尚香-枭姬';
                document.getElementById('gameSkinCount').value = 5;

                document.getElementById('gameDynamicSkins').value = '赵云-引雷,关羽-武圣,张飞-咆哮';
                document.getElementById('gameDynamicSkinCount').value = 3;
            }
        }

        // 在添加游戏账号模态框中添加演示数据按钮（可选）
        document.addEventListener('DOMContentLoaded', function() {
            const addModal = document.getElementById('addGameAccountModal');
            if (addModal) {
                const modalFooter = addModal.querySelector('.modal-footer');
                if (modalFooter) {
                    const demoBtn = document.createElement('button');
                    demoBtn.type = 'button';
                    demoBtn.className = 'btn btn-outline-info me-auto';
                    demoBtn.innerHTML = '<i class="bi bi-lightbulb me-1"></i>填入演示数据';
                    demoBtn.onclick = fillDemoData;
                    modalFooter.insertBefore(demoBtn, modalFooter.firstChild);
                }
            }
        });
    </script>

    <script src="js/dashboard.js"></script>
</body>
</html>
