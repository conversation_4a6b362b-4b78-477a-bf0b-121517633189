<?php
require_once __DIR__ . '/../inc/db.php';
require_admin_login();
$pdo = db();

$action = $_GET['action'] ?? '';
function redirect_list(){ header('Location: /admin/pending_passwords.php'); exit; }

if ($_SERVER['REQUEST_METHOD']==='POST') {
    if ($action==='set_new_password') {
        $pp_id = (int)($_POST['pp_id'] ?? 0);
        $new_password = trim($_POST['new_password'] ?? '');
        if ($pp_id && $new_password!=='') {
            // Get pending record
            $stmt = $pdo->prepare('SELECT * FROM pending_passwords WHERE id=?');
            $stmt->execute([$pp_id]);
            if ($pp = $stmt->fetch()) {
                // Update game password
                $pdo->prepare('UPDATE game_accounts SET password=?, updated_at=NOW() WHERE id=?')->execute([$new_password, $pp['game_id']]);
                // Remove pending record
                $pdo->prepare('DELETE FROM pending_passwords WHERE id=?')->execute([$pp_id]);
                log_event('admin', $_SESSION['admin_id'], 'password_updated', ['game_id'=>$pp['game_id']]);
            }
        }
        redirect_list();
    }
}

$pp = $pdo->query('SELECT pp.*, ga.account_name AS name FROM pending_passwords pp JOIN game_accounts ga ON ga.id=pp.game_id ORDER BY pp.uploaded_at DESC')->fetchAll();
?>
<!doctype html>
<html lang="zh-CN">
<head>
<meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1">
<title>待修改密码列表</title>
<style>
body{font-family:system-ui,-apple-system,Segoe UI,Roboto,Helvetica,Arial; margin:0; background:#f7f8fb}
.top{background:#fff; padding:12px 16px; display:flex; justify-content:space-between; align-items:center; box-shadow:0 2px 12px rgba(0,0,0,.05)}
.container{padding:16px}
.table{width:100%; border-collapse:collapse}
.table th,.table td{padding:8px; border-bottom:1px solid #eee; text-align:left}
input{padding:6px 8px; border:1px solid #dcdfe6; border-radius:6px}
.btn{padding:6px 10px; border:0; background:#2d74ff; color:#fff; border-radius:6px; cursor:pointer}
</style>
</head>
<body>
  <div class="top">
    <div>待修改密码列表</div>
    <div><a class="a" href="/admin/dashboard.php">返回面板</a></div>
  </div>
  <div class="container">
    <table class="table">
      <tr><th>ID</th><th>游戏ID</th><th>账号</th><th>旧密码</th><th>上传来源</th><th>上传时间</th><th>新密码</th><th>操作</th></tr>
      <?php foreach ($pp as $r): ?>
        <tr>
          <td><?=$r['id']?></td>
          <td><?=$r['game_id']?></td>
          <td><?=$r['account_login']?></td>
          <td><?=htmlspecialchars($r['old_password'],ENT_QUOTES,'UTF-8')?></td>
          <td><?=$r['uploaded_by']?></td>
          <td><?=$r['uploaded_at']?></td>
          <td>
            <form method="post" action="?action=set_new_password">
              <input type="hidden" name="pp_id" value="<?=$r['id']?>" />
              <input name="new_password" placeholder="新密码" required />
              <button class="btn" type="submit">保存</button>
            </form>
          </td>
          <td></td>
        </tr>
      <?php endforeach; ?>
    </table>
  </div>
</body>
</html>

