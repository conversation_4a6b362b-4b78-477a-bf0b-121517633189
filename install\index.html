<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服话术管理系统 - 一键安装</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .install-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 600px;
            overflow: hidden;
        }
        
        .install-header {
            background: #4361ee;
            color: white;
            text-align: center;
            padding: 30px;
        }
        
        .install-header h1 {
            font-size: 2.2rem;
            margin-bottom: 10px;
        }
        
        .install-header p {
            opacity: 0.9;
        }
        
        .install-body {
            padding: 30px;
        }
        
        .prerequisites {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .prerequisites h3 {
            color: #4361ee;
            margin-bottom: 15px;
        }
        
        .prerequisites ul {
            list-style: none;
        }
        
        .prerequisites li {
            padding: 8px 0;
            display: flex;
            align-items: center;
        }
        
        .prerequisites li::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .database-info {
            background: #e8f4fd;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            border-left: 4px solid #4361ee;
        }
        
        .database-info h3 {
            color: #4361ee;
            margin-bottom: 15px;
        }
        
        .info-item {
            margin-bottom: 8px;
            display: flex;
        }
        
        .info-label {
            font-weight: 600;
            min-width: 120px;
            color: #495057;
        }
        
        .info-value {
            color: #212529;
        }
        
        .install-btn {
            background: #4361ee;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .install-btn:hover {
            background: #3a56d4;
            transform: translateY(-2px);
        }
        
        .install-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .progress-container {
            margin-top: 20px;
            display: none;
        }
        
        .progress-bar {
            height: 10px;
            background: #e9ecef;
            border-radius: 5px;
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .progress-fill {
            height: 100%;
            background: #4361ee;
            border-radius: 5px;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .status-message {
            text-align: center;
            color: #495057;
            font-weight: 500;
        }
        
        .status-success {
            color: #28a745;
            font-weight: 600;
        }
        
        .status-error {
            color: #dc3545;
            font-weight: 600;
        }
        
        .step-list {
            margin-top: 20px;
            display: none;
        }
        
        .step-item {
            padding: 10px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .step-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            font-size: 12px;
            font-weight: bold;
        }
        
        .step-pending {
            background: #e9ecef;
            color: #6c757d;
        }
        
        .step-success {
            background: #d4edda;
            color: #155724;
        }
        
        .step-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .login-info {
            background: #d4edda;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            display: none;
        }
        
        .login-info h3 {
            color: #155724;
            margin-bottom: 15px;
        }
        
        .login-details {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
        }
        
        .login-item {
            display: flex;
            margin-bottom: 8px;
        }
        
        .login-label {
            font-weight: 600;
            min-width: 100px;
            color: #495057;
        }
        
        .login-value {
            color: #212529;
        }
        
        .note {
            background: #fff3cd;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #ffc107;
        }
        
        .note h4 {
            color: #856404;
            margin-bottom: 8px;
        }
        
        .note p {
            color: #856404;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1>客服话术管理系统</h1>
            <p>一键安装向导</p>
        </div>
        
        <div class="install-body">
            <div class="prerequisites">
                <h3>安装前提条件</h3>
                <ul>
                    <li>MySQL 5.7+ 数据库服务器</li>
                    <li>PHP 7.4+ 运行环境</li>
                    <li>Apache/Nginx Web服务器</li>
                    <li>数据库操作权限</li>
                </ul>
            </div>
            
            <div class="database-config">
                <h3>数据库配置</h3>
                <div class="info-item">
                    <span class="info-label">MySQL 主机:</span>
                    <input type="text" id="db-host" value="localhost" placeholder="localhost" style="border: 1px solid #ccc; padding: 5px; border-radius: 4px; width: 200px;">
                </div>
                <div class="info-item">
                    <span class="info-label">MySQL 用户:</span>
                    <input type="text" id="db-user" value="root" placeholder="root" style="border: 1px solid #ccc; padding: 5px; border-radius: 4px; width: 200px;">
                </div>
                <div class="info-item">
                    <span class="info-label">MySQL 密码:</span>
                    <input type="password" id="db-password" placeholder="输入密码" style="border: 1px solid #ccc; padding: 5px; border-radius: 4px; width: 200px;">
                </div>
            </div>
            
            <button class="install-btn" id="install-btn">
                <i class="fas fa-cogs"></i>
                开始安装系统
            </button>
            
            <div class="progress-container" id="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div class="status-message" id="status-message">准备开始安装...</div>
            </div>
            
            <div class="step-list" id="step-list">
                <div class="step-item" id="step-db-connection">
                    <div class="step-icon step-pending">1</div>
                    <span>检查数据库连接</span>
                </div>
                <div class="step-item" id="step-db-create">
                    <div class="step-icon step-pending">2</div>
                    <span>创建数据库</span>
                </div>
                <div class="step-item" id="step-tables-create">
                    <div class="step-icon step-pending">3</div>
                    <span>创建数据表</span>
                </div>
                <div class="step-item" id="step-data-insert">
                    <div class="step-icon step-pending">4</div>
                    <span>插入初始数据</span>
                </div>
                <div class="step-item" id="step-user-create">
                    <div class="step-icon step-pending">5</div>
                    <span>创建数据库用户</span>
                </div>
                <div class="step-item" id="step-config-update">
                    <div class="step-icon step-pending">6</div>
                    <span>更新配置文件</span>
                </div>
            </div>
            
            <div class="login-info" id="login-info">
                <h3>安装成功！</h3>
                <p>系统已成功安装，请使用以下账号登录：</p>
                <div class="login-details">
                    <div class="login-item">
                        <span class="login-label">管理员账号:</span>
                        <span class="login-value">admin</span>
                    </div>
                    <div class="login-item">
                        <span class="login-label">初始密码:</span>
                        <span class="login-value">admin123</span>
                    </div>
                    <div class="login-item">
                        <span class="login-label">登录地址:</span>
                        <span class="login-value" id="login-url"></span>
                    </div>
                </div>
            </div>
            
            <div class="note">
                <h4>重要提示</h4>
                <p>安装完成后，请立即登录系统并修改管理员密码以确保安全。建议定期备份数据库。</p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const installBtn = document.getElementById('install-btn');
            const progressContainer = document.getElementById('progress-container');
            const progressFill = document.getElementById('progress-fill');
            const statusMessage = document.getElementById('status-message');
            const stepList = document.getElementById('step-list');
            const loginInfo = document.getElementById('login-info');
            const loginUrl = document.getElementById('login-url');
            
            // 设置登录URL
            const baseUrl = window.location.href.split('/install/')[0];
            loginUrl.textContent = baseUrl + '/frontend/';
            
            // 安装步骤状态
            const steps = {
                'step-db-connection': '检查数据库连接',
                'step-db-create': '创建数据库',
                'step-tables-create': '创建数据表',
                'step-data-insert': '插入初始数据',
                'step-user-create': '创建数据库用户',
                'step-config-update': '更新配置文件'
            };
            
            // 更新步骤状态
            function updateStep(stepId, status) {
                const stepElement = document.getElementById(stepId);
                const iconElement = stepElement.querySelector('.step-icon');
                
                iconElement.className = 'step-icon';
                if (status === 'success') {
                    iconElement.classList.add('step-success');
                    iconElement.innerHTML = '✓';
                } else if (status === 'error') {
                    iconElement.classList.add('step-error');
                    iconElement.innerHTML = '✗';
                } else {
                    iconElement.classList.add('step-pending');
                    iconElement.innerHTML = Object.keys(steps).indexOf(stepId) + 1;
                }
            }
            
            // 更新进度条
            function updateProgress(percentage, message) {
                progressFill.style.width = percentage + '%';
                statusMessage.textContent = message;
                
                if (percentage === 100) {
                    statusMessage.className = 'status-message status-success';
                } else if (message.includes('错误')) {
                    statusMessage.className = 'status-message status-error';
                }
            }
            
            // 执行安装
            installBtn.addEventListener('click', async function() {
                installBtn.disabled = true;
                installBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 安装中...';
                progressContainer.style.display = 'block';
                stepList.style.display = 'block';
                
                try {
                    // 初始化所有步骤为pending
                    Object.keys(steps).forEach(stepId => {
                        updateStep(stepId, 'pending');
                    });
                    
                    updateProgress(0, '开始安装过程...');
                    
                    // 获取数据库配置
                    const dbHost = document.getElementById('db-host').value;
                    const dbUser = document.getElementById('db-user').value;
                    const dbPassword = document.getElementById('db-password').value;
                    
                    // 发送安装请求
                    const response = await fetch('install.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `action=install&db_host=${encodeURIComponent(dbHost)}&db_user=${encodeURIComponent(dbUser)}&db_password=${encodeURIComponent(dbPassword)}`
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        // 更新所有步骤状态
                        if (result.status.database_connection) {
                            updateStep('step-db-connection', 'success');
                            updateProgress(20, '数据库连接成功');
                        }
                        
                        if (result.status.database_created) {
                            updateStep('step-db-create', 'success');
                            updateProgress(30, '数据库创建成功');
                        }
                        
                        if (result.status.tables_created) {
                            updateStep('step-tables-create', 'success');
                            updateProgress(50, '数据表创建成功');
                        }
                        
                        if (result.status.initial_data_inserted) {
                            updateStep('step-data-insert', 'success');
                            updateProgress(70, '初始数据插入成功');
                        }
                        
                        if (result.status.config_updated) {
                            updateStep('step-config-update', 'success');
                            updateProgress(90, '配置文件更新成功');
                        }
                        
                        if (result.status.complete) {
                            updateProgress(100, '系统安装成功！');
                            loginInfo.style.display = 'block';
                            
                            // 添加前往登录页的按钮
                            const goToLoginBtn = document.createElement('button');
                            goToLoginBtn.className = 'install-btn';
                            goToLoginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> 前往登录页面';
                            goToLoginBtn.onclick = function() {
                                window.location.href = baseUrl + '/frontend/';
                            };
                            installBtn.parentNode.replaceChild(goToLoginBtn, installBtn);
                        }
                    } else {
                        // 处理安装错误
                        updateProgress(0, '安装失败: ' + (result.errors && result.errors.length > 0 ? result.errors[0] : '未知错误'));
                        statusMessage.className = 'status-message status-error';
                        
                        // 启用重新安装按钮
                        installBtn.disabled = false;
                        installBtn.innerHTML = '<i class="fas fa-redo"></i> 重新安装';
                    }
                    
                } catch (error) {
                    console.error('安装错误:', error);
                    updateProgress(0, '安装过程中发生错误: ' + error.message);
                    statusMessage.className = 'status-message status-error';
                    
                    // 启用重新安装按钮
                    installBtn.disabled = false;
                    installBtn.innerHTML = '<i class="fas fa-redo"></i> 重新安装';
                }
            });
        });
    </script>
    
    <!-- Font Awesome 图标 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
</body>
</html>
