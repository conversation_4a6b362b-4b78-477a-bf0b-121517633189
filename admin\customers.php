<?php
require_once __DIR__ . '/../inc/db.php';
require_admin_login();

$pdo = db();
$action = $_GET['action'] ?? '';

function redirect_list(){ header('Location: /admin/customers.php'); exit; }

if ($_SERVER['REQUEST_METHOD']==='POST') {
    if ($action==='create') {
        $username = trim($_POST['username'] ?? '');
        $hours = (float)($_POST['hours'] ?? 1);
        $remark = trim($_POST['remark'] ?? '');
        if ($username==='') { $err='账号不能为空'; }
        else {
            // 生成8位数字账号（若用户输入非8位数字，则忽略用户输入，自动生成）
            $account_number = preg_match('/^\d{8}$/', $username) ? $username : str_pad(mt_rand(0, ********), 8, '0', STR_PAD_LEFT);
            // 新模型：customer_accounts
            $stmt = $pdo->prepare('INSERT INTO customer_accounts (account_number, account_type, duration_hours, remaining_hours, status, ban_reason, remark, created_at) VALUES (?,?,?,?,?,?,?,NOW())');
            $stmt->execute([$account_number, 'normal', $hours, $hours, 'active', null, $remark]);
            log_event('admin', $_SESSION['admin_id'], 'customer_create', ['account_number'=>$account_number,'hours'=>$hours]);
        }
        redirect_list();
    }
    if ($action==='update') {
        $id = (int)($_POST['id'] ?? 0);
        $remark = trim($_POST['remark'] ?? '');
        $hours_extend = (float)($_POST['extend_hours'] ?? 0);
        $stmt = $pdo->prepare('SELECT * FROM customer_accounts WHERE id=?');
        $stmt->execute([$id]);
        if ($c = $stmt->fetch()) {
            if ($hours_extend>0) {
                $new_remaining = ($c['remaining_hours'] ?? 0) + $hours_extend;
                $stmt2 = $pdo->prepare('UPDATE customer_accounts SET remark=?, duration_hours=duration_hours+?, remaining_hours=?, updated_at=NOW() WHERE id=?');
                $stmt2->execute([$remark, $hours_extend, $new_remaining, $id]);
            } else {
                $stmt2 = $pdo->prepare('UPDATE customer_accounts SET remark=?, updated_at=NOW() WHERE id=?');
                $stmt2->execute([$remark, $id]);
            }
            log_event('admin', $_SESSION['admin_id'], 'customer_update', ['id'=>$id,'extend'=>$hours_extend]);
        }
        redirect_list();
    }
    if ($action==='ban') {
        $id = (int)($_POST['id'] ?? 0);
        $reason = trim($_POST['reason'] ?? '');
        $stmt = $pdo->prepare("UPDATE customer_accounts SET status='banned', ban_reason=?, updated_at=NOW() WHERE id=?");
        $stmt->execute([$reason, $id]);
        log_event('admin', $_SESSION['admin_id'], 'customer_ban', ['id'=>$id,'reason'=>$reason]);
        redirect_list();
    }
}

if ($action==='delete' && isset($_GET['id'])) {
    $id = (int)$_GET['id'];
    $pdo->prepare('DELETE FROM customer_accounts WHERE id=?')->execute([$id]);
    log_event('admin', $_SESSION['admin_id'], 'customer_delete', ['id'=>$id]);
    redirect_list();
}

$customers = $pdo->query("SELECT * FROM customer_accounts ORDER BY id DESC")->fetchAll();

$hour_options = [0.5,1,2,3,4,5,6,12,24,48,72,168,360,720];
?>
<!doctype html>
<html lang="zh-CN">
<head>
<meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1">
<title>客户账号管理</title>
<style>
body{font-family:system-ui,-apple-system,Segoe UI,Roboto,Helvetica,Arial; margin:0; background:#f7f8fb}
.top{background:#fff; padding:12px 16px; display:flex; justify-content:space-between; align-items:center; box-shadow:0 2px 12px rgba(0,0,0,.05)}
.container{padding:16px}
.table{width:100%; border-collapse:collapse}
.table th,.table td{padding:8px; border-bottom:1px solid #eee; text-align:left}
input,select,textarea{padding:6px 8px; border:1px solid #dcdfe6; border-radius:6px}
.btn{padding:6px 10px; border:0; background:#2d74ff; color:#fff; border-radius:6px; cursor:pointer}
.a{color:#2d74ff; text-decoration:none}
</style>
</head>
<body>
  <div class="top">
    <div>客户账号管理</div>
    <div><a class="a" href="/admin/dashboard.php">返回面板</a></div>
  </div>
  <div class="container">
    <h3>新增客户</h3>
    <form method="post" action="?action=create">
      账号：<input name="username" required />
      时长：
      <select name="hours">
        <?php foreach ($hour_options as $h): ?>
          <option value="<?=$h?>"><?=$h?> 小时</option>
        <?php endforeach; ?>
      </select>
      备注：<input name="remark" />
      <button class="btn" type="submit">添加</button>
    </form>

    <h3 style="margin-top:16px">客户列表</h3>
    <table class="table">
      <tr><th>ID</th><th>账号</th><th>到期时间</th><th>封禁</th><th>封禁原因</th><th>备注</th><th>操作</th></tr>
      <?php foreach ($customers as $c): ?>
        <tr>
          <td><?=$c['id']?></td>
          <td><?=$c['username']?></td>
          <td><?=$c['expire_at']?></td>
          <td><?=$c['banned']? '是':'否'?></td>
          <td><?=htmlspecialchars($c['ban_reason']??'',ENT_QUOTES,'UTF-8')?></td>
          <td><?=htmlspecialchars($c['remark']??'',ENT_QUOTES,'UTF-8')?></td>
          <td>
            <form method="post" action="?action=update" style="display:inline">
              <input type="hidden" name="id" value="<?=$c['id']?>" />
              备注：<input name="remark" value="<?=htmlspecialchars($c['remark']??'',ENT_QUOTES,'UTF-8')?>" /> 延时：
              <select name="extend_hours">
                <option value="0">不延时</option>
                <?php foreach ($hour_options as $h): ?>
                  <option value="<?=$h?>"><?=$h?> 小时</option>
                <?php endforeach; ?>
              </select>
              <button class="btn" type="submit">保存</button>
            </form>
            <form method="post" action="?action=ban" style="display:inline">
              <input type="hidden" name="id" value="<?=$c['id']?>" />
              理由：<input name="reason" placeholder="封禁理由" />
              <button class="btn" type="submit">封禁</button>
            </form>
            <a class="a" href="?action=delete&id=<?=$c['id']?>" onclick="return confirm('确认删除?')">删除</a>
          </td>
        </tr>
      <?php endforeach; ?>
    </table>
  </div>
</body>
</html>

