<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三国杀账号查询系统 - 客户登录</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎮</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', Robot<PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 48px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 450px;
            width: 100%;
            margin: 20px;
        }
        
        .logo-section {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .logo-icon {
            font-size: 4rem;
            margin-bottom: 16px;
            display: block;
        }
        
        .logo-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 8px;
        }
        
        .logo-subtitle {
            color: #718096;
            font-size: 0.95rem;
        }
        
        .form-floating {
            margin-bottom: 20px;
        }
        
        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 12px;
            padding: 16px;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            margin-top: 20px;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .btn-login:disabled {
            opacity: 0.7;
            transform: none;
            box-shadow: none;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            padding: 16px;
            margin-bottom: 20px;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #fed7d7, #feb2b2);
            color: #742a2a;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #c6f6d5, #9ae6b4);
            color: #22543d;
        }
        
        .features-section {
            margin-top: 32px;
            padding-top: 32px;
            border-top: 1px solid #e2e8f0;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            color: #4a5568;
            font-size: 0.9rem;
        }
        
        .feature-icon {
            color: #667eea;
            margin-right: 12px;
            font-size: 1.1rem;
        }
        
        .footer-links {
            text-align: center;
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #e2e8f0;
        }
        
        .footer-links a {
            color: #667eea;
            text-decoration: none;
            font-size: 0.9rem;
            margin: 0 12px;
        }
        
        .footer-links a:hover {
            text-decoration: underline;
        }
        
        .account-type-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 8px;
        }
        
        .badge-premium {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #744210;
        }
        
        .badge-normal {
            background: linear-gradient(135deg, #e2e8f0, #cbd5e0);
            color: #2d3748;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo-section">
            <span class="logo-icon">🎮</span>
            <h1 class="logo-title">三国杀账号查询</h1>
            <p class="logo-subtitle">请输入您的客户账号登录</p>
        </div>
        
        <div id="alertContainer"></div>
        
        <form id="loginForm">
            <div class="form-floating">
                <input type="text" class="form-control" id="customerAccount" placeholder="客户账号" required>
                <label for="customerAccount">
                    <i class="bi bi-person me-2"></i>客户账号
                </label>
            </div>
            
            <button type="submit" class="btn btn-login" id="loginBtn">
                <i class="bi bi-box-arrow-in-right me-2"></i>登录系统
            </button>
        </form>
        
        <div class="features-section">
            <div class="feature-item">
                <i class="bi bi-shield-check feature-icon"></i>
                <span>安全可靠的账号管理</span>
            </div>
            <div class="feature-item">
                <i class="bi bi-clock feature-icon"></i>
                <span>实时查看剩余时长</span>
            </div>
            <div class="feature-item">
                <i class="bi bi-search feature-icon"></i>
                <span>智能搜索游戏账号</span>
            </div>
            <div class="feature-item">
                <i class="bi bi-controller feature-icon"></i>
                <span>一键取用游戏账号</span>
            </div>
        </div>
        
        <div class="footer-links">
            <a href="/user-interface.html">旧版查询</a>
            <a href="/index.php">系统首页</a>
            <a href="#" onclick="showHelp()">使用帮助</a>
        </div>
    </div>

    <!-- 帮助模态框 -->
    <div class="modal fade" id="helpModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-question-circle me-2"></i>使用帮助
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="bi bi-1-circle me-2"></i>登录系统</h6>
                            <p class="text-muted">输入您的客户账号（8位数字）进行登录</p>
                            
                            <h6><i class="bi bi-2-circle me-2"></i>选择账号</h6>
                            <p class="text-muted">登录后可以查看可用的游戏账号，支持搜索功能</p>
                            
                            <h6><i class="bi bi-3-circle me-2"></i>取用账号</h6>
                            <p class="text-muted">点击"取号"按钮获取游戏账号和密码</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="bi bi-star me-2"></i>账号类型说明</h6>
                            <div class="mb-2">
                                <span class="account-type-badge badge-premium">高级账号</span>
                                <small class="text-muted d-block">可使用所有游戏账号</small>
                            </div>
                            <div class="mb-3">
                                <span class="account-type-badge badge-normal">普通账号</span>
                                <small class="text-muted d-block">仅可使用普通游戏账号</small>
                            </div>
                            
                            <h6><i class="bi bi-search me-2"></i>搜索功能</h6>
                            <p class="text-muted">可以按武将名称、皮肤等条件搜索游戏账号</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeLoginPage();
        });

        /**
         * 初始化登录页面
         */
        function initializeLoginPage() {
            const loginForm = document.getElementById('loginForm');
            loginForm.addEventListener('submit', handleLogin);
            
            // 检查是否已经登录
            const sessionToken = localStorage.getItem('customer_session_token');
            if (sessionToken) {
                // 验证会话是否有效
                validateSession(sessionToken);
            }
        }

        /**
         * 处理登录表单提交
         */
        async function handleLogin(event) {
            event.preventDefault();
            
            const customerAccount = document.getElementById('customerAccount').value.trim();
            const loginBtn = document.getElementById('loginBtn');
            
            if (!customerAccount) {
                showAlert('danger', '请输入客户账号');
                return;
            }
            
            // 显示加载状态
            loginBtn.disabled = true;
            loginBtn.innerHTML = '<span class="loading-spinner"></span>登录中...';
            
            try {
                console.log('发送登录请求:', customerAccount);

                const response = await fetch('api/customer_new.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'login',
                        customer_account: customerAccount
                    })
                });

                console.log('响应状态:', response.status);

                if (!response.ok) {
                    const text = await response.text();
                    console.error('错误响应正文:', text);
                    throw new Error(`HTTP错误: ${response.status}`);
                }

                const result = await response.json();
                console.log('解析后的结果:', result);

                if (result.success) {
                    // 保存会话信息
                    localStorage.setItem('customer_session_token', result.data.session_token);
                    localStorage.setItem('customer_info', JSON.stringify({
                        id: result.data.customer_id,
                        account_number: result.data.account_number,
                        account_type: result.data.account_type,
                        duration_hours: result.data.duration_hours,
                        first_login_time: result.data.first_login_time,
                        remaining_hours: result.data.remaining_hours
                    }));

                    showAlert('success', '登录成功，正在跳转...');

                    // 跳转到取号面板
                    setTimeout(() => {
                        window.location.href = 'customer-dashboard.html';
                    }, 800);
                } else {
                    showAlert('danger', result.message || '登录失败');
                }
            } catch (error) {
                console.error('登录失败详细信息:', error);
                showAlert('danger', `网络错误: ${error.message}`);
            } finally {
                // 恢复按钮状态
                loginBtn.disabled = false;
                loginBtn.innerHTML = '<i class="bi bi-box-arrow-in-right me-2"></i>登录系统';
            }
        }

        /**
         * 验证会话
         */
        async function validateSession(sessionToken) {
            try {
                const response = await fetch('/api/customer_new.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'get_profile',
                        session_token: sessionToken
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // 会话有效，直接跳转
                    window.location.href = '/customer-dashboard.html';
                } else {
                    // 会话无效，清除本地存储
                    localStorage.removeItem('customer_session_token');
                    localStorage.removeItem('customer_info');
                }
            } catch (error) {
                console.error('验证会话失败:', error);
                // 清除可能无效的会话信息
                localStorage.removeItem('customer_session_token');
                localStorage.removeItem('customer_info');
            }
        }

        /**
         * 显示帮助
         */
        function showHelp() {
            new bootstrap.Modal(document.getElementById('helpModal')).show();
        }

        /**
         * 显示提示信息
         */
        function showAlert(type, message) {
            const alertContainer = document.getElementById('alertContainer');
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            alertContainer.innerHTML = '';
            alertContainer.appendChild(alertDiv);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 3000);
        }
    </script>
</body>
</html>
