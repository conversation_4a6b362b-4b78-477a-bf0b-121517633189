<?php
// Optional cron to auto-handle expiry: when customer expired, their in_use games are released and added to pending_passwords
require_once __DIR__ . '/../inc/db.php';
$pdo = db();

$pdo->beginTransaction();
try {
    // Find expired customers' in_use games
    $stmt = $pdo->query("SELECT g.* FROM games g JOIN customers c ON c.id=g.assigned_customer_id WHERE g.status='in_use' AND c.expire_at <= NOW() FOR UPDATE");
    $games = $stmt->fetchAll();
    foreach ($games as $g) {
        $stmt2 = $pdo->prepare('INSERT INTO pending_passwords (game_id, account_login, old_password, uploaded_by, uploaded_at) VALUES (?,?,?,?,NOW()) ON DUPLICATE KEY UPDATE uploaded_at=NOW()');
        $stmt2->execute([$g['id'], $g['account_login'], $g['password'], 'system']);
        $pdo->prepare("UPDATE games SET status='available', assigned_customer_id=NULL, assigned_at=NULL WHERE id=?")->execute([$g['id']]);
        log_event('system', null, 'auto_release_on_expiry', ['game_id'=>$g['id']]);
    }
    $pdo->commit();
    echo "handled: ".count($games)."\n";
} catch (Throwable $e) { $pdo->rollBack(); echo 'error: '.$e->getMessage(); }

