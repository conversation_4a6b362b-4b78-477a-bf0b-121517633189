-- 简化版数据库迁移脚本
-- 复制下面的内容到phpMyAdmin的SQL执行框中

-- 第一步：为客户账号表添加新字段
ALTER TABLE customer_accounts 
ADD COLUMN IF NOT EXISTS current_ip_address VARCHAR(45) NULL COMMENT '当前登录IP地址',
ADD COLUMN IF NOT EXISTS current_user_agent TEXT NULL COMMENT '当前设备信息',
ADD COLUMN IF NOT EXISTS device_fingerprint VARCHAR(255) NULL COMMENT '设备指纹';

-- 第二步：创建会话管理表
CREATE TABLE IF NOT EXISTS customer_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    session_token VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    device_fingerprint VARCHAR(255) NULL,
    status ENUM('active', 'expired', 'kicked') DEFAULT 'active',
    expires_at DATETIME NOT NULL,
    last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_session_token (session_token),
    INDEX idx_customer_id (customer_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 第三步：创建设备登录日志表
CREATE TABLE IF NOT EXISTS device_login_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    device_fingerprint VARCHAR(255) NULL,
    action ENUM('login', 'logout', 'kicked') NOT NULL,
    session_token VARCHAR(255) NULL,
    reason VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_customer_id (customer_id),
    INDEX idx_ip_address (ip_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 第四步：添加系统配置
INSERT IGNORE INTO system_config (config_key, config_value, description) VALUES
('single_device_login', '1', '是否启用单设备登录'),
('session_timeout_hours', '2', '会话超时时间（小时）');

-- 完成提示
SELECT '数据库迁移完成！单设备登录功能已启用。' as status;
