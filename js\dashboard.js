// 全局变量
let dashboardData = {};
let refreshInterval;
let onlineStartTime = new Date();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查管理员登录状态
    if (!checkAdminSession()) {
        window.location.href = '/admin-login.html';
        return;
    }

    loadDashboardData();
    loadRealtimeLogs();
    loadHotSearchWords();
    startAutoRefresh();
    startOnlineTimer();
});

/**
 * 检查管理员会话
 */
function checkAdminSession() {
    const sessionData = localStorage.getItem('admin_session') || sessionStorage.getItem('admin_session');
    
    if (!sessionData) {
        return false;
    }
    
    try {
        const session = JSON.parse(sessionData);
        const loginTime = new Date(session.login_time);
        const now = new Date();
        const hoursDiff = (now - loginTime) / (1000 * 60 * 60);
        
        // 检查会话是否过期（7天）
        if (hoursDiff >= 168) {
            localStorage.removeItem('admin_session');
            sessionStorage.removeItem('admin_session');
            return false;
        }
        
        // 更新管理员信息显示
        updateAdminInfo(session);
        return true;
    } catch (error) {
        console.error('会话验证失败:', error);
        localStorage.removeItem('admin_session');
        sessionStorage.removeItem('admin_session');
        return false;
    }
}

/**
 * 更新管理员信息显示
 */
function updateAdminInfo(session) {
    const adminName = session.username || 'admin';
    const adminNameElement = document.getElementById('adminName');
    const topAdminNameElement = document.getElementById('topAdminName');

    if (adminNameElement) {
        adminNameElement.textContent = adminName;
    }
    if (topAdminNameElement) {
        topAdminNameElement.textContent = adminName;
    }
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('admin_session');
        sessionStorage.removeItem('admin_session');
        window.location.href = '/admin-login.html';
    }
}

// 管理员登出（别名）
function adminLogout() {
    logout();
}



/**
 * 开始在线时间计时器
 */
function startOnlineTimer() {
    setInterval(() => {
        const now = new Date();
        const diff = now - onlineStartTime;
        
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);
        
        const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        document.getElementById('onlineTime').textContent = timeString;
    }, 1000);
}

// 加载仪表板数据
async function loadDashboardData() {
    try {
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_dashboard_stats'
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            dashboardData = result.data;
            updateDashboardStats();
        } else {
            console.warn('Dashboard data load failed:', result.message);
            // 使用默认数据，不显示错误提示
            dashboardData = {
                total_customers: 0,
                total_game_accounts: 0,
                available_game_accounts: 0,
                used_game_accounts: 0,
                pending_password_changes: 0,
                expiring_soon_customers: 0,
                active_customers: 0
            };
            updateDashboardStats();
        }
    } catch (error) {
        console.error('Load dashboard data failed:', error);

        // 使用模拟数据，不显示错误提示
        dashboardData = {
            total_customers: 0,
            total_game_accounts: 0,
            available_game_accounts: 0,
            used_game_accounts: 0,
            pending_password_changes: 0,
            expiring_soon_customers: 0,
            active_customers: 0
        };
        updateDashboardStats();
    }
}

// 更新仪表板统计数据
function updateDashboardStats() {
    // 安全地更新DOM元素，只更新存在的元素
    const updateElement = (id, value) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value || 0;
        }
    };

    updateElement('totalCustomers', dashboardData.total_customers);
    updateElement('totalGameAccounts', dashboardData.total_game_accounts);
    updateElement('availableGameAccounts', dashboardData.available_game_accounts);
    updateElement('inUseGameAccounts', dashboardData.in_use_game_accounts);
    updateElement('activeCustomers', dashboardData.active_customers);
    updateElement('expiredCustomers', dashboardData.expired_customers);
    updateElement('pendingPasswordChanges', dashboardData.pending_password_changes);
    updateElement('bannedCustomers', dashboardData.banned_customers);
}

// 开始自动刷新
function startAutoRefresh() {
    // 每30秒更新一次统计数据
    refreshInterval = setInterval(() => {
        loadDashboardData();
        loadRealtimeLogs();
        loadHotSearchWords();
    }, 30000);

    // 每分钟更新一次客户剩余时长
    setInterval(() => {
        updateRemainingTime();
    }, 60000);

    // 立即执行一次剩余时长更新
    updateRemainingTime();
}

// 显示指定内容区域
function showSection(sectionName) {
    console.log('showSection called:', sectionName);

    // 隐藏所有内容区域
    const sections = document.querySelectorAll('.content-section');
    console.log('Found sections:', sections.length);
    sections.forEach(section => {
        section.classList.remove('active');
    });

    // 移除所有导航链接的active状态
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
    });

    // 显示指定内容区域
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
        targetSection.classList.add('active');
        console.log('Section activated:', sectionName);
    } else {
        console.error('Section not found:', sectionName);
    }

    // 激活对应的导航链接
    const activeNavLink = document.querySelector(`[onclick="showSection('${sectionName}')"]`);
    if (activeNavLink) {
        activeNavLink.classList.add('active');
    }

    // 更新页面标题
    const titles = {
        'dashboard': '数据统计',
        'customers': '客户账号管理',
        'gameAccounts': '游戏账号管理',
        'passwordManage': '密码管理',
        'logs': '操作日志',
        'settings': '系统设置'
    };

    const pageTitle = document.getElementById('pageTitle');
    if (pageTitle && titles[sectionName]) {
        pageTitle.textContent = titles[sectionName];
    }

    // 根据不同区域加载相应数据
    switch(sectionName) {
        case 'customers':
            loadCustomers();
            break;
        case 'gameAccounts':
            loadGameAccounts();
            break;
        case 'passwordManage':
            loadPasswordRecords();
            break;
        case 'logs':
            loadLogs();
            break;
    }
}

// 加载实时日志
async function loadRealtimeLogs() {
    try {
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_operation_logs',
                limit: 10,
                page: 1
            })
        });

        const result = await response.json();

        if (result.success && result.data && result.data.logs) {
            displayRealtimeLogs(result.data.logs);
        } else {
            // 如果没有数据，显示空状态
            displayRealtimeLogs([]);
        }
    } catch (error) {
        console.error('Load realtime logs failed:', error);
        displayRealtimeLogs([]);
    }
}

/**
 * 显示实时日志
 */
function displayRealtimeLogs(logs) {
    const container = document.getElementById('realtimeLogs');
    
    if (!logs || logs.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted">
                <i class="bi bi-journal-text"></i>
                <p class="mt-2">暂无操作日志</p>
            </div>
        `;
        return;
    }
    
    const logsHtml = logs.map(log => {
        const time = new Date(log.created_at).toLocaleTimeString('zh-CN');
        const actionIcon = getActionIcon(log.action);
        const actionText = getActionText(log.action);
        
        return `
            <div class="d-flex align-items-center py-2 border-bottom">
                <div class="me-3">
                    <i class="bi ${actionIcon} text-primary"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold">${actionText}</div>
                    <small class="text-muted">${log.details || ''}</small>
                </div>
                <div class="text-end">
                    <small class="text-muted">${time}</small>
                </div>
            </div>
        `;
    }).join('');
    
    container.innerHTML = logsHtml;
}

// 加载客户热搜词排行
async function loadHotSearchWords() {
    try {
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_hot_search_words',
                limit: 10
            })
        });

        const result = await response.json();

        if (result.success && result.data && result.data.words) {
            displayHotSearchWords(result.data.words);
        } else if (result.success && result.data && Array.isArray(result.data)) {
            // 兼容直接返回数组的情况
            displayHotSearchWords(result.data);
        } else {
            // 显示暂无数据
            displayHotSearchWords([]);
        }
    } catch (error) {
        console.error('Load hot search words failed:', error);
    }
}

// 显示客户热搜词排行
function displayHotSearchWords(words) {
    const container = document.getElementById('hotSearchWords');

    if (!words || words.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted">
                <i class="bi bi-search"></i>
                <p class="mt-2">暂无搜索记录</p>
            </div>
        `;
        return;
    }

    const wordsHtml = words.map((word, index) => {
        const rankClass = index < 3 ? 'text-warning' : 'text-muted';
        const rankIcon = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : (index + 1);

        return `
            <div class="d-flex align-items-center py-2 border-bottom">
                <div class="me-2">
                    <span class="${rankClass} fw-bold">${rankIcon}</span>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold">${word.keyword}</div>
                    <small class="text-muted">${word.search_count} 次搜索</small>
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = wordsHtml;
}

/**
 * 获取操作图标
 */
function getActionIcon(action) {
    const icons = {
        'login': 'bi-box-arrow-in-right',
        'logout': 'bi-box-arrow-right',
        'take_account': 'bi-download',
        'release_account': 'bi-upload',
        'switch_account': 'bi-arrow-repeat',
        'ban_customer': 'bi-person-x',
        'unban_customer': 'bi-person-check',
        'generate_customer': 'bi-person-plus',
        'add_game_account': 'bi-plus-circle',
        'edit_game_account': 'bi-pencil',
        'delete_game_account': 'bi-trash'
    };
    
    return icons[action] || 'bi-info-circle';
}

/**
 * 获取操作文本
 */
function getActionText(action) {
    const texts = {
        'login': '客户登录',
        'logout': '客户登出',
        'take_account': '取用账号',
        'release_account': '释放账号',
        'switch_account': '切换账号',
        'ban_customer': '封禁客户',
        'unban_customer': '解封客户',
        'generate_customer': '生成客户账号',
        'add_game_account': '添加游戏账号',
        'edit_game_account': '编辑游戏账号',
        'delete_game_account': '删除游戏账号'
    };
    
    return texts[action] || '未知操作';
}

// 加载客户账号列表
async function loadCustomers(page = 1, search = '') {
    console.log('loadCustomers called:', { page, search });
    try {
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_customers',
                page: page,
                limit: 20,
                search: search
            })
        });

        console.log('Response status:', response.status);
        const result = await response.json();
        console.log('API result:', result);

        if (result.success) {
            displayCustomers(result.data.customers);
            updatePagination('customersPagination', result.data.page, Math.ceil(result.data.total / result.data.limit), 'loadCustomers');
        } else {
            showAlert('danger', result.message || '加载客户账号失败');
        }
    } catch (error) {
        console.error('Load customers failed:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

// 显示客户账号列表
function displayCustomers(customers) {
    const tbody = document.getElementById('customersTable');
    if (!tbody) return;

    if (!customers || customers.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center">暂无数据</td></tr>';
        return;
    }

    tbody.innerHTML = customers.map(customer => {
        const statusBadge = {
            'active': 'bg-success',
            'expired': 'bg-danger',
            'banned': 'bg-warning'
        }[customer.status] || 'bg-secondary';

        const typeBadge = customer.account_type === 'premium' ? 'bg-warning' : 'bg-info';

        return `
            <tr>
                <td><strong>${customer.account_number}</strong></td>
                <td><span class="badge ${typeBadge}">${customer.account_type === 'premium' ? '高级' : '普通'}</span></td>
                <td>${customer.duration_hours}小时</td>
                <td>${customer.remaining_hours ? parseFloat(customer.remaining_hours).toFixed(2) + '小时' : '-'}</td>
                <td><span class="badge ${statusBadge}">${getStatusText(customer.status)}</span></td>
                <td>${formatDateTime(customer.created_at)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editCustomer(${customer.id})" title="编辑">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning me-1" onclick="toggleCustomerStatus(${customer.id}, '${customer.status}')" title="${customer.status === 'banned' ? '解封' : '封禁'}">
                        <i class="bi bi-person-${customer.status === 'banned' ? 'check' : 'x'}"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteCustomer(${customer.id})" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

// 搜索客户账号
function searchCustomers() {
    const search = document.getElementById('customerSearch').value;
    loadCustomers(1, search);
}

// 加载游戏账号列表
async function loadGameAccounts(page = 1, search = '') {
    try {
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_game_accounts',
                page: page,
                limit: 20,
                search: search
            })
        });

        const result = await response.json();

        if (result.success) {
            displayGameAccounts(result.data.accounts);
            updatePagination('gameAccountsPagination', result.data.page, Math.ceil(result.data.total / result.data.limit), 'loadGameAccounts');
        } else {
            showAlert('danger', result.message || '加载游戏账号失败');
        }
    } catch (error) {
        console.error('Load game accounts failed:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

// 显示游戏账号列表
function displayGameAccounts(accounts) {
    const tbody = document.getElementById('gameAccountsTable');
    if (!tbody) return;

    if (!accounts || accounts.length === 0) {
        tbody.innerHTML = '<tr><td colspan="11" class="text-center">暂无数据</td></tr>';
        return;
    }

    tbody.innerHTML = accounts.map(account => {
        const statusBadge = {
            'available': 'bg-success',
            'in_use': 'bg-warning',
            'banned': 'bg-danger',
            'reported': 'bg-secondary'
        }[account.status] || 'bg-secondary';

        const typeBadge = account.account_type === 'premium' ? 'bg-warning' : 'bg-info';

        // 国战将池显示逻辑
        const poolCount = parseInt(account.nation_war) || 0;
        let poolDisplay = '';
        if (poolCount === 0) {
            poolDisplay = '<span style="color: #666;">未参与</span>';
        } else {
            let level = '';
            let color = '';
            if (poolCount <= 10) {
                level = '新手';
                color = '#95a5a6';
            } else if (poolCount <= 30) {
                level = '进阶';
                color = '#3498db';
            } else if (poolCount <= 50) {
                level = '高级';
                color = '#e74c3c';
            } else {
                level = '顶级';
                color = '#f39c12';
            }
            poolDisplay = `<span style="color: ${color}; font-weight: bold;">${poolCount}个 (${level})</span>`;
        }

        return `
            <tr>
                <td><strong>${account.account}</strong></td>
                <td>${account.account_name || '-'}</td>
                <td>Lv.${account.level || 0} / VIP${account.vip_level || 0}</td>
                <td>${account.rank || '-'}</td>
                <td>${poolDisplay}</td>
                <td><span class="badge ${typeBadge}">${account.account_type === 'premium' ? '高级' : '普通'}</span></td>
                <td>${account.general_count || 0} / ${account.skin_count || 0}</td>
                <td>${account.epic_general_count || 0} / ${account.dynamic_skin_count || 0}</td>
                <td>¥${parseFloat(account.price || 0).toFixed(2)}</td>
                <td><span class="badge ${statusBadge}">${getGameAccountStatusText(account.status)}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editGameAccount(${account.id})">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteGameAccount(${account.id})">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

// 加载密码修改记录
async function loadPasswordRecords(page = 1) {
    try {
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_password_records',
                page: page,
                limit: 20
            })
        });

        const result = await response.json();

        if (result.success) {
            displayPasswordRecords(result.data.records);
            updatePagination('passwordRecordsPagination', result.data.page, Math.ceil(result.data.total / result.data.limit), 'loadPasswordRecords');
        } else {
            showAlert('danger', result.message || '加载密码记录失败');
        }
    } catch (error) {
        console.error('Load password records failed:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

// 加载操作日志
async function loadLogs(page = 1) {
    try {
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_logs',
                page: page,
                limit: 20
            })
        });

        const result = await response.json();

        if (result.success) {
            displayLogs(result.data.logs);
            updatePagination('logsPagination', result.data.page, Math.ceil(result.data.total / result.data.limit), 'loadLogs');
        } else {
            showAlert('danger', result.message || '加载日志失败');
        }
    } catch (error) {
        console.error('Load logs failed:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

// 刷新数据
function refreshData() {
    loadDashboardData();
    loadRealtimeLogs();

    // 显示刷新提示
    showAlert('success', '数据已刷新');
}

/**
 * 管理员登出
 */
function adminLogout() {
    if (confirm('确定要退出管理后台吗？')) {
        localStorage.removeItem('admin_session');
        sessionStorage.removeItem('admin_session');
        window.location.href = '/admin-login.html';
    }
}

// 辅助函数
function getStatusText(status) {
    const statusMap = {
        'active': '活跃',
        'expired': '已过期',
        'banned': '已封禁'
    };
    return statusMap[status] || '未知';
}

function getGameAccountStatusText(status) {
    const statusMap = {
        'available': '可用',
        'in_use': '使用中',
        'banned': '已封禁',
        'reported': '已举报'
    };
    return statusMap[status] || '未知';
}

function formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

function formatTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleTimeString('zh-CN');
}

// 更新分页
function updatePagination(containerId, currentPage, totalPages, loadFunction) {
    const container = document.getElementById(containerId);
    if (!container) return;

    let paginationHtml = '<ul class="pagination pagination-sm justify-content-center">';

    // 上一页
    if (currentPage > 1) {
        paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="${loadFunction}(${currentPage - 1})">上一页</a></li>`;
    }

    // 页码
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        const activeClass = i === currentPage ? 'active' : '';
        paginationHtml += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="${loadFunction}(${i})">${i}</a></li>`;
    }

    // 下一页
    if (currentPage < totalPages) {
        paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="${loadFunction}(${currentPage + 1})">下一页</a></li>`;
    }

    paginationHtml += '</ul>';
    container.innerHTML = paginationHtml;
}

// 显示生成客户账号模态框
function showGenerateModal() {
    const modal = new bootstrap.Modal(document.getElementById('generateModal'));
    modal.show();
}

// 显示添加游戏账号模态框
function showAddGameAccountModal() {
    document.getElementById('addGameAccountForm').reset();
    const modal = new bootstrap.Modal(document.getElementById('addGameAccountModal'));
    modal.show();
}

// 生成客户账号
async function generateAccounts() {
    const accountType = document.getElementById('accountType').value;
    const durationHours = parseInt(document.getElementById('durationHours').value);
    const generateCount = parseInt(document.getElementById('generateCount').value);

    if (!durationHours || durationHours < 1) {
        showAlert('danger', '请输入有效的时长');
        return;
    }

    if (!generateCount || generateCount < 1 || generateCount > 100) {
        showAlert('danger', '生成数量必须在1-100之间');
        return;
    }

    try {
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'generate_customers',
                account_type: accountType,
                duration_hours: durationHours,
                count: generateCount
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('success', `成功生成 ${generateCount} 个客户账号`);
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('generateModal'));
            modal.hide();
            // 刷新客户列表
            if (document.getElementById('customers').classList.contains('active')) {
                loadCustomers();
            }
            // 刷新仪表板数据
            loadDashboardData();
        } else {
            showAlert('danger', result.message || '生成客户账号失败');
        }
    } catch (error) {
        console.error('Generate accounts failed:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

// 添加游戏账号
async function addGameAccount() {
    const gameAccount = document.getElementById('gameAccount').value.trim();
    const gamePassword = document.getElementById('gamePassword').value.trim();
    const gameAccountName = document.getElementById('gameAccountName').value.trim();
    const gameLevel = parseInt(document.getElementById('gameLevel').value) || 0;
    const gameVipLevel = parseInt(document.getElementById('gameVipLevel').value) || 0;
    const gameRank = document.getElementById('gameRank').value.trim();
    const gameNationWar = document.getElementById('gameNationWar').value.trim();
    const gameGender = document.getElementById('gameGender').value;
    const gamePrice = parseFloat(document.getElementById('gamePrice').value) || 0;
    const gameAccountType = document.getElementById('gameAccountType').value;
    const gameGeneralCount = parseInt(document.getElementById('gameGeneralCount').value) || 0;
    const gameSkinCount = parseInt(document.getElementById('gameSkinCount').value) || 0;
    const gamePremiumGenerals = document.getElementById('gamePremiumGenerals').value.trim();
    const gameDynamicSkins = document.getElementById('gameDynamicSkins').value.trim();
    const gameSkin = document.getElementById('gameSkin').value.trim();

    if (!gameAccount || !gamePassword) {
        showAlert('danger', '请填写游戏账号和密码');
        return;
    }

    try {
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'add_game_account',
                account: gameAccount,
                password: gamePassword,
                account_name: gameAccountName,
                level: gameLevel,
                vip_level: gameVipLevel,
                rank: gameRank,
                nation_war: gameNationWar,
                gender: gameGender,
                price: gamePrice,
                account_type: gameAccountType,
                general_count: gameGeneralCount,
                skin_count: gameSkinCount,
                premium_generals: gamePremiumGenerals,
                dynamic_skins: gameDynamicSkins,
                skin: gameSkin
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('success', '游戏账号添加成功');
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('addGameAccountModal'));
            modal.hide();
            // 刷新游戏账号列表
            if (document.getElementById('gameAccounts').classList.contains('active')) {
                loadGameAccounts();
            }
            // 刷新仪表板数据
            loadDashboardData();
        } else {
            showAlert('danger', result.message || '添加游戏账号失败');
        }
    } catch (error) {
        console.error('Add game account failed:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

// 显示密码修改记录
function displayPasswordRecords(records) {
    const tbody = document.getElementById('passwordRecordsTable');
    if (!tbody) return;

    if (!records || records.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center">暂无数据</td></tr>';
        return;
    }

    tbody.innerHTML = records.map(record => {
        const statusBadge = {
            'pending': 'bg-warning',
            'approved': 'bg-success',
            'rejected': 'bg-danger'
        }[record.status] || 'bg-secondary';

        const statusText = {
            'pending': '待处理',
            'approved': '已通过',
            'rejected': '已拒绝'
        }[record.status] || '未知';

        const reasonText = {
            'expired': '时长到期',
            'switched': '切换账号',
            'manual': '手动申请',
            'released': '释放账号'
        }[record.reason] || '未知';

        const reasonBadge = {
            'expired': 'bg-danger',
            'switched': 'bg-info',
            'manual': 'bg-secondary',
            'released': 'bg-warning'
        }[record.reason] || 'bg-secondary';

        return `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <span class="me-2">${record.game_account || '-'}</span>
                        <button class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard('${record.game_account}', '游戏账号')" title="复制账号">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <span class="me-2 text-muted">${record.old_password}</span>
                        <button class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard('${record.old_password}', '旧密码')" title="复制密码">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                </td>
                <td>
                    <small class="text-muted">${formatDateTime(record.created_at)}</small>
                </td>
                <td>
                    <span class="badge ${reasonBadge}">${reasonText}</span>
                </td>
                <td>
                    ${record.status === 'pending' ? `
                        <div class="input-group input-group-sm">
                            <input type="text" class="form-control"
                                   id="newPassword_${record.id}"
                                   value="${record.new_password || ''}"
                                   placeholder="输入新密码">
                            <button class="btn btn-outline-success" onclick="updateGameAccountPassword(${record.id})" title="更新密码">
                                <i class="bi bi-check"></i>
                            </button>
                        </div>
                    ` : `
                        <span class="text-success">${record.new_password || '已更新'}</span>
                    `}
                </td>
                <td><span class="badge ${statusBadge}">${statusText}</span></td>
                <td>
                    <button class="btn btn-sm btn-danger" onclick="deletePasswordRecord(${record.id})" title="删除记录">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

// 显示操作日志
function displayLogs(logs) {
    const tbody = document.getElementById('logsTable');
    if (!tbody) return;

    if (!logs || logs.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center">暂无数据</td></tr>';
        return;
    }

    tbody.innerHTML = logs.map(log => {
        const userTypeBadge = log.user_type === 'admin' ? 'bg-danger' : 'bg-info';
        const userTypeText = log.user_type === 'admin' ? '管理员' : '客户';
        const actionIcon = getActionIcon(log.action);
        const actionText = getActionText(log.action);

        return `
            <tr>
                <td>${formatDateTime(log.created_at)}</td>
                <td>
                    <i class="bi ${actionIcon} me-2"></i>
                    ${actionText}
                </td>
                <td>
                    <span class="badge ${userTypeBadge}">${userTypeText}</span>
                    ${log.user_name || log.user_id || '-'}
                </td>
                <td>${log.details || log.description || '-'}</td>
                <td>${log.ip_address || '-'}</td>
            </tr>
        `;
    }).join('');
}

// 审核密码修改 - 通过
async function approvePasswordChange(recordId) {
    if (!confirm('确定要通过这个密码修改申请吗？')) return;

    try {
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'approve_password_change',
                record_id: recordId
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('success', '密码修改申请已通过');
            loadPasswordRecords();
        } else {
            showAlert('danger', result.message || '操作失败');
        }
    } catch (error) {
        console.error('Approve password change failed:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

// 更新游戏账号密码
async function updateGameAccountPassword(recordId) {
    const newPasswordInput = document.getElementById(`newPassword_${recordId}`);
    if (!newPasswordInput) {
        showAlert('danger', '找不到密码输入框');
        return;
    }

    const newPassword = newPasswordInput.value.trim();
    if (!newPassword) {
        showAlert('danger', '请输入新密码');
        newPasswordInput.focus();
        return;
    }

    if (!confirm('确定要更新这个游戏账号的密码吗？更新后记录将被清除。')) return;

    try {
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'manual_update_password',
                record_id: recordId,
                new_password: newPassword
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('success', `密码更新成功！游戏账号: ${result.data.game_account}`);
            loadPasswordRecords();
            loadDashboardData(); // 刷新统计数据
        } else {
            showAlert('danger', result.message || '密码更新失败');
        }
    } catch (error) {
        console.error('Update game account password failed:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

// 删除密码记录
async function deletePasswordRecord(recordId) {
    if (!confirm('确定要删除这条密码记录吗？此操作不可撤销。')) return;

    try {
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'delete_password_record',
                record_id: recordId
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('success', '密码记录删除成功');
            loadPasswordRecords();
            loadDashboardData(); // 刷新统计数据
        } else {
            showAlert('danger', result.message || '删除失败');
        }
    } catch (error) {
        console.error('Delete password record failed:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

// 刷新密码记录
function refreshPasswordRecords() {
    loadPasswordRecords();
}

// 审核密码修改 - 拒绝
async function rejectPasswordChange(recordId) {
    const reason = prompt('请输入拒绝理由：');
    if (!reason) return;

    try {
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'reject_password_change',
                record_id: recordId,
                reason: reason
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('success', '密码修改申请已拒绝');
            loadPasswordRecords();
        } else {
            showAlert('danger', result.message || '操作失败');
        }
    } catch (error) {
        console.error('Reject password change failed:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

// 显示提示信息
function showAlert(type, message) {
    // 创建提示框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // 3秒后自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}

// 删除客户账号
async function deleteCustomer(customerId) {
    if (!confirm('确定要删除这个客户账号吗？此操作不可恢复！')) return;

    try {
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'delete_customer',
                customer_id: customerId
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('success', '客户账号删除成功');
            loadCustomers();
            loadDashboardData();
        } else {
            showAlert('danger', result.message || '删除失败');
        }
    } catch (error) {
        console.error('Delete customer failed:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

// 删除游戏账号
async function deleteGameAccount(accountId) {
    if (!confirm('确定要删除这个游戏账号吗？此操作不可恢复！')) return;

    try {
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'delete_game_account',
                account_id: accountId
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('success', '游戏账号删除成功');
            loadGameAccounts();
            loadDashboardData();
        } else {
            showAlert('danger', result.message || '删除失败');
        }
    } catch (error) {
        console.error('Delete game account failed:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

// 编辑客户账号
async function editCustomer(customerId) {
    try {
        // 获取客户信息
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_customer_detail',
                customer_id: customerId
            })
        });

        const result = await response.json();

        if (result.success) {
            const customer = result.data;

            // 填充表单
            document.getElementById('editCustomerId').value = customer.id;
            document.getElementById('editCustomerAccount').value = customer.account_number;
            document.getElementById('editCustomerType').value = customer.account_type;
            document.getElementById('editCustomerDuration').value = customer.duration_hours;
            document.getElementById('editCustomerRemaining').value = customer.remaining_hours || 0;
            document.getElementById('editCustomerStatus').value = customer.status;

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('editCustomerModal'));
            modal.show();
        } else {
            showAlert('danger', result.message || '获取客户信息失败');
        }
    } catch (error) {
        console.error('Edit customer failed:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

// 更新客户账号
async function updateCustomer() {
    const customerId = document.getElementById('editCustomerId').value;
    const accountType = document.getElementById('editCustomerType').value;
    const durationHours = parseInt(document.getElementById('editCustomerDuration').value);
    const remainingHours = parseFloat(document.getElementById('editCustomerRemaining').value);
    const status = document.getElementById('editCustomerStatus').value;

    if (!durationHours || durationHours < 1) {
        showAlert('danger', '请输入有效的总时长');
        return;
    }

    if (remainingHours < 0) {
        showAlert('danger', '剩余时长不能为负数');
        return;
    }

    try {
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'update_customer',
                customer_id: customerId,
                account_type: accountType,
                duration_hours: durationHours,
                remaining_hours: remainingHours,
                status: status
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('success', '客户账号更新成功');
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('editCustomerModal'));
            modal.hide();
            // 刷新客户列表
            loadCustomers();
            loadDashboardData();
        } else {
            showAlert('danger', result.message || '更新客户账号失败');
        }
    } catch (error) {
        console.error('Update customer failed:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

// 切换客户状态
async function toggleCustomerStatus(customerId, currentStatus) {
    const newStatus = currentStatus === 'banned' ? 'active' : 'banned';
    const action = newStatus === 'banned' ? '封禁' : '解封';

    if (!confirm(`确定要${action}这个客户账号吗？`)) return;

    try {
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'toggle_customer_status',
                customer_id: customerId,
                status: newStatus
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('success', `客户账号${action}成功`);
            loadCustomers();
            loadDashboardData();
        } else {
            showAlert('danger', result.message || `${action}失败`);
        }
    } catch (error) {
        console.error('Toggle customer status failed:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

// 编辑游戏账号
async function editGameAccount(accountId) {
    try {
        // 获取游戏账号信息
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_game_account_detail',
                account_id: accountId
            })
        });

        const result = await response.json();

        if (result.success) {
            const account = result.data;

            // 填充编辑表单
            document.getElementById('editGameAccountId').value = account.id;
            document.getElementById('editGameAccount').value = account.account;
            document.getElementById('editGamePassword').value = account.password;
            document.getElementById('editGameAccountName').value = account.account_name || '';
            document.getElementById('editGameLevel').value = account.level || 0;
            document.getElementById('editGameVipLevel').value = account.vip_level || 0;
            document.getElementById('editGameRank').value = account.rank || '';
            document.getElementById('editGameNationWar').value = account.nation_war || '';
            document.getElementById('editGameGender').value = account.gender || '';
            document.getElementById('editGamePrice').value = account.price || 0;
            document.getElementById('editGameAccountType').value = account.account_type || 'normal';
            document.getElementById('editGameGeneralCount').value = account.general_count || 0;
            document.getElementById('editGameSkinCount').value = account.skin_count || 0;
            document.getElementById('editGamePremiumGenerals').value = account.premium_generals || '';
            document.getElementById('editGameDynamicSkins').value = account.dynamic_skins || '';
            document.getElementById('editGameSkin').value = account.skin || '';

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('editGameAccountModal'));
            modal.show();
        } else {
            showAlert('danger', result.message || '获取游戏账号信息失败');
        }
    } catch (error) {
        console.error('Edit game account failed:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

// 更新游戏账号
async function updateGameAccount() {
    const accountId = document.getElementById('editGameAccountId').value;
    const account = document.getElementById('editGameAccount').value.trim();
    const password = document.getElementById('editGamePassword').value.trim();
    const accountName = document.getElementById('editGameAccountName').value.trim();
    const level = parseInt(document.getElementById('editGameLevel').value) || 0;
    const vipLevel = parseInt(document.getElementById('editGameVipLevel').value) || 0;
    const rank = document.getElementById('editGameRank').value.trim();
    const nationWar = document.getElementById('editGameNationWar').value.trim();
    const gender = document.getElementById('editGameGender').value;
    const price = parseFloat(document.getElementById('editGamePrice').value) || 0;
    const accountType = document.getElementById('editGameAccountType').value;
    const generalCount = parseInt(document.getElementById('editGameGeneralCount').value) || 0;
    const skinCount = parseInt(document.getElementById('editGameSkinCount').value) || 0;
    const premiumGenerals = document.getElementById('editGamePremiumGenerals').value.trim();
    const dynamicSkins = document.getElementById('editGameDynamicSkins').value.trim();
    const skinDetails = document.getElementById('editGameSkin').value.trim();

    if (!account || !password) {
        showAlert('danger', '游戏账号和密码不能为空');
        return;
    }

    try {
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'update_game_account',
                account_id: accountId,
                account: account,
                password: password,
                account_name: accountName,
                level: level,
                vip_level: vipLevel,
                rank: rank,
                nation_war: nationWar,
                gender: gender,
                price: price,
                account_type: accountType,
                general_count: generalCount,
                skin_count: skinCount,
                premium_generals: premiumGenerals,
                dynamic_skins: dynamicSkins,
                skin: skinDetails
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('success', '游戏账号更新成功');
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('editGameAccountModal'));
            modal.hide();
            // 刷新游戏账号列表
            loadGameAccounts();
            loadDashboardData();
        } else {
            showAlert('danger', result.message || '更新游戏账号失败');
        }
    } catch (error) {
        console.error('Update game account failed:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}





// 修改管理员密码
async function changeAdminPassword() {
    const currentPassword = document.getElementById('currentPassword').value.trim();
    const newPassword = document.getElementById('newPassword').value.trim();
    const confirmPassword = document.getElementById('confirmPassword').value.trim();

    // 验证输入
    if (!currentPassword || !newPassword || !confirmPassword) {
        showAlert('danger', '请填写所有密码字段');
        return;
    }

    if (newPassword !== confirmPassword) {
        showAlert('danger', '新密码和确认密码不匹配');
        return;
    }

    if (newPassword.length < 6) {
        showAlert('danger', '新密码长度至少6位');
        return;
    }

    if (newPassword === currentPassword) {
        showAlert('warning', '新密码不能与当前密码相同');
        return;
    }

    try {
        const response = await fetch('/api/change_password_simple.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                current_password: currentPassword,
                new_password: newPassword,
                confirm_password: confirmPassword
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('success', '密码修改成功！');

            // 清空表单
            document.getElementById('changePasswordForm').reset();

            // 可选：刷新页面或重新登录
            setTimeout(() => {
                if (confirm('密码已修改成功，是否重新登录？')) {
                    window.location.href = '/admin-login.html';
                }
            }, 2000);
        } else {
            showAlert('danger', result.message || '密码修改失败');
        }
    } catch (error) {
        console.error('Change admin password failed:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

// 更新客户剩余时长
async function updateRemainingTime() {
    try {
        const response = await fetch('/api/admin_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'update_remaining_time'
            })
        });

        const result = await response.json();

        if (result.success) {
            console.log('剩余时长更新成功:', result.data);

            // 如果当前在客户管理页面，刷新客户列表
            if (document.getElementById('customers').classList.contains('active')) {
                loadCustomers();
            }

            // 刷新仪表板统计数据
            loadDashboardData();
        } else {
            console.error('剩余时长更新失败:', result.message);
        }
    } catch (error) {
        console.error('更新剩余时长网络错误:', error);
    }
}

// 手动刷新剩余时长
function manualUpdateRemainingTime() {
    updateRemainingTime().then(() => {
        showAlert('success', '剩余时长已更新');
    });
}

/**
 * 清空热搜关键词
 */
async function clearHotSearchWords() {
    if (!confirm('确定要清空所有热搜关键词吗？此操作不可恢复！')) {
        return;
    }

    try {
        const response = await fetch('/api/clear_search_keywords.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            showAlert('success', result.message || '热搜关键词已清空');
            // 重新加载热搜词数据
            loadHotSearchWords();
        } else {
            showAlert('danger', result.message || '清空失败');
        }
    } catch (error) {
        console.error('清空热搜关键词失败:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}

/**
 * 清空操作日志
 */
async function clearOperationLogs() {
    if (!confirm('确定要清空所有操作日志吗？此操作不可恢复！')) {
        return;
    }

    try {
        const response = await fetch('/api/clear_operation_logs.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            showAlert('success', result.message || '操作日志已清空');
            // 重新加载日志数据
            loadLogs();
            // 重新加载实时日志
            loadRealtimeLogs();
        } else {
            showAlert('danger', result.message || '清空失败');
        }
    } catch (error) {
        console.error('清空操作日志失败:', error);
        showAlert('danger', '网络错误，请稍后重试');
    }
}
