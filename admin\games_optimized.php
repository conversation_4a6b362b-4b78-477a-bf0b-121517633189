<?php
/**
 * 优化的游戏账号管理页面
 * 解决录入详情需要特殊格式的问题
 */

require_once '../config/database.php';
require_once '../config/auth.php';

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$database = new Database();
$pdo = $database->getConnection();

$action = $_GET['action'] ?? '';

// 数据转换函数
function convertToJsonArray($input) {
    if (empty($input)) {
        return '[]';
    }
    
    // 如果已经是JSON格式，直接返回
    if (is_string($input) && (strpos($input, '[') === 0 || strpos($input, '{') === 0)) {
        $decoded = json_decode($input, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return $input;
        }
    }
    
    // 将逗号分隔的字符串转换为JSON数组
    $items = explode(',', $input);
    $items = array_map('trim', $items);
    $items = array_filter($items, function($item) {
        return !empty($item);
    });
    
    return json_encode(array_values($items), JSON_UNESCAPED_UNICODE);
}

// 自动计算数量
function calculateCount($details) {
    if (empty($details)) {
        return 0;
    }
    
    // 如果是JSON格式
    if (strpos($details, '[') === 0) {
        $decoded = json_decode($details, true);
        return is_array($decoded) ? count($decoded) : 0;
    }
    
    // 如果是逗号分隔
    $items = explode(',', $details);
    $items = array_filter(array_map('trim', $items));
    return count($items);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'create') {
        try {
            // 获取表单数据
            $data = [
                'account' => trim($_POST['account'] ?? ''),
                'password' => trim($_POST['password'] ?? ''),
                'account_name' => trim($_POST['account_name'] ?? ''),
                'level' => (int)($_POST['level'] ?? 0),
                'vip_level' => (int)($_POST['vip_level'] ?? 0),
                'rank' => trim($_POST['rank'] ?? ''),
                'nation_war' => trim($_POST['nation_war'] ?? ''),
                'gender' => trim($_POST['gender'] ?? '未知'),
                'price' => ($_POST['price'] !== '' ? (float)$_POST['price'] : 0),
                'account_type' => trim($_POST['account_type'] ?? 'normal'),
            ];
            
            // 处理武将和皮肤数据
            $generalDetails = trim($_POST['general_details'] ?? '');
            $epicGenerals = trim($_POST['epic_generals'] ?? '');
            $skinDetails = trim($_POST['skin_details'] ?? '');
            $dynamicSkinDetails = trim($_POST['dynamic_skin_details'] ?? '');

            // 转换为JSON格式并计算数量
            $data['general_details'] = $generalDetails;
            $data['epic_generals'] = $epicGenerals;
            $data['skin_details'] = $skinDetails;
            $data['dynamic_skin_details'] = $dynamicSkinDetails;

            // 转换为JSON格式用于客户端显示
            $data['premium_generals'] = convertToJsonArray($epicGenerals);
            $data['dynamic_skins'] = convertToJsonArray($dynamicSkinDetails);
            $data['skin'] = $skinDetails; // 保持原格式用于兼容

            // 自动计算数量
            $data['general_count'] = calculateCount($generalDetails);
            $data['epic_general_count'] = calculateCount($epicGenerals);
            $data['skin_count'] = calculateCount($skinDetails);
            $data['dynamic_skin_count'] = calculateCount($dynamicSkinDetails);
            
            // 验证必填字段
            if (empty($data['account']) || empty($data['password'])) {
                throw new Exception('游戏账号和密码为必填项');
            }
            
            // 插入数据库
            $stmt = $pdo->prepare('
                INSERT INTO game_accounts (
                    account, password, account_name, level, vip_level, `rank`, nation_war, 
                    skin, gender, price, premium_generals, dynamic_skins, general_count, 
                    skin_count, epic_general_count, dynamic_skin_count, epic_generals, 
                    dynamic_skin_details, general_details, skin_details, account_type, 
                    status, created_at
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "available", NOW()
                )
            ');
            
            $stmt->execute([
                $data['account'], $data['password'], $data['account_name'], $data['level'], 
                $data['vip_level'], $data['rank'], $data['nation_war'], $data['skin'], 
                $data['gender'], $data['price'], $data['premium_generals'], $data['dynamic_skins'], 
                $data['general_count'], $data['skin_count'], $data['epic_general_count'], 
                $data['dynamic_skin_count'], $data['epic_generals'], $data['dynamic_skin_details'], 
                $data['general_details'], $data['skin_details'], $data['account_type']
            ]);
            
            $message = '账号添加成功！系统已自动处理数据格式。';
            
        } catch (Exception $e) {
            $error = '添加失败: ' . $e->getMessage();
        }
    }
}

// 获取账号列表
$stmt = $pdo->query('SELECT * FROM game_accounts ORDER BY created_at DESC LIMIT 20');
$accounts = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化的游戏账号管理</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .form-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        .form-section h4 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 18px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 8px;
        }
        .form-row {
            margin-bottom: 15px;
        }
        .form-row.full {
            grid-column: 1 / -1;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #dee2e6;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .alert-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .help-text {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        .auto-count {
            background: #e8f5e8;
            border-color: #28a745;
        }
        .preview-section {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }
        .preview-section h4 {
            color: #1976d2;
            margin: 0 0 10px 0;
        }
        .tag {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin: 2px;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .table th, .table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: left;
        }
        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 优化的游戏账号管理</h1>
            <p>智能数据处理，无需手动格式化</p>
        </div>

        <?php if (isset($message)): ?>
            <div class="alert alert-success"><?= htmlspecialchars($message) ?></div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-error"><?= htmlspecialchars($error) ?></div>
        <?php endif; ?>

        <form method="post" action="?action=create" id="accountForm">
            <div class="form-grid">
                <!-- 基本信息 -->
                <div class="form-section">
                    <h4>🎮 基本信息</h4>
                    <div class="form-row">
                        <label>游戏账号 *</label>
                        <input name="account" required placeholder="请输入游戏登录账号">
                    </div>
                    <div class="form-row">
                        <label>游戏密码 *</label>
                        <input name="password" required placeholder="请输入游戏密码">
                    </div>
                    <div class="form-row">
                        <label>账号名称</label>
                        <input name="account_name" placeholder="游戏内角色名称">
                    </div>
                    <div class="form-row">
                        <label>官阶</label>
                        <input name="rank" placeholder="如：王者官阶、钻石官阶等">
                    </div>
                    <div class="form-row">
                        <label>国战将池数量</label>
                        <input name="nation_war" type="number" min="0" max="999" placeholder="0" value="0">
                        <div class="help-text">💡 国战中可用的武将数量，0表示未参与国战</div>
                    </div>
                    <div class="form-row">
                        <label>性别</label>
                        <select name="gender">
                            <option value="未知">未知</option>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </div>
                </div>

                <!-- 等级信息 -->
                <div class="form-section">
                    <h4>⭐ 等级信息</h4>
                    <div class="form-row">
                        <label>等级</label>
                        <input name="level" type="number" min="0" max="999" placeholder="0">
                    </div>
                    <div class="form-row">
                        <label>VIP等级</label>
                        <input name="vip_level" type="number" min="0" max="15" placeholder="0">
                    </div>
                    <div class="form-row">
                        <label>账号出售价格</label>
                        <input name="price" type="number" step="0.01" min="0" placeholder="0.00">
                    </div>
                    <div class="form-row">
                        <label>账号区域</label>
                        <select name="account_type">
                            <option value="normal">普通区域</option>
                            <option value="premium">高级区域</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-grid">
                <!-- 武将信息 -->
                <div class="form-section">
                    <h4>👥 武将信息</h4>
                    <div class="form-row">
                        <label>所有武将详情</label>
                        <textarea name="general_details" placeholder="请输入所有武将名称，用逗号分隔&#10;例如：赵云,关羽,张飞,诸葛亮,司马懿,曹操" oninput="updatePreview()"></textarea>
                        <div class="help-text">💡 直接输入武将名称，用逗号分隔即可，系统会自动处理格式</div>
                    </div>
                    <div class="form-row">
                        <label>史诗武将详情</label>
                        <textarea name="epic_generals" placeholder="请输入史诗武将名称，用逗号分隔&#10;例如：赵云,关羽,张飞" oninput="updatePreview()"></textarea>
                        <div class="help-text">💡 这些武将会在客户端以特殊样式显示</div>
                    </div>
                </div>

                <!-- 皮肤信息 -->
                <div class="form-section">
                    <h4>🎨 皮肤信息</h4>
                    <div class="form-row">
                        <label>所有皮肤详情</label>
                        <textarea name="skin_details" placeholder="请输入所有皮肤名称，用逗号分隔&#10;例如：貂蝉-闭月,大乔-国色,小乔-天香" oninput="updatePreview()"></textarea>
                        <div class="help-text">💡 建议格式：武将名-皮肤名</div>
                    </div>
                    <div class="form-row">
                        <label>动态皮肤详情</label>
                        <textarea name="dynamic_skin_details" placeholder="请输入动态皮肤名称，用逗号分隔&#10;例如：赵云-引雷,关羽-武圣,张飞-咆哮" oninput="updatePreview()"></textarea>
                        <div class="help-text">💡 这些皮肤会在客户端以动态效果显示</div>
                    </div>
                </div>
            </div>

            <!-- 预览区域 -->
            <div class="preview-section" id="previewSection" style="display: none;">
                <h4>📋 数据预览</h4>
                <div id="previewContent"></div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button type="submit" class="btn">✅ 添加账号</button>
                <a href="games.php" class="btn" style="background: #6c757d; text-decoration: none; margin-left: 10px;">返回原版</a>
            </div>
        </form>

        <!-- 最近添加的账号 -->
        <h2 style="margin: 40px 0 20px 0; color: #333;">📋 最近添加的账号</h2>
        <table class="table">
            <tr>
                <th>ID</th>
                <th>账号</th>
                <th>名称</th>
                <th>等级/VIP</th>
                <th>武将/皮肤</th>
                <th>史诗/动态</th>
                <th>数据格式</th>
                <th>添加时间</th>
            </tr>
            <?php foreach ($accounts as $account): ?>
            <tr>
                <td><?= $account['id'] ?></td>
                <td><?= htmlspecialchars($account['account']) ?></td>
                <td><?= htmlspecialchars($account['account_name'] ?: '-') ?></td>
                <td>Lv.<?= $account['level'] ?> / VIP<?= $account['vip_level'] ?></td>
                <td><?= $account['general_count'] ?? 0 ?> / <?= $account['skin_count'] ?? 0 ?></td>
                <td><?= $account['epic_general_count'] ?? 0 ?> / <?= $account['dynamic_skin_count'] ?? 0 ?></td>
                <td>
                    <?php if (!empty($account['premium_generals']) && strpos($account['premium_generals'], '[') === 0): ?>
                        <span style="color: #28a745;">✅ JSON</span>
                    <?php else: ?>
                        <span style="color: #dc3545;">❌ 文本</span>
                    <?php endif; ?>
                </td>
                <td><?= date('m-d H:i', strtotime($account['created_at'])) ?></td>
            </tr>
            <?php endforeach; ?>
        </table>
    </div>

    <script>
        function updatePreview() {
            const generalDetails = document.querySelector('textarea[name="general_details"]').value;
            const epicGenerals = document.querySelector('textarea[name="epic_generals"]').value;
            const skinDetails = document.querySelector('textarea[name="skin_details"]').value;
            const dynamicSkinDetails = document.querySelector('textarea[name="dynamic_skin_details"]').value;
            
            const previewSection = document.getElementById('previewSection');
            const previewContent = document.getElementById('previewContent');
            
            let hasContent = false;
            let html = '';
            
            if (generalDetails.trim()) {
                const generals = generalDetails.split(',').map(s => s.trim()).filter(s => s);
                html += `<p><strong>所有武将 (${generals.length}个):</strong><br>`;
                html += generals.map(g => `<span class="tag">${g}</span>`).join('');
                html += '</p>';
                hasContent = true;
            }
            
            if (epicGenerals.trim()) {
                const epics = epicGenerals.split(',').map(s => s.trim()).filter(s => s);
                html += `<p><strong>史诗武将 (${epics.length}个):</strong><br>`;
                html += epics.map(g => `<span class="tag" style="background: #ff6b35;">${g}</span>`).join('');
                html += '</p>';
                hasContent = true;
            }
            
            if (skinDetails.trim()) {
                const skins = skinDetails.split(',').map(s => s.trim()).filter(s => s);
                html += `<p><strong>所有皮肤 (${skins.length}个):</strong><br>`;
                html += skins.map(s => `<span class="tag" style="background: #9c27b0;">${s}</span>`).join('');
                html += '</p>';
                hasContent = true;
            }
            
            if (dynamicSkinDetails.trim()) {
                const dynamics = dynamicSkinDetails.split(',').map(s => s.trim()).filter(s => s);
                html += `<p><strong>动态皮肤 (${dynamics.length}个):</strong><br>`;
                html += dynamics.map(s => `<span class="tag" style="background: #e91e63;">${s}</span>`).join('');
                html += '</p>';
                hasContent = true;
            }
            
            if (hasContent) {
                previewContent.innerHTML = html;
                previewSection.style.display = 'block';
            } else {
                previewSection.style.display = 'none';
            }
        }
    </script>
</body>
</html>
