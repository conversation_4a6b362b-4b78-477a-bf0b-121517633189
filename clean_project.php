<?php
/**
 * 🧹 项目文件清理脚本 - 安全版本
 * 只删除测试文件、文档文件和临时文件，保留所有核心功能文件
 * 确保系统正常运行不受影响
 */

// 核心系统文件列表（绝对不会删除的文件）
$coreFiles = [
    // 主要页面
    'index.html',
    'dashboard.html',
    'customer-dashboard.html',
    'customer-login.html',
    'admin-login.html',

    // 配置文件
    'config.php',
    'config/database.php',
    '.user.ini',

    // 核心API
    'api/customer_new.php',
    'api/admin_login.php',
    'api/admin_stats.php',
    'api/password_change.php',
    'api/query.php',
    'api/clear_operation_logs.php',
    'api/clear_search_keywords.php',
    'api/change_password_simple.php',
    'api/simple_change_password.php',

    // 管理后台
    'admin/games.php',
    'admin/games_new.php',
    'admin/games_optimized.php',
    'admin/customers.php',
    'admin/change_password.php',
    'admin/pending_passwords.php',
    'admin/check_logs.php',

    // 样式和脚本
    'css/customer-dashboard.css',
    'js/customer-dashboard.js',
    'js/dashboard.js',

    // 定时任务
    'cron/update_remaining_time.php',
    'cron/check_expiry.php',
    'cron_update_time.php',
    'update_remaining_time.php',

    // 数据库相关
    'database/add_password_change_table.sql',
    'database/update_password_change_records_reason.sql',
    'install.sql',
    '简化迁移脚本.sql',

    // 其他核心文件
    'inc/db.php',
    'favicon.ico',
    'install.lock',

    // 安装和管理文件
    'one_click_install.php',
    'install.php',
    'clean_project.php',
    'enhanced-customer-api.php'
];

// 要删除的具体文件列表（明确指定，避免误删）
$deleteFiles = [
    // 测试文件
    'test_admin_login.html',
    'test_count_display.html',
    'test_data_parsing.html',
    'test_password_change.html',
    'test_tag_styles.html',
    'api/test_api.php',

    // 文档和说明文件
    'mobile_copy_optimization_summary.md',
    'mobile_image2_effect_summary.md',
    'mobile_image_effect_summary.md',
    'mobile_issue_solution.md',
    'mobile_optimization_summary.md',
    'optimization_summary.md',
    'desktop_style_mobile_summary.md',
    'details_optimization_summary.md',
    'final_fix_summary.md',
    'password_change_fix_summary.md',
    'password_fix_update.md',
    'rental_terms_feature_summary.md',
    'ultra_compact_optimization_summary.md',
    'universal_copy_solution.md',

    // 测试和演示页面
    'mobile_copy_test.html',
    'mobile_image_effect_test.html',
    'mobile_optimization_test.html',
    'mobile_ultra_compact_test.html',
    'ultra_compact_mobile_test.html',
    'desktop_style_mobile_test.html',
    'details_optimization_test.html',
    'optimization_test.html',
    'rental_terms_test.html',
    'universal_copy_test.html',
    'tag_comparison.html',
    'final_tag_test.html',
    'admin_input_guide.html',

    // 压缩包和备份文件
    'sanguosha.lanjuan.xin_XYk8p.zip',

    // 日志文件（可选清理）
    'logs/client_api.log',
    'logs/error.log'
];

// 可选删除的日志目录（如果为空）
$optionalCleanDirs = [
    'logs'
];

echo "<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>项目文件清理</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .file-list { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; max-height: 300px; overflow-y: auto; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn.danger { background: #dc3545; }
        .btn.danger:hover { background: #c82333; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🧹 项目文件清理</h1>
        <p>此脚本将删除所有测试、调试、临时文件，保留核心功能文件</p>";

if (isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action === 'clean') {
        echo "<h3>开始清理文件...</h3>";
        
        $deletedFiles = [];
        $errors = [];
        
        // 删除匹配模式的文件
        foreach ($deletePatterns as $pattern) {
            $files = glob($pattern);
            foreach ($files as $file) {
                if (file_exists($file) && !in_array($file, $coreFiles)) {
                    if (unlink($file)) {
                        $deletedFiles[] = $file;
                    } else {
                        $errors[] = "无法删除: $file";
                    }
                }
            }
        }
        
        // 删除指定的API文件
        foreach ($deleteApiFiles as $file) {
            if (file_exists($file)) {
                if (unlink($file)) {
                    $deletedFiles[] = $file;
                } else {
                    $errors[] = "无法删除: $file";
                }
            }
        }
        
        // 删除指定的管理后台文件
        foreach ($deleteAdminFiles as $file) {
            if (file_exists($file)) {
                if (unlink($file)) {
                    $deletedFiles[] = $file;
                } else {
                    $errors[] = "无法删除: $file";
                }
            }
        }
        
        // 清理空目录
        $emptyDirs = [];
        if (is_dir('backup') && count(scandir('backup')) == 2) {
            if (rmdir('backup')) {
                $emptyDirs[] = 'backup';
            }
        }
        
        echo "<div class='success'>";
        echo "<h4>✅ 清理完成！</h4>";
        echo "<p>删除了 " . count($deletedFiles) . " 个文件</p>";
        if (!empty($emptyDirs)) {
            echo "<p>删除了 " . count($emptyDirs) . " 个空目录</p>";
        }
        echo "</div>";
        
        if (!empty($deletedFiles)) {
            echo "<h4>已删除的文件：</h4>";
            echo "<div class='file-list'>";
            foreach ($deletedFiles as $file) {
                echo "<div class='info'>✓ $file</div>";
            }
            echo "</div>";
        }
        
        if (!empty($errors)) {
            echo "<h4 class='error'>删除失败的文件：</h4>";
            echo "<div class='file-list'>";
            foreach ($errors as $error) {
                echo "<div class='error'>✗ $error</div>";
            }
            echo "</div>";
        }
        
        echo "<h4>保留的核心文件：</h4>";
        echo "<div class='file-list'>";
        foreach ($coreFiles as $file) {
            if (file_exists($file)) {
                echo "<div class='success'>✓ $file</div>";
            }
        }
        echo "</div>";
        
        echo "<div class='success'>";
        echo "<h4>🎉 项目清理完成！</h4>";
        echo "<p>现在项目只包含核心功能文件，可以安全地部署到生产环境。</p>";
        echo "<p><strong>建议下一步：</strong></p>";
        echo "<ul>";
        echo "<li>运行 <a href='one_click_install.php'>一键安装脚本</a> 重新初始化系统</li>";
        echo "<li>检查 <a href='dashboard.html'>管理后台</a> 功能是否正常</li>";
        echo "<li>测试 <a href='customer-dashboard.html'>客户端</a> 功能</li>";
        echo "</ul>";
        echo "</div>";
        
    } elseif ($action === 'preview') {
        echo "<h3>预览将要删除的文件</h3>";
        
        $toDelete = [];
        
        // 收集匹配模式的文件
        foreach ($deletePatterns as $pattern) {
            $files = glob($pattern);
            foreach ($files as $file) {
                if (!in_array($file, $coreFiles)) {
                    $toDelete[] = $file;
                }
            }
        }
        
        // 添加指定的API和管理文件
        foreach (array_merge($deleteApiFiles, $deleteAdminFiles) as $file) {
            if (file_exists($file)) {
                $toDelete[] = $file;
            }
        }
        
        if (!empty($toDelete)) {
            echo "<div class='warning'>";
            echo "<h4>⚠️ 将要删除 " . count($toDelete) . " 个文件：</h4>";
            echo "</div>";
            echo "<div class='file-list'>";
            foreach ($toDelete as $file) {
                echo "<div class='warning'>🗑️ $file</div>";
            }
            echo "</div>";
        } else {
            echo "<div class='info'><h4>ℹ️ 没有找到需要删除的文件</h4></div>";
        }
        
        echo "<form method='post'>";
        echo "<button type='submit' name='action' value='clean' class='btn danger'>确认删除这些文件</button>";
        echo "<button type='submit' name='action' value='cancel' class='btn'>取消</button>";
        echo "</form>";
    }
} else {
    echo "<h3>选择操作</h3>";
    echo "<form method='post'>";
    echo "<button type='submit' name='action' value='preview' class='btn'>预览要删除的文件</button>";
    echo "<button type='submit' name='action' value='clean' class='btn danger'>直接开始清理</button>";
    echo "</form>";
    
    echo "<div class='info'>";
    echo "<h4>ℹ️ 说明</h4>";
    echo "<p>此脚本将删除所有测试、调试、临时文件，只保留核心功能文件。</p>";
    echo "<p><strong>保留的核心文件包括：</strong></p>";
    echo "<ul>";
    echo "<li>主要页面：dashboard.html, customer-dashboard.html 等</li>";
    echo "<li>核心API：customer_new.php, admin_login.php 等</li>";
    echo "<li>管理后台：games.php, customers.php 等</li>";
    echo "<li>配置文件：config.php, database.php</li>";
    echo "<li>样式和脚本：CSS, JS 文件</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div></body></html>";
?>
