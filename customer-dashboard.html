<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=0.45, minimum-scale=0.25, maximum-scale=3.0, user-scalable=yes">
    <title>🎮 游戏账号管理-三国杀手游版</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎮</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        /* 使用系统字体作为备用，避免网络字体加载失败 */
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Share+Tech+Mono:wght@400&family=Exo+2:wght@300;400;500;600;700;800&display=swap');

        /* 字体加载失败时的备用方案 */
        @font-face {
            font-family: 'Orbitron-Fallback';
            src: local('Consolas'), local('Monaco'), local('Courier New');
            font-weight: 400 900;
            font-display: swap;
        }

        @font-face {
            font-family: 'ShareTechMono-Fallback';
            src: local('Consolas'), local('Monaco'), local('Courier New');
            font-weight: 400;
            font-display: swap;
        }

        @font-face {
            font-family: 'Exo2-Fallback';
            src: local('Arial'), local('Helvetica'), local('sans-serif');
            font-weight: 300 800;
            font-display: swap;
        }

        :root {
            /* 赛博朋克风格配色 */
            --cyber-pink: #ff0080;
            --cyber-pink-dark: #cc0066;
            --cyber-pink-light: #ff33a0;
            --cyber-pink-glow: rgba(255, 0, 128, 0.6);

            --cyber-cyan: #00ffff;
            --cyber-cyan-dark: #00cccc;
            --cyber-cyan-light: #33ffff;
            --cyber-cyan-glow: rgba(0, 255, 255, 0.6);

            --cyber-purple: #8a2be2;
            --cyber-purple-dark: #6a1bb2;
            --cyber-purple-light: #aa4bff;
            --cyber-purple-glow: rgba(138, 43, 226, 0.6);

            --cyber-green: #00ff41;
            --cyber-green-dark: #00cc33;
            --cyber-green-light: #33ff66;
            --cyber-green-glow: rgba(0, 255, 65, 0.6);

            --cyber-orange: #ff6600;
            --cyber-orange-dark: #cc5200;
            --cyber-orange-light: #ff8533;
            --cyber-orange-glow: rgba(255, 102, 0, 0.6);

            --cyber-yellow: #ffff00;
            --cyber-red: #ff0040;
            --cyber-red-glow: rgba(255, 0, 64, 0.6);

            /* 赛博朋克背景色 */
            --bg-primary: #0a0a0f;
            --bg-secondary: #1a1a2e;
            --bg-tertiary: #16213e;
            --bg-card: #0f0f23;
            --bg-glass: rgba(15, 15, 35, 0.85);
            --bg-matrix: rgba(0, 255, 65, 0.05);

            /* 赛博朋克文字颜色 */
            --text-primary: #ffffff;
            --text-secondary: #b8c5d6;
            --text-muted: #7a8ba0;
            --text-accent: #00ffff;
            --text-matrix: #00ff41;

            /* 赛博朋克边框颜色 */
            --border-primary: #2a2a4a;
            --border-accent: #00ffff;
            --border-glow: rgba(0, 255, 255, 0.4);
            --border-matrix: #00ff41;

            /* 赛博朋克渐变 */
            --gradient-cyber: linear-gradient(135deg, #ff0080 0%, #00ffff 100%);
            --gradient-matrix: linear-gradient(135deg, #00ff41 0%, #00ffff 100%);
            --gradient-neon: linear-gradient(135deg, #8a2be2 0%, #ff0080 100%);
            --gradient-danger: linear-gradient(135deg, #ff0040 0%, #ff6600 100%);
            --gradient-bg: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #16213e 100%);
            --gradient-card: linear-gradient(135deg, rgba(15, 15, 35, 0.9) 0%, rgba(26, 26, 46, 0.9) 100%);
            --gradient-glass: linear-gradient(135deg, rgba(0, 255, 255, 0.1) 0%, rgba(255, 0, 128, 0.1) 100%);

            /* 赛博朋克阴影 */
            --shadow-cyber: 0 0 30px var(--cyber-cyan-glow);
            --shadow-pink: 0 0 30px var(--cyber-pink-glow);
            --shadow-matrix: 0 0 30px var(--cyber-green-glow);
            --shadow-card: 0 8px 32px rgba(0, 0, 0, 0.6);
            --shadow-hover: 0 15px 50px rgba(0, 255, 255, 0.3);
            --shadow-neon: 0 0 40px rgba(255, 0, 128, 0.4);

            /* 边框半径 */
            --radius-sm: 0.125rem;
            --radius-md: 0.25rem;
            --radius-lg: 0.5rem;
            --radius-xl: 0.75rem;
            --radius-2xl: 1rem;

            /* 动画 */
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-fast: all 0.15s ease-out;
            --transition-glow: all 0.4s ease;
            --transition-matrix: all 0.6s ease-in-out;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Exo 2', 'Exo2-Fallback', 'Share Tech Mono', 'ShareTechMono-Fallback', 'Orbitron', 'Orbitron-Fallback', 'Arial', 'Helvetica', sans-serif;
            background: var(--gradient-bg);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            color: var(--text-primary);
            line-height: 1.6;
            font-weight: 400;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            position: relative;
            overflow-x: hidden;
        }

        /* 赛博朋克背景效果 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, var(--cyber-pink-glow) 0%, transparent 60%),
                radial-gradient(circle at 80% 20%, var(--cyber-cyan-glow) 0%, transparent 60%),
                radial-gradient(circle at 40% 40%, var(--cyber-purple-glow) 0%, transparent 60%),
                radial-gradient(circle at 60% 60%, var(--cyber-green-glow) 0%, transparent 60%);
            pointer-events: none;
            z-index: -1;
            animation: cyberGlow 12s ease-in-out infinite;
        }

        /* 赛博朋克矩阵网格 */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(0, 255, 255, 0.15) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 255, 0.15) 1px, transparent 1px),
                linear-gradient(45deg, rgba(255, 0, 128, 0.08) 1px, transparent 1px),
                linear-gradient(-45deg, rgba(0, 255, 65, 0.08) 1px, transparent 1px);
            background-size: 40px 40px, 40px 40px, 80px 80px, 80px 80px;
            pointer-events: none;
            z-index: -1;
            opacity: 0.4;
            animation: matrixFlow 25s linear infinite;
        }

        @keyframes cyberGlow {
            0%, 100% {
                opacity: 0.7;
                transform: scale(1) rotate(0deg);
            }
            33% {
                opacity: 1;
                transform: scale(1.1) rotate(120deg);
            }
            66% {
                opacity: 0.8;
                transform: scale(0.95) rotate(240deg);
            }
        }

        @keyframes matrixFlow {
            0% { transform: translate(0, 0) rotate(0deg); }
            25% { transform: translate(20px, -20px) rotate(90deg); }
            50% { transform: translate(40px, 40px) rotate(180deg); }
            75% { transform: translate(-20px, 20px) rotate(270deg); }
            100% { transform: translate(0, 0) rotate(360deg); }
        }

        @keyframes neonPulse {
            0%, 100% {
                box-shadow: 0 0 20px var(--cyber-cyan-glow);
                border-color: var(--cyber-cyan);
            }
            50% {
                box-shadow: 0 0 40px var(--cyber-pink-glow);
                border-color: var(--cyber-pink);
            }
        }

        @keyframes textGlitch {
            0%, 100% { transform: translateX(0); }
            10% { transform: translateX(-2px); }
            20% { transform: translateX(2px); }
            30% { transform: translateX(-1px); }
            40% { transform: translateX(1px); }
            50% { transform: translateX(0); }
        }

        /* 赛博朋克头部导航 */
        .sgs-header {
            background: var(--bg-glass);
            backdrop-filter: blur(25px);
            border-bottom: 3px solid var(--cyber-cyan);
            border-image: linear-gradient(90deg, var(--cyber-pink), var(--cyber-cyan), var(--cyber-purple)) 1;
            padding: 16px 0;
            box-shadow: var(--shadow-cyber);
            position: sticky;
            top: 0;
            z-index: 1000;
            animation: neonPulse 4s ease-in-out infinite;
        }

        .sgs-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><rect width="100" height="20" fill="url(%23headerPattern)"/><defs><pattern id="headerPattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,215,0,0.1)"/></pattern></defs></svg>');
            pointer-events: none;
        }

        .sgs-title {
            font-family: 'Orbitron', 'Orbitron-Fallback', 'Consolas', 'Monaco', monospace;
            font-size: 1.8rem;
            font-weight: 900;
            color: var(--cyber-cyan);
            text-shadow:
                0 0 10px var(--cyber-cyan-glow),
                0 0 20px var(--cyber-cyan-glow),
                0 0 30px var(--cyber-cyan-glow);
            margin: 0;
            letter-spacing: 3px;
            text-transform: uppercase;
            animation: textGlitch 3s ease-in-out infinite;
        }

        .sgs-subtitle {
            font-family: 'Share Tech Mono', 'ShareTechMono-Fallback', 'Consolas', 'Monaco', monospace;
            color: var(--cyber-pink);
            font-size: 0.9rem;
            opacity: 0.9;
            margin: 0;
            font-weight: 500;
            text-shadow: 0 0 8px var(--cyber-pink-glow);
            letter-spacing: 1px;
        }

        /* 赛博朋克用户卡片 */
        .sgs-player-card {
            background: var(--gradient-card);
            border: 3px solid transparent;
            border-image: linear-gradient(45deg, var(--cyber-pink), var(--cyber-cyan), var(--cyber-purple), var(--cyber-green)) 1;
            border-radius: var(--radius-xl);
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: var(--shadow-neon);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(15px);
        }

        .sgs-player-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
            animation: royalGlow 8s ease-in-out infinite;
        }

        @keyframes royalGlow {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.5; }
            50% { transform: translateY(-10px) rotate(180deg); opacity: 0.8; }
        }

        .sgs-player-info {
            position: relative;
            z-index: 2;
        }

        .sgs-player-name {
            font-family: 'Orbitron', monospace;
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--cyber-cyan);
            text-shadow:
                0 0 10px var(--cyber-cyan-glow),
                0 0 20px var(--cyber-cyan-glow);
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .sgs-account-type {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-left: 12px;
            border: 2px solid transparent;
        }

        .sgs-type-premium {
            background: var(--gradient-neon);
            color: var(--text-primary);
            box-shadow: var(--shadow-pink);
            border-color: var(--cyber-pink);
            text-shadow: 0 0 8px var(--cyber-pink-glow);
        }

        .sgs-type-normal {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border-color: var(--border-primary);
            text-shadow: 0 0 5px var(--cyber-cyan-glow);
        }

        .sgs-time-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 16px;
        }

        .sgs-countdown {
            font-family: 'Share Tech Mono', monospace;
            font-size: 2rem;
            font-weight: 900;
            color: var(--cyber-green);
            text-shadow:
                0 0 10px var(--cyber-green-glow),
                0 0 20px var(--cyber-green-glow),
                0 0 30px var(--cyber-green-glow);
            letter-spacing: 3px;
            animation: textGlitch 2s ease-in-out infinite;
        }

        /* 赛博朋克搜索区域 */
        .sgs-search-panel {
            background: var(--gradient-card);
            border: 2px solid transparent;
            border-image: linear-gradient(90deg, var(--cyber-cyan), var(--cyber-purple)) 1;
            border-radius: var(--radius-xl);
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: var(--shadow-cyber);
            backdrop-filter: blur(15px);
        }

        .sgs-search-title {
            font-family: 'Orbitron', monospace;
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--cyber-cyan);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 0 0 10px var(--cyber-cyan-glow);
        }

        /* 赛博朋克账号列表区域 */
        .sgs-accounts-panel {
            background: var(--gradient-card);
            border: 3px solid transparent;
            border-image: linear-gradient(135deg, var(--cyber-pink), var(--cyber-cyan), var(--cyber-green)) 1;
            border-radius: var(--radius-xl);
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: var(--shadow-neon);
            backdrop-filter: blur(15px);
        }

        /* 赛博朋克账号卡片 */
        .sgs-account-card {
            background: var(--bg-card);
            border: 2px solid var(--cyber-cyan);
            border-radius: var(--radius-lg);
            padding: 20px;
            margin-bottom: 20px;
            transition: var(--transition);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-cyber);
            backdrop-filter: blur(10px);
        }

        .sgs-account-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-cyber);
            transform: scaleX(0);
            transition: var(--transition);
        }

        .sgs-account-card:hover::before {
            transform: scaleX(1);
        }

        .sgs-account-card:hover {
            border-color: var(--cyber-pink);
            transform: translateY(-10px);
            box-shadow: var(--shadow-hover);
            animation: neonPulse 2s ease-in-out infinite;
        }

        .sgs-account-card.premium {
            border-color: var(--cyber-pink);
            background: linear-gradient(135deg, var(--bg-card) 0%, rgba(255, 0, 128, 0.15) 100%);
            box-shadow: var(--shadow-pink);
        }

        .sgs-account-card.in-use {
            border-color: var(--cyber-green);
            background: linear-gradient(135deg, var(--bg-card) 0%, rgba(0, 255, 65, 0.15) 100%);
            box-shadow: var(--shadow-matrix);
        }

        /* 账号头部信息 */
        .sgs-account-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid rgba(255, 215, 0, 0.3);
        }

        .sgs-account-title {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .sgs-level-badge {
            background: linear-gradient(135deg, var(--cyber-cyan), #0080ff);
            color: var(--bg-primary);
            font-size: 1.2rem;
            font-weight: 900;
            padding: 8px 16px;
            border-radius: 20px;
            border: 1px solid #0080ff;
            min-width: 80px;
            text-align: center;
            text-shadow: none;
        }

        .sgs-account-name {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--text-primary);
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
            margin: 0;
        }

        .sgs-badges {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-top: 8px;
        }

        .sgs-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .sgs-badge-premium {
            background: linear-gradient(135deg, var(--cyber-pink), #ff1493);
            color: var(--text-primary);
            border: 1px solid #ff1493;
        }

        .sgs-badge-normal {
            background: rgba(255, 255, 255, 0.2);
            color: #ccc;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .sgs-badge-vip {
            background: linear-gradient(135deg, var(--cyber-pink), #ff1493);
            color: var(--text-primary);
            text-shadow: 0 0 5px var(--cyber-pink-glow);
            border: 1px solid #ff1493;
            animation: vip-glow 2s ease-in-out infinite alternate;
        }

        @keyframes vip-glow {
            from { box-shadow: 0 0 5px var(--cyber-pink); }
            to { box-shadow: 0 0 15px var(--cyber-pink), 0 0 25px var(--cyber-pink); }
        }

        /* 官阶徽章样式 */
        .sgs-badge-rank {
            background: linear-gradient(135deg, var(--cyber-purple), #9932cc);
            color: var(--text-primary);
            text-shadow: 0 0 5px var(--cyber-purple);
            border: 1px solid #9932cc;
        }

        /* 账号统计信息 - 属性面板风格 */
        .sgs-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 12px;
            margin: 16px 0;
        }

        .sgs-stat-item {
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px 8px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .sgs-stat-item:hover {
            background: rgba(0, 0, 0, 0.3);
            transform: translateY(-2px);
            border-color: var(--cyber-cyan);
        }

        .sgs-stat-icon {
            font-size: 1.5rem;
            margin-bottom: 8px;
            display: block;
        }

        .sgs-stat-value {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .sgs-stat-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* 武将和皮肤展示 - 收藏品风格 */
        .sgs-features {
            margin: 16px 0;
        }

        .sgs-feature-section {
            margin-bottom: 12px;
        }

        .sgs-feature-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .sgs-feature-list {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .sgs-feature-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .sgs-general-tag {
            background: var(--cyber-pink);
            color: var(--text-primary);
            border: 1px solid #ff1493;
        }

        .sgs-skin-tag {
            background: var(--cyber-green);
            color: var(--bg-primary);
            border: 1px solid #32cd32;
        }

        .sgs-dynamic-skin-tag {
            background: var(--cyber-purple);
            color: var(--text-primary);
            border: 1px solid #9932cc;
        }

        /* 操作按钮 - 三国杀按钮风格 */
        .sgs-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
            align-items: center;
            margin-top: 16px;
            padding-top: 12px;
            border-top: 1px solid rgba(255, 215, 0, 0.3);
        }

        .sgs-btn {
            border: 2px solid transparent;
            border-radius: var(--radius-lg);
            padding: 10px 20px;
            font-family: 'Orbitron', monospace;
            font-weight: 600;
            font-size: 0.8rem;
            transition: var(--transition);
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            backdrop-filter: blur(5px);
        }

        .sgs-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .sgs-btn:hover::before {
            left: 100%;
        }

        .sgs-btn-primary {
            background: var(--gradient-cyber);
            color: var(--text-primary);
            border-color: var(--cyber-cyan);
            box-shadow: var(--shadow-cyber);
            text-shadow: 0 0 8px var(--cyber-cyan-glow);
        }

        .sgs-btn-primary:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-hover);
            color: var(--text-primary);
            animation: neonPulse 1s ease-in-out infinite;
        }

        .sgs-btn-success {
            background: var(--gradient-matrix);
            color: var(--text-primary);
            border-color: var(--cyber-green);
            box-shadow: var(--shadow-matrix);
            text-shadow: 0 0 8px var(--cyber-green-glow);
        }

        .sgs-btn-success:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-hover);
            color: var(--text-primary);
            animation: neonPulse 1s ease-in-out infinite;
        }

        .sgs-btn-warning {
            background: var(--gradient-neon);
            color: var(--text-primary);
            border-color: var(--cyber-purple);
            box-shadow: 0 0 20px var(--cyber-purple-glow);
            text-shadow: 0 0 8px var(--cyber-purple-glow);
        }

        .sgs-btn-warning:hover {
            transform: translateY(-4px);
            box-shadow: 0 15px 50px var(--cyber-purple-glow);
            animation: neonPulse 1s ease-in-out infinite;
        }

        .sgs-btn-outline {
            background: var(--bg-glass);
            border: 2px solid var(--cyber-cyan);
            color: var(--cyber-cyan);
            text-shadow: 0 0 5px var(--cyber-cyan-glow);
        }

        .sgs-btn-outline:hover {
            background: var(--cyber-cyan);
            color: var(--bg-primary);
            transform: translateY(-4px);
            box-shadow: var(--shadow-cyber);
            text-shadow: none;
        }

        .sgs-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .sgs-btn:disabled:hover {
            transform: none !important;
            box-shadow: none !important;
        }

        /* 标签页 - 古典书签风格 */
        .sgs-tabs {
            display: flex;
            gap: 4px;
            margin-bottom: 20px;
            border-bottom: 2px solid var(--sgs-gold);
            padding-bottom: 0;
        }

        .sgs-tab {
            background: linear-gradient(135deg, var(--sgs-dark-brown) 0%, var(--sgs-black) 100%);
            border: 2px solid var(--sgs-bronze);
            border-bottom: none;
            border-radius: 12px 12px 0 0;
            padding: 12px 24px;
            color: #ccc;
            font-weight: 600;
            font-size: 0.9rem;
            text-decoration: none;
            transition: var(--sgs-transition);
            position: relative;
            cursor: pointer;
        }

        .sgs-tab.active {
            background: var(--sgs-gold-gradient);
            border-color: var(--sgs-gold);
            color: var(--sgs-black);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
        }

        .sgs-tab:hover:not(.active) {
            background: linear-gradient(135deg, var(--sgs-bronze) 0%, var(--sgs-dark-brown) 100%);
            color: var(--sgs-light-gold);
            transform: translateY(-1px);
        }

        .sgs-tab-count {
            background: var(--sgs-red);
            color: white;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 8px;
            margin-left: 8px;
            font-weight: 700;
        }

        /* 空状态 - 古典风格 */
        .sgs-empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #888;
        }

        .sgs-empty-icon {
            font-size: 4rem;
            margin-bottom: 16px;
            opacity: 0.3;
            color: var(--sgs-bronze);
        }

        .sgs-empty-text {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 8px;
        }

        .sgs-empty-hint {
            font-size: 0.9rem;
            color: #888;
        }

        /* 加载动画 - 三国杀风格 */
        .sgs-loading {
            display: inline-block;
            width: 24px;
            height: 24px;
            border: 3px solid rgba(255, 215, 0, 0.3);
            border-top: 3px solid var(--sgs-gold);
            border-radius: 50%;
            animation: sgsSpinning 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes sgsSpinning {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 时间警告 - 紧急通知风格 */
        .sgs-time-warning {
            background: linear-gradient(135deg, #8b0000 0%, #d32f2f 100%);
            border: 2px solid var(--sgs-red);
            border-radius: var(--sgs-border-radius);
            padding: 16px;
            margin-bottom: 20px;
            color: white;
            box-shadow: var(--sgs-shadow);
            animation: warningPulse 2s ease-in-out infinite;
        }

        @keyframes warningPulse {
            0%, 100% { box-shadow: var(--sgs-shadow); }
            50% { box-shadow: 0 8px 32px rgba(211, 47, 47, 0.6); }
        }

        .sgs-warning-icon {
            color: var(--sgs-gold);
            font-size: 1.2rem;
            margin-right: 8px;
        }

        /* 搜索框 - 古典输入框风格 */
        .sgs-search-input {
            background: rgba(255, 248, 220, 0.9);
            border: 2px solid var(--sgs-bronze);
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 1rem;
            color: var(--sgs-dark-brown);
            transition: var(--sgs-transition);
            width: 100%;
        }

        .sgs-search-input:focus {
            outline: none;
            border-color: var(--sgs-gold);
            box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.2);
            background: var(--sgs-light-gold);
        }

        .sgs-search-input::placeholder {
            color: #888;
            font-style: italic;
        }

        /* 赛博朋克详情页面样式 */
        .sgs-details-panel {
            background: linear-gradient(135deg, rgba(15, 15, 35, 0.95) 0%, rgba(26, 26, 46, 0.95) 100%);
            border: 3px solid var(--cyber-cyan);
            border-radius: var(--radius-xl);
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: var(--shadow-cyber);
            backdrop-filter: blur(15px);
        }

        .sgs-details-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid var(--cyber-cyan);
        }

        .sgs-details-title {
            font-family: 'Orbitron', monospace;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--cyber-cyan);
            margin: 0;
            text-shadow: 0 0 10px var(--cyber-cyan-glow);
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .sgs-details-content {
            color: var(--text-primary);
            line-height: 1.8;
        }

        .sgs-details-content h3 {
            font-family: 'Orbitron', monospace;
            color: var(--cyber-pink);
            text-shadow: 0 0 8px var(--cyber-pink-glow);
            font-size: 1.2rem;
            margin-top: 20px;
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .sgs-details-content p {
            color: var(--text-primary);
            font-size: 1rem;
            margin-bottom: 8px;
            background: rgba(0, 255, 255, 0.1);
            padding: 8px 12px;
            border-radius: var(--radius-md);
            border-left: 3px solid var(--cyber-cyan);
        }

        .sgs-details-content strong {
            color: var(--cyber-green);
            text-shadow: 0 0 5px var(--cyber-green-glow);
            font-weight: 600;
        }

        .sgs-details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin: 20px 0;
        }

        .sgs-details-item {
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid var(--cyber-cyan);
            border-radius: var(--radius-md);
            padding: 12px;
        }

        .sgs-details-item-label {
            font-family: 'Share Tech Mono', monospace;
            color: var(--cyber-cyan);
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 4px;
        }

        .sgs-details-item-value {
            color: var(--text-primary);
            font-size: 1.1rem;
            font-weight: 500;
        }

        /* 特色标签样式 */
        .sgs-feature-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 12px 0;
        }

        .sgs-feature-tag {
            padding: 6px 12px;
            border-radius: var(--radius-md);
            font-size: 0.9rem;
            font-weight: 600;
            text-shadow: 0 0 5px currentColor;
        }

        .sgs-tag-premium {
            background: var(--cyber-pink);
            color: var(--text-primary);
        }

        .sgs-tag-dynamic {
            background: var(--cyber-purple);
            color: var(--text-primary);
        }

        .sgs-tag-normal {
            background: var(--cyber-green);
            color: var(--bg-primary);
        }

        /* 账号类型标识 */
        .sgs-account-type-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 2px solid var(--border-primary);
        }

        .sgs-account-type-badge {
            font-family: 'Orbitron', monospace;
            font-size: 0.9rem;
            font-weight: 700;
            padding: 8px 16px;
            border-radius: var(--radius-lg);
            text-transform: uppercase;
            letter-spacing: 1px;
            display: flex;
            align-items: center;
            gap: 8px;
            border: 2px solid;
        }

        .premium-type {
            background: linear-gradient(135deg, var(--cyber-pink) 0%, var(--cyber-purple) 100%);
            color: var(--text-primary);
            border-color: var(--cyber-pink);
            box-shadow: 0 0 15px var(--cyber-pink-glow);
            text-shadow: 0 0 8px var(--cyber-pink-glow);
        }

        .normal-type {
            background: linear-gradient(135deg, var(--cyber-cyan) 0%, var(--cyber-green) 100%);
            color: var(--bg-primary);
            border-color: var(--cyber-cyan);
            box-shadow: 0 0 15px var(--cyber-cyan-glow);
            font-weight: 600;
        }

        .sgs-status-badge {
            font-size: 0.8rem;
            font-weight: 600;
            padding: 4px 12px;
            border-radius: var(--radius-md);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .sgs-status-in-use {
            background: var(--cyber-red);
            color: var(--text-primary);
            box-shadow: 0 0 10px var(--cyber-red-glow);
        }

        .sgs-status-available {
            background: var(--cyber-green);
            color: var(--bg-primary);
            box-shadow: 0 0 10px var(--cyber-green-glow);
        }

        /* 账号区域分隔 */
        .sgs-account-section {
            margin-bottom: 32px;
        }

        .sgs-section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            margin-bottom: 20px;
            border-radius: var(--radius-xl);
            border: 2px solid;
        }

        .premium-section {
            background: linear-gradient(135deg, rgba(255, 0, 128, 0.1) 0%, rgba(138, 43, 226, 0.1) 100%);
            border-color: var(--cyber-pink);
            box-shadow: 0 0 20px var(--cyber-pink-glow);
        }

        .normal-section {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.1) 0%, rgba(0, 255, 65, 0.1) 100%);
            border-color: var(--cyber-cyan);
            box-shadow: 0 0 20px var(--cyber-cyan-glow);
        }

        .sgs-section-title {
            font-family: 'Orbitron', monospace;
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .premium-section .sgs-section-title {
            color: var(--cyber-pink);
            text-shadow: 0 0 10px var(--cyber-pink-glow);
        }

        .normal-section .sgs-section-title {
            color: var(--cyber-cyan);
            text-shadow: 0 0 10px var(--cyber-cyan-glow);
        }

        .sgs-section-count {
            font-family: 'Share Tech Mono', monospace;
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-secondary);
            background: rgba(0, 0, 0, 0.3);
            padding: 6px 12px;
            border-radius: var(--radius-md);
            border: 1px solid var(--border-primary);
        }

        .sgs-accounts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .sgs-accounts-grid {
                grid-template-columns: 1fr;
            }

            .sgs-section-header {
                flex-direction: column;
                gap: 12px;
                text-align: center;
            }
        }

        .sgs-details-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid var(--sgs-gold);
        }

        .sgs-details-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--sgs-dark-brown);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* 响应式设计 - 大屏幕 (1200px+) */
        @media (min-width: 1200px) {
            .container {
                max-width: 1400px;
            }

            .sgs-accounts-grid {
                grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            }
        }

        /* 响应式设计 - 平板电脑横屏 (992px - 1199px) */
        @media (max-width: 1199px) and (min-width: 992px) {
            .sgs-accounts-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .sgs-stats-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        /* 响应式设计 - 平板电脑竖屏 (768px - 991px) */
        @media (max-width: 991px) and (min-width: 768px) {
            .sgs-accounts-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
            }

            .sgs-stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .sgs-player-card {
                padding: 20px;
            }

            .sgs-search-panel, .sgs-accounts-panel, .sgs-details-panel {
                padding: 20px;
            }
        }

        /* 响应式设计 - 手机横屏 (576px - 767px) */
        @media (max-width: 767px) and (min-width: 576px) {
            .container {
                padding: 0 15px;
            }

            .sgs-header {
                padding: 12px 0;
            }

            .sgs-title {
                font-size: 1.2rem;
                letter-spacing: 1px;
            }

            .sgs-subtitle {
                font-size: 0.7rem;
            }

            .sgs-player-card {
                padding: 16px;
                margin-bottom: 16px;
            }

            .sgs-search-panel, .sgs-accounts-panel, .sgs-details-panel {
                padding: 16px;
                margin-bottom: 16px;
            }

            .sgs-account-card {
                padding: 16px;
            }

            .sgs-accounts-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .sgs-stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }

            .sgs-actions {
                flex-direction: column;
                gap: 8px;
            }

            .sgs-btn {
                width: 100%;
                justify-content: center;
                padding: 10px 16px;
                font-size: 0.9rem;
            }

            .sgs-tabs {
                flex-wrap: wrap;
                gap: 6px;
            }

            .sgs-tab {
                padding: 8px 12px;
                font-size: 0.8rem;
            }

            .sgs-countdown {
                font-size: 1.3rem;
            }

            .sgs-section-header {
                flex-direction: column;
                gap: 12px;
                text-align: center;
            }
        }

        /* 响应式设计 - 手机端优化 (最大575px) - 极致紧凑版 */
        @media (max-width: 575px) {
            .container {
                padding: 0;
                max-width: 100%;
            }

            .sgs-header {
                padding: 2px 0;
            }

            .sgs-header .d-flex {
                flex-direction: row;
                gap: 2px;
                align-items: center;
                justify-content: space-between;
            }

            .sgs-title {
                font-size: 0.7rem;
                letter-spacing: 0.2px;
                font-weight: 700;
            }

            .sgs-subtitle {
                font-size: 0.4rem;
                margin-bottom: 1px;
            }

            .sgs-player-card {
                padding: 2px;
                margin-bottom: 2px;
            }

            .sgs-player-name {
                font-size: 0.6rem;
                flex-direction: row;
                align-items: center;
                gap: 2px;
                flex-wrap: wrap;
                font-weight: 600;
            }

            .sgs-search-panel, .sgs-accounts-panel, .sgs-details-panel {
                padding: 2px;
                margin-bottom: 2px;
            }

            .sgs-account-card {
                padding: 2px;
                margin-bottom: 1px;
            }

            .sgs-accounts-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1px;
            }

            .sgs-stats-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 1px;
            }

            .sgs-account-header {
                flex-direction: row;
                align-items: center;
                gap: 2px;
                flex-wrap: wrap;
            }

            .sgs-account-title {
                flex-direction: row;
                align-items: center;
                gap: 2px;
                font-size: 0.5rem;
                flex-wrap: wrap;
                font-weight: 600;
            }

            .sgs-time-info {
                flex-direction: row;
                gap: 2px;
                text-align: left;
                font-size: 0.4rem;
                flex-wrap: wrap;
                font-weight: 500;
            }

            .sgs-actions {
                flex-direction: row;
                gap: 1px;
                flex-wrap: wrap;
            }

            .sgs-btn {
                flex: 1;
                min-width: 35px;
                padding: 1px 2px;
                font-size: 0.4rem;
                font-weight: 600;
            }

            .sgs-tabs {
                flex-direction: row;
                gap: 1px;
                flex-wrap: wrap;
            }

            .sgs-tab {
                padding: 1px 2px;
                font-size: 0.4rem;
                text-align: center;
                flex: 1;
                min-width: 30px;
                font-weight: 600;
            }

            /* 倒计时特别优化 - 保持清晰但缩小 */
            .sgs-countdown {
                font-size: 0.7rem !important;
                font-weight: 700 !important;
                text-shadow: 0 0 6px currentColor !important;
            }

            .sgs-section-header {
                flex-direction: row;
                gap: 2px;
                text-align: left;
                align-items: center;
            }

            .sgs-section-title {
                font-size: 0.5rem;
                font-weight: 600;
            }

            .sgs-section-count {
                font-size: 0.4rem;
                padding: 1px 2px;
                font-weight: 600;
            }

            /* 移动端详情页面优化 - 图2效果版 */
            .sgs-details-panel {
                padding: 2px;
            }

            .sgs-details-panel h3 {
                font-size: 0.6rem;
                margin-bottom: 2px;
                font-weight: 700;
            }

            .sgs-details-panel .row {
                margin: 0;
            }

            .sgs-details-panel .col-md-6 {
                padding: 0 1px;
                margin-bottom: 2px;
            }

            /* 移动端统计卡片优化 - 4列布局 */
            .sgs-stats-container {
                grid-template-columns: repeat(4, 1fr) !important;
                gap: 1px !important;
                margin-bottom: 2px !important;
            }

            /* 移动端登录信息优化 */
            .sgs-details-item {
                margin-bottom: 2px;
            }

            .sgs-details-item-label {
                font-size: 0.5rem;
                margin-bottom: 1px;
                font-weight: 600;
            }

            .sgs-details-item-value {
                font-size: 0.5rem;
            }

            .sgs-details-item-value span {
                padding: 2px 3px !important;
                font-size: 0.5rem !important;
                font-weight: 600 !important;
            }

            .sgs-details-item-value button {
                padding: 1px 3px !important;
                font-size: 0.5rem !important;
                font-weight: 600 !important;
            }

            /* 移动端标签优化 - 图2效果版 */
            .sgs-tag-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr)) !important;
                gap: 1px !important;
            }

            .sgs-tag {
                padding: 1px !important;
                font-size: 0.4rem !important;
                border-radius: 1px !important;
                line-height: 1 !important;
                text-align: center !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                white-space: nowrap !important;
                font-weight: 600 !important;
            }

            .sgs-tag i {
                font-size: 0.35rem !important;
                margin-right: 0 !important;
            }

            /* 移动端区域标题优化 */
            .sgs-details-panel h3 {
                font-size: 0.5rem !important;
                margin-bottom: 2px !important;
                font-weight: 700 !important;
            }

            /* 移动端区域容器优化 */
            .sgs-details-panel > div[style*="margin-bottom: 25px"] {
                margin-bottom: 2px !important;
            }

            .sgs-details-panel > div[style*="padding: 15px"] {
                padding: 2px !important;
            }

            /* 移动端统计卡片进一步优化 */
            .sgs-details-panel [style*="padding: 20px; text-align: center"] {
                padding: 2px 1px !important;
            }

            .sgs-details-panel [style*="font-size: 2.5rem"] {
                font-size: 0.8rem !important;
            }

            .sgs-details-panel [style*="font-size: 1.8rem"] {
                font-size: 0.6rem !important;
            }

            .sgs-details-panel [style*="font-size: 0.9rem"] {
                font-size: 0.4rem !important;
            }

            /* 移动端账号列表标签优化 - 极致紧凑版 */
            .sgs-feature-list {
                gap: 0 !important;
                margin: 1px 0 !important;
            }

            .sgs-feature-tag {
                padding: 0 1px !important;
                font-size: 0.35rem !important;
                border-radius: 1px !important;
                margin: 0 !important;
                line-height: 1 !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                white-space: nowrap !important;
                font-weight: 600 !important;
            }

            .sgs-feature-section {
                margin-bottom: 1px !important;
            }

            .sgs-feature-title {
                font-size: 0.4rem !important;
                margin-bottom: 0 !important;
                font-weight: 700 !important;
            }

            /* 移动端账号卡片优化 - 极致紧凑版 */
            .sgs-account-card {
                padding: 1px !important;
                margin-bottom: 1px !important;
            }

            .sgs-account-header {
                margin-bottom: 1px !important;
            }

            .sgs-account-title {
                font-size: 0.45rem !important;
                font-weight: 700 !important;
            }

            .sgs-account-subtitle {
                font-size: 0.35rem !important;
                font-weight: 600 !important;
            }

            .sgs-stats-grid {
                gap: 0 !important;
                margin: 1px 0 !important;
            }

            .sgs-stat-item {
                padding: 0 !important;
                font-size: 0.35rem !important;
            }

            .sgs-stat-value {
                font-size: 0.4rem !important;
                font-weight: 700 !important;
            }

            .sgs-stat-label {
                font-size: 0.3rem !important;
                font-weight: 600 !important;
            }

            /* 确保账号列表双列布局在极小屏幕上也能正常显示 */
            .sgs-accounts-grid .sgs-account-card {
                min-width: 0 !important;
                width: 100% !important;
                box-sizing: border-box !important;
            }

            /* 进一步压缩账号卡片内容 */
            .sgs-account-card .sgs-feature-list {
                display: grid !important;
                grid-template-columns: repeat(auto-fill, minmax(25px, 1fr)) !important;
                gap: 0 !important;
            }

            /* 确保倒计时在小屏幕上仍然可见 */
            .sgs-countdown {
                min-height: 20px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            /* 移动端返回按钮优化 */
            .sgs-details-header button {
                font-size: 0.6rem !important;
                padding: 4px 8px !important;
                min-width: 80px !important;
            }

            .sgs-details-header {
                flex-direction: column !important;
                gap: 4px !important;
                text-align: center !important;
            }

            .sgs-details-title {
                font-size: 0.7rem !important;
                margin-bottom: 2px !important;
            }
        }
    </style>
</head>
<body>
    <!-- 三国杀风格头部 -->
    <div class="sgs-header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="sgs-title">🎮 游戏账号管理-三国杀手游版</h1>
                    <p class="sgs-subtitle">// 大神账号已就位，等你来战！</p>
                </div>
                <div>
                    <button class="sgs-btn sgs-btn-outline me-2" onclick="refreshData()">
                        <i class="bi bi-arrow-clockwise"></i>刷新
                    </button>
                    <button class="sgs-btn sgs-btn-outline me-2" onclick="showDeviceInfo()">
                        <i class="bi bi-shield-check"></i>设备
                    </button>
                    <button class="sgs-btn sgs-btn-primary" onclick="logout()">
                        <i class="bi bi-box-arrow-right"></i>退出
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <!-- 玩家信息卡片 -->
        <div class="sgs-player-card">
            <div class="sgs-player-info">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="sgs-player-name">
                            <i class="bi bi-person-badge me-2"></i>
                            玩家：<span id="customerAccount">-</span>
                            <span id="accountTypeBadge" class="sgs-account-type">-</span>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-hourglass-split me-2" style="color: var(--sgs-gold);"></i>
                                    <span>剩余时长：<strong id="remainingHours">-</strong> 小时</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-calendar-check me-2" style="color: var(--sgs-gold);"></i>
                                    <span>总时长：<strong id="totalHours">-</strong> 小时</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="sgs-time-info">
                            <div class="text-center">
                                <div class="sgs-countdown" id="countdownTime">--:--:--</div>
                                <small style="color: var(--sgs-light-gold);">剩余时长倒计时</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 时间警告 -->
        <div id="timeWarning" class="sgs-time-warning" style="display: none;">
            <i class="bi bi-exclamation-triangle sgs-warning-icon"></i>
            <span id="warningMessage"></span>
        </div>

        <!-- 搜索区域 -->
        <div class="sgs-search-panel">
            <div class="sgs-search-title">
                <i class="bi bi-search"></i>寻找心仪账号
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="text" class="sgs-search-input" id="searchInput" placeholder="输入武将名称、皮肤名称或账号昵称...">
                        <button class="sgs-btn sgs-btn-primary ms-2" onclick="searchAccounts()">
                            <i class="bi bi-search"></i>搜索
                        </button>
                    </div>
                </div>
                <div class="col-md-4">
                    <select class="sgs-search-input" id="accountTypeFilter">
                        <option value="">所有账号类型</option>
                        <option value="premium">高级账号</option>
                        <option value="normal">普通账号</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 账号选择区域 -->
        <div class="sgs-accounts-panel" id="accountsSection">
            <!-- 三国杀风格标签页 -->
            <div class="sgs-tabs">
                <div class="sgs-tab active" id="available-tab" onclick="switchTab('available')">
                    <i class="bi bi-person-plus me-2"></i>可用账号
                    <span class="sgs-tab-count" id="availableCount">0</span>
                </div>
                <div class="sgs-tab" id="inuse-tab" onclick="switchTab('inuse')">
                    <i class="bi bi-person-check me-2"></i>使用中账号
                    <span class="sgs-tab-count" id="inUseCount">0</span>
                </div>
            </div>

            <!-- 标签页内容 -->
            <div class="tab-content">
                <!-- 可使用账号 -->
                <div class="tab-pane show active" id="available">
                    <div id="availableAccountsContainer">
                        <div class="sgs-empty-state">
                            <div class="sgs-loading"></div>
                            <div class="sgs-empty-text">正在加载账号信息...</div>
                        </div>
                    </div>
                </div>

                <!-- 使用中账号 -->
                <div class="tab-pane" id="inuse" style="display: none;">
                    <div id="inUseAccountsContainer">
                        <div class="sgs-empty-state">
                            <div class="sgs-loading"></div>
                            <div class="sgs-empty-text">正在加载使用信息...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 账号详情页面 -->
        <div class="sgs-details-panel" id="accountDetailsSection" style="display: none;">
            <div class="sgs-details-header">
                <h2 class="sgs-details-title">
                    <i class="bi bi-person-badge"></i>账号详情
                </h2>
                <button class="sgs-btn sgs-btn-warning" onclick="backToAccountList()" style="font-size: 1.2rem; padding: 12px 24px; font-weight: 700; background: linear-gradient(135deg, #ff6b35, #f7931e); border: 2px solid #ff6b35; box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);">
                    <i class="bi bi-arrow-left me-2"></i>返回账号列表
                </button>
            </div>

            <div id="accountDetailsContent">
                <!-- 详情内容将通过JavaScript动态填充 -->
            </div>
        </div>
    </div>

    <!-- 账号详情模态框 -->
    <div class="modal fade" id="accountDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content" style="background: var(--gradient-card); border: 3px solid var(--cyber-cyan); color: var(--text-primary); backdrop-filter: blur(20px);">
                <div class="modal-header" style="border-bottom: 2px solid var(--cyber-cyan);">
                    <h5 class="modal-title" style="color: var(--cyber-cyan); font-weight: 700; font-family: 'Orbitron', monospace; text-shadow: 0 0 10px var(--cyber-cyan-glow); text-transform: uppercase; letter-spacing: 1px;">
                        <i class="bi bi-person-badge me-2"></i>账号详细信息
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="modalAccountDetailsContent">
                    <!-- 详情内容将通过JavaScript动态填充 -->
                </div>
                <div class="modal-footer" style="border-top: 2px solid var(--cyber-cyan);">
                    <button type="button" class="sgs-btn sgs-btn-outline" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 设备信息模态框 -->
    <div class="modal fade" id="deviceInfoModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="background: var(--gradient-card); border: 3px solid var(--cyber-cyan); color: var(--text-primary); backdrop-filter: blur(20px);">
                <div class="modal-header" style="border-bottom: 2px solid var(--cyber-cyan);">
                    <h5 class="modal-title" style="color: var(--cyber-cyan); font-weight: 700; font-family: 'Orbitron', monospace; text-shadow: 0 0 10px var(--cyber-cyan-glow); text-transform: uppercase; letter-spacing: 1px;">
                        <i class="bi bi-shield-check me-2"></i>设备安全信息
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="deviceInfoContent">
                    <!-- 设备信息内容将通过JavaScript动态填充 -->
                </div>
                <div class="modal-footer" style="border-top: 2px solid var(--cyber-cyan);">
                    <button type="button" class="sgs-btn sgs-btn-outline" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/customer-dashboard.js"></script>

    <script>
        // 标签页切换功能
        function switchTab(tabName) {
            // 移除所有活动状态
            document.querySelectorAll('.sgs-tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('show', 'active');
                pane.style.display = 'none';
            });

            // 激活选中的标签页
            document.getElementById(tabName + '-tab').classList.add('active');
            const targetPane = document.getElementById(tabName);
            targetPane.classList.add('show', 'active');
            targetPane.style.display = 'block';
        }
    </script>
</body>
</html>
