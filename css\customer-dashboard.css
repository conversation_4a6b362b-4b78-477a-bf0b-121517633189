/**
 * 客户端面板样式文件
 * 为客户端提供美观的界面设计
 */

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 2.5rem;
    font-weight: 700;
}

.user-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.user-details {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.user-detail-item {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 10px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* 卡片样式 */
.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.card-title {
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 0.95rem;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 5px;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.btn-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.btn-warning {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
    color: #212529;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
}

.btn-secondary {
    background: linear-gradient(45deg, #6c757d, #5a6268);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

/* 表单样式 */
.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #495057;
}

/* 搜索区域样式 */
.search-section {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
}

.search-controls {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.search-controls .form-group {
    flex: 1;
    min-width: 200px;
    margin-bottom: 0;
}

/* 账号列表样式 */
.accounts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.account-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 20px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.account-card:hover {
    border-color: #667eea;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.account-card.premium {
    border-color: #ffd700;
    background: linear-gradient(135deg, #fff9e6, #ffffff);
}

.account-card.premium::before {
    content: "⭐";
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 1.2rem;
}

.account-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.account-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
}

.account-type {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.account-type.premium {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #8b6914;
}

.account-type.normal {
    background: linear-gradient(45deg, #e9ecef, #dee2e6);
    color: #495057;
}

.account-details {
    margin-bottom: 15px;
}

.account-detail {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.account-detail .label {
    color: #6c757d;
    font-weight: 500;
}

.account-detail .value {
    color: #2c3e50;
    font-weight: 600;
}

/* 当前账号样式 */
.current-account {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border-color: #28a745;
}

.current-account .account-header {
    border-bottom: 2px solid #28a745;
    padding-bottom: 10px;
}

/* 统计信息样式 */
.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

/* 警告和提示样式 */
.alert {
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    border: 1px solid transparent;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border-color: #28a745;
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border-color: #dc3545;
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border-color: #ffc107;
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    border-color: #17a2b8;
    color: #0c5460;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .user-info {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-controls {
        flex-direction: column;
    }
    
    .search-controls .form-group {
        min-width: auto;
    }
    
    .accounts-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-row {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .stats-row {
        grid-template-columns: 1fr;
    }
    
    .user-details {
        flex-direction: column;
        gap: 10px;
    }
    
    .btn {
        width: 100%;
        margin: 5px 0;
    }
}

/* 模态框样式 */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 2px solid #f8f9fa;
    border-radius: 15px 15px 0 0;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.modal-footer {
    border-top: 2px solid #f8f9fa;
    border-radius: 0 0 15px 15px;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* 特殊效果 */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.gradient-text {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 工具提示 */
.tooltip-custom {
    position: relative;
    cursor: help;
}

.tooltip-custom::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
}

.tooltip-custom:hover::after {
    opacity: 1;
}

/* 账号详情模态框样式 */
#accountDetailsModal .modal-dialog {
    max-width: 900px;
}

#accountDetailsModal .modal-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: 3px solid #00ffff;
    border-radius: 15px;
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
    color: white;
}

#accountDetailsModal .modal-header {
    border-bottom: 2px solid #00ffff;
    background: rgba(0, 255, 255, 0.1);
}

#accountDetailsModal .modal-title {
    color: #00ffff;
    font-weight: 700;
    font-family: 'Orbitron', monospace;
    text-shadow: 0 0 10px #00ffff;
    text-transform: uppercase;
    letter-spacing: 1px;
}

#accountDetailsModal .modal-body {
    padding: 25px;
    max-height: 70vh;
    overflow-y: auto;
}

#accountDetailsModal .modal-footer {
    border-top: 2px solid #00ffff;
    background: rgba(0, 255, 255, 0.1);
}

/* 自定义滚动条 */
#accountDetailsModal .modal-body::-webkit-scrollbar {
    width: 8px;
}

#accountDetailsModal .modal-body::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

#accountDetailsModal .modal-body::-webkit-scrollbar-thumb {
    background: #00ffff;
    border-radius: 4px;
}

#accountDetailsModal .modal-body::-webkit-scrollbar-thumb:hover {
    background: #00cccc;
}

/* 武将和皮肤列表的滚动条 */
.generals-list::-webkit-scrollbar,
.skins-list::-webkit-scrollbar {
    width: 6px;
}

.generals-list::-webkit-scrollbar-track,
.skins-list::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.generals-list::-webkit-scrollbar-thumb,
.skins-list::-webkit-scrollbar-thumb {
    background: rgba(0, 255, 255, 0.5);
    border-radius: 3px;
}

.generals-list::-webkit-scrollbar-thumb:hover,
.skins-list::-webkit-scrollbar-thumb:hover {
    background: #00ffff;
}

/* 账号详情模态框响应式设计 */
@media (max-width: 991px) {
    #accountDetailsModal .modal-dialog {
        max-width: 90%;
        margin: 1rem auto;
    }

    #accountDetailsModal .modal-body {
        padding: 20px;
    }

    #accountDetailsModal .row {
        margin: 0;
    }

    #accountDetailsModal .col-md-6 {
        padding: 0 8px;
        margin-bottom: 20px;
    }
}

@media (max-width: 767px) {
    #accountDetailsModal .modal-dialog {
        max-width: 95%;
        margin: 0.5rem auto;
    }

    #accountDetailsModal .modal-body {
        padding: 15px;
        max-height: 80vh;
    }

    #accountDetailsModal .modal-title {
        font-size: 1.1rem;
        letter-spacing: 0.5px;
    }

    #accountDetailsModal .col-md-6 {
        padding: 0 5px;
        margin-bottom: 15px;
    }

    #accountDetailsModal h6 {
        font-size: 1rem;
        margin-bottom: 10px;
    }

    .generals-list, .skins-list {
        max-height: 150px;
        padding: 8px;
    }

    .general-tag, .skin-tag {
        font-size: 11px !important;
        padding: 4px 8px !important;
        margin: 2px !important;
    }
}

@media (max-width: 575px) {
    #accountDetailsModal .modal-dialog {
        max-width: 98%;
        margin: 0.25rem auto;
    }

    #accountDetailsModal .modal-body {
        padding: 10px;
        max-height: 85vh;
    }

    #accountDetailsModal .modal-title {
        font-size: 1rem;
        letter-spacing: 0px;
    }

    #accountDetailsModal .modal-header {
        padding: 10px 15px;
    }

    #accountDetailsModal .modal-footer {
        padding: 10px 15px;
    }

    #accountDetailsModal .col-md-6 {
        padding: 0;
        margin-bottom: 12px;
    }

    #accountDetailsModal h6 {
        font-size: 0.9rem;
        margin-bottom: 8px;
    }

    #accountDetailsModal .card-body {
        padding: 10px;
    }

    #accountDetailsModal .mb-2 {
        margin-bottom: 0.5rem !important;
        font-size: 0.85rem;
    }

    .generals-list, .skins-list {
        max-height: 120px;
        padding: 6px;
    }

    .general-tag, .skin-tag {
        font-size: 10px !important;
        padding: 3px 6px !important;
        margin: 1px !important;
    }

    /* 账号价值区域在手机上的特殊样式 */
    #accountDetailsModal .text-center div[style*="font-size: 2.5rem"] {
        font-size: 1.8rem !important;
    }

    #accountDetailsModal .badge {
        font-size: 10px !important;
        padding: 6px 12px !important;
    }
}

/* 优化后的账号卡片样式 */
.sgs-account-card {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(0, 0, 0, 0.3));
    border: 2px solid var(--cyber-cyan);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.sgs-account-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.sgs-account-card:hover::before {
    left: 100%;
}

.sgs-account-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
    border-color: var(--cyber-pink);
}

.sgs-account-card.premium {
    border-color: var(--cyber-pink);
    background: linear-gradient(135deg, rgba(255, 0, 128, 0.1), rgba(0, 0, 0, 0.3));
}

.sgs-account-card.premium:hover {
    box-shadow: 0 10px 30px rgba(255, 0, 128, 0.3);
    border-color: var(--cyber-cyan);
}

.sgs-account-card.in-use {
    border-color: var(--cyber-green);
    background: linear-gradient(135deg, rgba(0, 255, 65, 0.1), rgba(0, 0, 0, 0.3));
}

.sgs-account-card.in-use:hover {
    box-shadow: 0 10px 30px rgba(0, 255, 65, 0.3);
}

/* VIP等级徽章样式 */
.sgs-badge-vip {
    background: linear-gradient(135deg, var(--cyber-pink), #ff1493) !important;
    color: var(--text-primary) !important;
    text-shadow: 0 0 5px var(--cyber-pink-glow);
    border: 1px solid #ff1493;
    animation: vip-glow 2s ease-in-out infinite alternate;
}

@keyframes vip-glow {
    from { box-shadow: 0 0 5px var(--cyber-pink); }
    to { box-shadow: 0 0 15px var(--cyber-pink), 0 0 25px var(--cyber-pink); }
}

/* 官阶徽章样式 */
.sgs-badge-rank {
    background: linear-gradient(135deg, var(--cyber-purple), #9932cc) !important;
    color: var(--text-primary) !important;
    text-shadow: 0 0 5px var(--cyber-purple);
    border: 1px solid #9932cc;
}

/* 统计项样式优化 */
.sgs-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.sgs-stat-item {
    text-align: center;
    padding: 15px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.sgs-stat-item:hover {
    background: rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
}

.sgs-stat-icon {
    font-size: 1.5rem;
    margin-bottom: 8px;
}

.sgs-stat-value {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 4px;
}

.sgs-stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* 特色标签样式 */
.sgs-feature-section {
    margin-bottom: 15px;
}

.sgs-feature-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.sgs-feature-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 12px 0;
    padding: 8px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.sgs-feature-tag {
    display: inline-block;
    padding: 8px 14px;
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 600;
    white-space: nowrap;
    transition: all 0.3s ease;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
    margin: 3px;
    position: relative;
    overflow: hidden;
}

.sgs-feature-tag:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.25);
}

.sgs-feature-tag::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.sgs-feature-tag:hover::before {
    left: 100%;
}

.sgs-general-tag {
    background: linear-gradient(135deg, var(--cyber-pink), #ff69b4);
    color: var(--text-primary);
    border: 1px solid #ff1493;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.sgs-general-tag:hover {
    background: linear-gradient(135deg, #ff69b4, var(--cyber-pink));
}

.sgs-dynamic-skin-tag {
    background: linear-gradient(135deg, var(--cyber-purple), #ba55d3);
    color: var(--text-primary);
    border: 1px solid #9932cc;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.sgs-dynamic-skin-tag:hover {
    background: linear-gradient(135deg, #ba55d3, var(--cyber-purple));
}

.sgs-skin-tag {
    background: linear-gradient(135deg, var(--cyber-green), #32cd32);
    color: var(--bg-primary);
    border: 1px solid #32cd32;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.sgs-skin-tag:hover {
    background: linear-gradient(135deg, #32cd32, var(--cyber-green));
}

/* 移动端优化 */
@media (max-width: 768px) {
    .sgs-feature-list {
        gap: 8px;
        padding: 6px;
    }

    .sgs-feature-tag {
        padding: 6px 10px;
        font-size: 0.7rem;
        margin: 2px;
    }

    .sgs-feature-tag:hover {
        transform: translateY(-1px) scale(1.02);
    }
}

/* 账号类型头部样式 */
.sgs-account-type-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.sgs-account-type-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
}

.sgs-account-type-badge.premium-type {
    background: var(--cyber-pink);
    color: var(--text-primary);
    border: 1px solid #ff1493;
}

.sgs-account-type-badge.normal-type {
    background: var(--cyber-cyan);
    color: var(--bg-primary);
    border: 1px solid #00ffff;
}

.sgs-status-badge {
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
}

.sgs-status-badge.sgs-status-available {
    background: var(--cyber-green);
    color: var(--bg-primary);
}

.sgs-status-badge.sgs-status-in-use {
    background: #ff6b35;
    color: var(--text-primary);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* 账号头部信息样式 */
.sgs-account-header {
    margin-bottom: 20px;
}

.sgs-account-title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sgs-level-badge {
    background: linear-gradient(135deg, var(--cyber-cyan), #0080ff);
    color: var(--bg-primary);
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    border: 1px solid #0080ff;
}

.sgs-account-name {
    color: var(--text-primary);
    font-size: 1.3rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.sgs-badges {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.sgs-badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* 操作按钮区域 */
.sgs-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.sgs-btn {
    padding: 8px 16px;
    border-radius: 6px;
    border: none;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.sgs-btn-primary {
    background: var(--cyber-cyan);
    color: var(--bg-primary);
}

.sgs-btn-primary:hover {
    background: #00cccc;
    transform: translateY(-1px);
}

.sgs-btn-outline {
    background: transparent;
    color: var(--text-primary);
    border: 1px solid var(--cyber-cyan);
}

.sgs-btn-outline:hover {
    background: var(--cyber-cyan);
    color: var(--bg-primary);
}

.sgs-btn-success {
    background: var(--cyber-green);
    color: var(--bg-primary);
}

.sgs-btn-warning {
    background: #ffa500;
    color: var(--bg-primary);
}
