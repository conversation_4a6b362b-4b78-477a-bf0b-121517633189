<?php
require_once __DIR__ . '/../inc/db.php';
require_admin_login();

$err = $ok = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $old = (string)($_POST['old'] ?? '');
    $new = (string)($_POST['new'] ?? '');
    if ($old === '' || $new === '') {
        $err = '请输入旧密码与新密码';
    } else {
        $stmt = db()->prepare('SELECT * FROM admins WHERE id=?');
        $stmt->execute([$_SESSION['admin_id']]);
        $row = $stmt->fetch();
        if (!$row || !password_verify($old, $row['password_hash'])) {
            $err = '旧密码不正确';
        } else {
            $hash = password_hash($new, PASSWORD_DEFAULT);
            $stmt = db()->prepare('UPDATE admins SET password_hash=? WHERE id=?');
            $stmt->execute([$hash, $_SESSION['admin_id']]);
            $ok = '密码已更新，请使用新密码重新登录';
        }
    }
}
?>
<!doctype html>
<html lang="zh-CN">
<head>
<meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1">
<title>修改管理员密码</title>
<style>
body{font-family:system-ui,-apple-system,Segoe UI,Roboto,Helvetica,Arial; margin:0; background:#f7f8fb}
.wrap{max-width:600px; margin:40px auto; background:#fff; padding:20px; border-radius:8px; box-shadow:0 2px 12px rgba(0,0,0,.05)}
label{display:block; font-weight:600; margin:10px 0 6px}
input{width:100%; padding:10px; border:1px solid #dcdfe6; border-radius:6px}
.btn{margin-top:14px; padding:10px 14px; border:0; background:#2d74ff; color:#fff; border-radius:6px; cursor:pointer}
.msg{margin:8px 0; padding:8px 10px; border-radius:6px}
.err{background:#fff3f3; border:1px solid #ffd0d0; color:#b80000}
.ok{background:#eefbf2; border:1px solid #bde5c8; color:#176b3b}
</style>
</head>
<body>
  <div class="wrap">
    <h2>修改管理员密码</h2>
    <?php if ($err): ?><div class="msg err"><?php echo htmlspecialchars($err,ENT_QUOTES,'UTF-8'); ?></div><?php endif; ?>
    <?php if ($ok): ?><div class="msg ok"><?php echo htmlspecialchars($ok,ENT_QUOTES,'UTF-8'); ?></div><?php endif; ?>
    <form method="post">
      <label>旧密码</label>
      <input name="old" type="password" />
      <label>新密码</label>
      <input name="new" type="password" />
      <button class="btn" type="submit">保存</button>
    </form>
  </div>
</body>
</html>

