<?php
/**
 * 定时更新客户剩余时长
 * 建议每分钟执行一次
 */

// 防止通过浏览器直接访问
if (php_sapi_name() !== 'cli' && !isset($_GET['force'])) {
    // 如果不是命令行执行且没有force参数，检查是否是内部调用
    $allowedIPs = ['127.0.0.1', '::1', 'localhost'];
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
    if (!in_array($clientIP, $allowedIPs)) {
        http_response_code(403);
        die('Access denied');
    }
}

try {
    // 数据库连接（统一走 config/database.php 的 Database 类）
    require_once __DIR__ . '/../config/database.php';
    $database = new Database();
    $pdo = $database->getConnection();
    if (!$pdo) { throw new Exception('DB connect failed'); }
    
    $startTime = microtime(true);
    $logPrefix = "[" . date('Y-m-d H:i:s') . "] ";
    
    // 检查是否需要执行
    $stmt = $pdo->prepare("
        SELECT * FROM scheduled_tasks 
        WHERE task_name = 'update_remaining_time' AND is_active = 1 AND next_run <= NOW()
    ");
    $stmt->execute();
    $task = $stmt->fetch();
    
    if (!$task) {
        echo $logPrefix . "任务未到执行时间或已禁用\n";
        exit(0);
    }
    
    echo $logPrefix . "开始更新客户剩余时长...\n";
    
    // 更新剩余时长
    $stmt = $pdo->prepare("
        UPDATE customer_accounts 
        SET remaining_hours = CASE 
            WHEN first_login_time IS NULL THEN duration_hours
            WHEN expires_at IS NOT NULL THEN GREATEST(0, TIMESTAMPDIFF(SECOND, NOW(), expires_at) / 3600)
            ELSE GREATEST(0, duration_hours - TIMESTAMPDIFF(SECOND, first_login_time, NOW()) / 3600)
        END,
        status = CASE 
            WHEN first_login_time IS NOT NULL AND (
                (expires_at IS NOT NULL AND expires_at <= NOW()) OR
                (expires_at IS NULL AND TIMESTAMPDIFF(SECOND, first_login_time, NOW()) / 3600 >= duration_hours)
            ) THEN 'expired'
            WHEN status = 'expired' AND first_login_time IS NOT NULL AND (
                (expires_at IS NOT NULL AND expires_at > NOW()) OR
                (expires_at IS NULL AND TIMESTAMPDIFF(SECOND, first_login_time, NOW()) / 3600 < duration_hours)
            ) THEN 'active'
            ELSE status
        END,
        updated_at = NOW()
        WHERE status != 'banned'
    ");
    $stmt->execute();
    $updatedRows = $stmt->rowCount();
    
    // 处理过期客户的游戏账号
    $stmt = $pdo->prepare("
        UPDATE game_accounts ga
        JOIN customer_accounts ca ON ga.current_user_id = ca.id
        SET ga.current_user_id = NULL, ga.status = 'available', ga.taken_at = NULL
        WHERE ca.status = 'expired' AND ga.status = 'in_use'
    ");
    $stmt->execute();
    $releasedAccounts = $stmt->rowCount();
    
    // 记录过期客户释放账号的操作日志
    if ($releasedAccounts > 0) {
        $stmt = $pdo->prepare("
            INSERT INTO operation_logs (user_type, user_id, action, description, ip_address, created_at)
            SELECT 'system', 0, 'auto_release_expired', 
                   CONCAT('系统自动释放过期客户的游戏账号，共释放 ', ?, ' 个账号'), 
                   'system', NOW()
        ");
        $stmt->execute([$releasedAccounts]);
    }
    
    // 更新任务执行时间
    $nextRun = date('Y-m-d H:i:s', time() + $task['run_interval']);
    $stmt = $pdo->prepare("
        UPDATE scheduled_tasks 
        SET last_run = NOW(), next_run = ? 
        WHERE task_name = 'update_remaining_time'
    ");
    $stmt->execute([$nextRun]);
    
    $endTime = microtime(true);
    $executionTime = round(($endTime - $startTime) * 1000, 2);
    
    echo $logPrefix . "更新完成: 更新了 {$updatedRows} 个客户账号, 释放了 {$releasedAccounts} 个游戏账号, 耗时 {$executionTime}ms\n";
    
    // 如果是通过浏览器访问，返回JSON格式
    if (php_sapi_name() !== 'cli') {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => '更新完成',
            'updated_customers' => $updatedRows,
            'released_accounts' => $releasedAccounts,
            'execution_time_ms' => $executionTime
        ]);
    }
    
} catch (Exception $e) {
    $errorMsg = $logPrefix . "更新失败: " . $e->getMessage() . "\n";
    echo $errorMsg;
    error_log($errorMsg);
    
    if (php_sapi_name() !== 'cli') {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => '更新失败: ' . $e->getMessage()
        ]);
    }
    
    exit(1);
}
?>
