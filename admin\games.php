<?php
require_once __DIR__ . '/../inc/db.php';
require_admin_login();
$pdo = db();

$action = $_GET['action'] ?? '';
function redirect_list(){ header('Location: /admin/games.php'); exit; }

// 数据转换函数
function convertToJsonArray($input) {
    if (empty($input)) {
        return '[]';
    }

    // 如果已经是JSON格式，直接返回
    if (is_string($input) && (strpos($input, '[') === 0 || strpos($input, '{') === 0)) {
        $decoded = json_decode($input, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return $input;
        }
    }

    // 将逗号分隔的字符串转换为JSON数组
    $items = explode(',', $input);
    $items = array_map('trim', $items);
    $items = array_filter($items, function($item) {
        return !empty($item);
    });

    return json_encode(array_values($items), JSON_UNESCAPED_UNICODE);
}

// 自动计算数量
function calculateCount($details) {
    if (empty($details)) {
        return 0;
    }

    // 如果是JSON格式
    if (strpos($details, '[') === 0) {
        $decoded = json_decode($details, true);
        return is_array($decoded) ? count($decoded) : 0;
    }

    // 如果是逗号分隔
    $items = explode(',', $details);
    $items = array_filter(array_map('trim', $items));
    return count($items);
}

if ($_SERVER['REQUEST_METHOD']==='POST') {
    if ($action==='create') {
        $data = [
            'account'=>trim($_POST['account']??''),
            'password'=>trim($_POST['password']??''),
            'account_name'=>trim($_POST['account_name']??''),
            'level'=>(int)($_POST['level']??0),
            'vip_level'=>(int)($_POST['vip_level']??0),
            'rank'=>trim($_POST['rank']??''),
            'nation_war'=>(int)($_POST['nation_war']??0), // 改为数字类型
            'gender'=>trim($_POST['gender']??'未知'),
            'price'=>($_POST['price']!==''? (float)$_POST['price'] : 0),
            'account_type'=>trim($_POST['account_type']??'normal'),
            'general_details'=>trim($_POST['general_details']??''),
            'skin_details'=>trim($_POST['skin_details']??''),
            'epic_generals'=>trim($_POST['epic_generals']??''),
            'dynamic_skin_details'=>trim($_POST['dynamic_skin_details']??''),
        ];

        // 自动转换为JSON格式并计算数量
        $data['premium_generals'] = convertToJsonArray($data['epic_generals']);
        $data['dynamic_skins'] = convertToJsonArray($data['dynamic_skin_details']);
        $data['skin'] = $data['skin_details']; // 保持原格式用于兼容

        // 自动计算数量
        $data['general_count'] = calculateCount($data['general_details']);
        $data['epic_general_count'] = calculateCount($data['epic_generals']);
        $data['skin_count'] = calculateCount($data['skin_details']);
        $data['dynamic_skin_count'] = calculateCount($data['dynamic_skin_details']);

        $stmt = $pdo->prepare('INSERT INTO game_accounts (account, password, account_name, level, vip_level, `rank`, nation_war, skin, gender, price, premium_generals, dynamic_skins, general_count, skin_count, epic_general_count, dynamic_skin_count, epic_generals, dynamic_skin_details, general_details, skin_details, account_type, status, created_at) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,"available",NOW())');
        $stmt->execute([$data['account'],$data['password'],$data['account_name'],$data['level'],$data['vip_level'],$data['rank'],$data['nation_war'],$data['skin'],$data['gender'],$data['price'],$data['premium_generals'],$data['dynamic_skins'],$data['general_count'],$data['skin_count'],$data['epic_general_count'],$data['dynamic_skin_count'],$data['epic_generals'],$data['dynamic_skin_details'],$data['general_details'],$data['skin_details'],$data['account_type']]);
        log_event('admin', $_SESSION['admin_id'], 'game_create', ['acc'=>$data['account']]);
        redirect_list();
    }
    if ($action==='update') {
        $id = (int)($_POST['id'] ?? 0);

        // 处理基本字段
        $updateData = [];
        $basicFields = [
            'password', 'account_name', 'level', 'vip_level', 'rank', 'gender', 'price', 'account_type'
        ];

        foreach ($basicFields as $field) {
            if (isset($_POST[$field])) {
                $updateData[$field] = $_POST[$field] === '' ? null : $_POST[$field];
            }
        }

        // 处理国战将池数量
        if (isset($_POST['nation_war'])) {
            $updateData['nation_war'] = (int)($_POST['nation_war'] ?? 0);
        }

        // 处理详情字段并自动转换
        $detailFields = ['general_details', 'skin_details', 'epic_generals', 'dynamic_skin_details'];
        foreach ($detailFields as $field) {
            if (isset($_POST[$field])) {
                $updateData[$field] = trim($_POST[$field]);

                // 自动计算对应的数量字段
                if ($field === 'general_details') {
                    $updateData['general_count'] = calculateCount($_POST[$field]);
                } elseif ($field === 'skin_details') {
                    $updateData['skin_count'] = calculateCount($_POST[$field]);
                    $updateData['skin'] = $_POST[$field]; // 兼容字段
                } elseif ($field === 'epic_generals') {
                    $updateData['epic_general_count'] = calculateCount($_POST[$field]);
                    $updateData['premium_generals'] = convertToJsonArray($_POST[$field]); // 转换为JSON
                } elseif ($field === 'dynamic_skin_details') {
                    $updateData['dynamic_skin_count'] = calculateCount($_POST[$field]);
                    $updateData['dynamic_skins'] = convertToJsonArray($_POST[$field]); // 转换为JSON
                }
            }
        }

        // 构建SQL
        $set = [];
        $vals = [];
        foreach ($updateData as $field => $value) {
            $columnName = $field === 'rank' ? '`rank`' : $field;
            $set[] = "$columnName=?";
            $vals[] = $value;
        }

        $vals[]=$id;
        if ($set) {
            $pdo->prepare('UPDATE game_accounts SET '.implode(',', $set).', updated_at=NOW() WHERE id=?')->execute($vals);
            log_event('admin', $_SESSION['admin_id'], 'game_update', ['id'=>$id]);
        }
        redirect_list();
    }
}

if ($action==='delete' && isset($_GET['id'])) {
    $id = (int)$_GET['id'];
    $pdo->prepare('DELETE FROM game_accounts WHERE id=?')->execute([$id]);
    log_event('admin', $_SESSION['admin_id'], 'game_delete', ['id'=>$id]);
    redirect_list();
}

$games = $pdo->query('SELECT * FROM game_accounts ORDER BY id DESC')->fetchAll();
?>
<!doctype html>
<html lang="zh-CN">
<head>
<meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1">
<title>游戏账号管理</title>
<style>
body{font-family:system-ui,-apple-system,Segoe UI,Roboto,Helvetica,Arial; margin:0; background:#f7f8fb}
.top{background:#fff; padding:12px 16px; display:flex; justify-content:space-between; align-items:center; box-shadow:0 2px 12px rgba(0,0,0,.05)}
.container{padding:16px; max-width:1400px; margin:0 auto}
.table{width:100%; border-collapse:collapse; margin-top:20px}
.table th,.table td{padding:8px; border-bottom:1px solid #eee; text-align:left; font-size:12px}
input,select,textarea{padding:6px 8px; border:1px solid #dcdfe6; border-radius:6px; font-size:12px}
textarea{resize:vertical; min-height:60px}
.btn{padding:8px 16px; border:0; background:#2d74ff; color:#fff; border-radius:6px; cursor:pointer; font-size:14px}
.btn:hover{background:#1e5bff}
.a{color:#2d74ff; text-decoration:none}
.form-grid{display:grid; grid-template-columns:repeat(auto-fit, minmax(300px, 1fr)); gap:20px; margin-bottom:20px}
.form-section{background:#fff; padding:20px; border-radius:8px; box-shadow:0 2px 8px rgba(0,0,0,.1)}
.form-section h4{margin:0 0 15px 0; color:#333; border-bottom:2px solid #2d74ff; padding-bottom:8px}
.form-row{display:flex; gap:10px; margin-bottom:12px; align-items:center}
.form-row label{min-width:100px; font-weight:500; color:#555}
.form-row input, .form-row select, .form-row textarea{flex:1}
.form-row.full{flex-direction:column; align-items:stretch}
.form-row.full label{margin-bottom:5px}
.small-input{width:80px !important; flex:none !important}
.medium-input{width:120px !important; flex:none !important}
.edit-form{background:#f9f9f9; padding:15px; border-radius:6px; margin-top:10px}
.edit-form .form-row{margin-bottom:8px}
.status-badge{padding:4px 8px; border-radius:4px; font-size:11px; font-weight:500}
.status-available{background:#e7f5e7; color:#2d8f2d}
.status-in-use{background:#fff3cd; color:#856404}
.status-banned{background:#f8d7da; color:#721c24}
</style>
</head>
<body>
  <div class="top">
    <div>游戏账号管理</div>
    <div><a class="a" href="/admin/dashboard.php">返回面板</a></div>
  </div>
  <div class="container">
    <h2 style="margin-bottom:20px; color:#333">新增游戏账号</h2>
    <form method="post" action="?action=create">
      <div class="form-grid">
        <!-- 基本信息 -->
        <div class="form-section">
          <h4>🎮 基本信息</h4>
          <div class="form-row">
            <label>游戏账号*</label>
            <input name="account" required placeholder="请输入游戏登录账号" />
          </div>
          <div class="form-row">
            <label>游戏密码*</label>
            <input name="password" required placeholder="请输入游戏密码" />
          </div>
          <div class="form-row">
            <label>账号名称</label>
            <input name="account_name" placeholder="游戏内角色名称" />
          </div>
          <div class="form-row">
            <label>官阶</label>
            <input name="rank" placeholder="如：王者官阶、钻石官阶等" />
          </div>
          <div class="form-row">
            <label>国战将池数量</label>
            <input name="nation_war" type="number" min="0" max="999" placeholder="0" value="0">
            <small style="color: #666; font-size: 12px; display: block; margin-top: 5px;">💡 国战中可用的武将数量，0表示未参与国战</small>
          </div>
          <div class="form-row">
            <label>性别</label>
            <select name="gender">
              <option value="未知">未知</option>
              <option value="男">男</option>
              <option value="女">女</option>
            </select>
          </div>
        </div>

        <!-- 等级信息 -->
        <div class="form-section">
          <h4>⭐ 等级信息</h4>
          <div class="form-row">
            <label>等级</label>
            <input name="level" type="number" min="0" max="999" class="small-input" placeholder="0" />
          </div>
          <div class="form-row">
            <label>VIP等级</label>
            <input name="vip_level" type="number" min="0" max="15" class="small-input" placeholder="0" />
          </div>
          <div class="form-row">
            <label>账号出售价格</label>
            <input name="price" type="number" step="0.01" min="0" class="medium-input" placeholder="0.00" />
          </div>
          <div class="form-row">
            <label>账号区域</label>
            <select name="account_type">
              <option value="normal">普通区域</option>
              <option value="premium">高级区域</option>
            </select>
          </div>
        </div>
      </div>

      <div class="form-grid">
        <!-- 武将信息 -->
        <div class="form-section">
          <h4>👥 武将信息</h4>
          <div class="form-row full">
            <label>武将详情</label>
            <textarea name="general_details" placeholder="请输入所有武将名称，用逗号分隔，如：赵云,关羽,张飞,诸葛亮"></textarea>
            <small style="color: #28a745; font-size: 12px; display: block; margin-top: 5px;">✅ 直接用逗号分隔即可，系统会自动处理格式和数量统计</small>
          </div>
          <div class="form-row full">
            <label>史诗武将详情</label>
            <textarea name="epic_generals" placeholder="请输入史诗武将名称，用逗号分隔，如：赵云,关羽,张飞"></textarea>
            <small style="color: #28a745; font-size: 12px; display: block; margin-top: 5px;">✅ 直接用逗号分隔即可，系统会自动转换为JSON格式并统计数量</small>
          </div>
        </div>

        <!-- 皮肤信息 -->
        <div class="form-section">
          <h4>🎨 皮肤信息</h4>
          <div class="form-row full">
            <label>皮肤详情</label>
            <textarea name="skin_details" placeholder="请输入所有皮肤名称，用逗号分隔，如：貂蝉-闭月,大乔-国色"></textarea>
            <small style="color: #28a745; font-size: 12px; display: block; margin-top: 5px;">✅ 直接用逗号分隔即可，系统会自动处理格式和数量统计</small>
          </div>
          <div class="form-row full">
            <label>动态皮肤详情</label>
            <textarea name="dynamic_skin_details" placeholder="请输入动态皮肤名称，用逗号分隔，如：赵云-引雷,关羽-武圣"></textarea>
            <small style="color: #28a745; font-size: 12px; display: block; margin-top: 5px;">✅ 直接用逗号分隔即可，系统会自动转换为JSON格式并统计数量</small>
          </div>
        </div>
      </div>

      <div style="text-align:center; margin-top:20px">
        <button class="btn" type="submit">✅ 添加账号</button>
      </div>
    </form>

    <h2 style="margin:40px 0 20px 0; color:#333">📋 账号列表</h2>
    <table class="table">
      <tr>
        <th>ID</th>
        <th>账号</th>
        <th>密码</th>
        <th>名称</th>
        <th>等级/VIP</th>
        <th>官阶</th>
        <th>国战将池</th>
        <th>区域</th>
        <th>武将/皮肤</th>
        <th>史诗/动态</th>
        <th>价格</th>
        <th>状态</th>
        <th>操作</th>
      </tr>
      <?php foreach ($games as $g): ?>
        <tr>
          <td><?=$g['id']?></td>
          <td style="font-family:monospace"><?=htmlspecialchars($g['account']??'',ENT_QUOTES,'UTF-8')?></td>
          <td style="font-family:monospace"><?=htmlspecialchars($g['password'],ENT_QUOTES,'UTF-8')?></td>
          <td><?=htmlspecialchars($g['account_name']??'',ENT_QUOTES,'UTF-8')?></td>
          <td>Lv.<?=$g['level']?> / VIP<?=$g['vip_level']?></td>
          <td><?=htmlspecialchars($g['rank']??'',ENT_QUOTES,'UTF-8')?></td>
          <td>
            <?php
              $poolCount = (int)($g['nation_war'] ?? 0);
              if ($poolCount === 0) {
                echo '<span style="color: #666;">未参与</span>';
              } else {
                $level = '';
                $color = '';
                if ($poolCount <= 10) {
                  $level = '新手';
                  $color = '#95a5a6';
                } elseif ($poolCount <= 30) {
                  $level = '进阶';
                  $color = '#3498db';
                } elseif ($poolCount <= 50) {
                  $level = '高级';
                  $color = '#e74c3c';
                } else {
                  $level = '顶级';
                  $color = '#f39c12';
                }
                echo "<span style='color: $color; font-weight: bold;'>{$poolCount}个 ($level)</span>";
              }
            ?>
          </td>
          <td>
            <span class="status-badge <?=$g['account_type']==='premium'?'status-in-use':'status-available'?>">
              <?=$g['account_type']==='premium'?'高级':'普通'?>
            </span>
          </td>
          <td><?=$g['general_count']??0?> / <?=$g['skin_count']??0?></td>
          <td><?=$g['epic_general_count']??0?> / <?=$g['dynamic_skin_count']??0?></td>
          <td>¥<?=number_format($g['price']??0, 2)?></td>
          <td>
            <span class="status-badge status-<?=$g['status']?>">
              <?php
                $statusMap = ['available'=>'可用', 'in_use'=>'使用中', 'banned'=>'禁用', 'reported'=>'举报'];
                echo $statusMap[$g['status']] ?? $g['status'];
              ?>
            </span>
          </td>
          <td>
            <details>
              <summary style="cursor:pointer; color:#2d74ff; font-weight:500">📝 编辑</summary>
              <div class="edit-form">
                <form method="post" action="?action=update">
                  <input type="hidden" name="id" value="<?=$g['id']?>" />

                  <div style="display:grid; grid-template-columns:1fr 1fr; gap:15px">
                    <div>
                      <div class="form-row">
                        <label>游戏密码</label>
                        <input name="password" value="<?=htmlspecialchars($g['password'],ENT_QUOTES,'UTF-8')?>" />
                      </div>
                      <div class="form-row">
                        <label>账号名称</label>
                        <input name="account_name" value="<?=htmlspecialchars($g['account_name']??'',ENT_QUOTES,'UTF-8')?>" />
                      </div>
                      <div class="form-row">
                        <label>等级</label>
                        <input name="level" type="number" value="<?=$g['level']?>" class="small-input" />
                      </div>
                      <div class="form-row">
                        <label>VIP等级</label>
                        <input name="vip_level" type="number" value="<?=$g['vip_level']?>" class="small-input" />
                      </div>
                      <div class="form-row">
                        <label>官阶</label>
                        <input name="rank" value="<?=htmlspecialchars($g['rank']??'',ENT_QUOTES,'UTF-8')?>" />
                      </div>
                      <div class="form-row">
                        <label>国战将池数量</label>
                        <input name="nation_war" type="number" min="0" max="999" value="<?=htmlspecialchars($g['nation_war'] ?? '0')?>" placeholder="0">
                        <small style="color: #666; font-size: 12px;">国战中可用的武将数量，0表示未参与国战</small>
                      </div>
                      <div class="form-row">
                        <label>性别</label>
                        <select name="gender">
                          <option value="未知" <?=$g['gender']==='未知'?'selected':''?>>未知</option>
                          <option value="男" <?=$g['gender']==='男'?'selected':''?>>男</option>
                          <option value="女" <?=$g['gender']==='女'?'selected':''?>>女</option>
                        </select>
                      </div>
                      <div class="form-row">
                        <label>价格</label>
                        <input name="price" type="number" step="0.01" value="<?=$g['price']?>" class="medium-input" />
                      </div>
                      <div class="form-row">
                        <label>账号区域</label>
                        <select name="account_type">
                          <option value="normal" <?=$g['account_type']==='normal'?'selected':''?>>普通区域</option>
                          <option value="premium" <?=$g['account_type']==='premium'?'selected':''?>>高级区域</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <div class="form-row">
                        <label>武将数量</label>
                        <input name="general_count" type="number" value="<?=$g['general_count']?>" class="small-input" />
                      </div>
                      <div class="form-row full">
                        <label>武将详情</label>
                        <textarea name="general_details"><?=htmlspecialchars($g['general_details']??$g['skin']??'',ENT_QUOTES,'UTF-8')?></textarea>
                      </div>
                      <div class="form-row">
                        <label>皮肤数量</label>
                        <input name="skin_count" type="number" value="<?=$g['skin_count']?>" class="small-input" />
                      </div>
                      <div class="form-row full">
                        <label>皮肤详情</label>
                        <textarea name="skin_details"><?=htmlspecialchars($g['skin_details']??$g['skin']??'',ENT_QUOTES,'UTF-8')?></textarea>
                      </div>
                      <div class="form-row">
                        <label>史诗武将数量</label>
                        <input name="epic_general_count" type="number" value="<?=$g['epic_general_count']??0?>" class="small-input" />
                      </div>
                      <div class="form-row full">
                        <label>史诗武将详情</label>
                        <textarea name="epic_generals"><?=htmlspecialchars($g['epic_generals']??$g['premium_generals']??'',ENT_QUOTES,'UTF-8')?></textarea>
                      </div>
                      <div class="form-row">
                        <label>动态皮肤数量</label>
                        <input name="dynamic_skin_count" type="number" value="<?=$g['dynamic_skin_count']??0?>" class="small-input" />
                      </div>
                      <div class="form-row full">
                        <label>动态皮肤详情</label>
                        <textarea name="dynamic_skin_details"><?=htmlspecialchars($g['dynamic_skin_details']??$g['dynamic_skins']??'',ENT_QUOTES,'UTF-8')?></textarea>
                      </div>
                    </div>
                  </div>

                  <div style="text-align:center; margin-top:15px">
                    <button class="btn" type="submit">💾 保存修改</button>
                  </div>
                </form>
              </div>
            </details>
            <a class="a" href="?action=delete&id=<?=$g['id']?>" onclick="return confirm('⚠️ 确认删除账号 <?=htmlspecialchars($g['account']??'',ENT_QUOTES,'UTF-8')?> 吗？\n\n此操作不可恢复！')" style="color:#dc3545; margin-left:10px">🗑️ 删除</a>
          </td>
        </tr>
      <?php endforeach; ?>
    </table>

    <?php if (empty($games)): ?>
      <div style="text-align:center; padding:40px; color:#666">
        <p style="font-size:18px">📭 暂无游戏账号</p>
        <p>请使用上方表单添加第一个游戏账号</p>
      </div>
    <?php endif; ?>
  </div>

  <script>
    // 自动计算数量
    function autoCalculateCount(detailsInput, countInput) {
      detailsInput.addEventListener('input', function() {
        const text = this.value.trim();
        if (text === '') {
          countInput.value = 0;
        } else {
          const count = text.split(',').filter(item => item.trim() !== '').length;
          countInput.value = count;
        }
      });
    }

    // 为所有相关输入框添加自动计算功能
    document.addEventListener('DOMContentLoaded', function() {
      // 新增表单
      const generalDetails = document.querySelector('textarea[name="general_details"]');
      const generalCount = document.querySelector('input[name="general_count"]');
      if (generalDetails && generalCount) {
        autoCalculateCount(generalDetails, generalCount);
      }

      const skinDetails = document.querySelector('textarea[name="skin_details"]');
      const skinCount = document.querySelector('input[name="skin_count"]');
      if (skinDetails && skinCount) {
        autoCalculateCount(skinDetails, skinCount);
      }

      const epicGenerals = document.querySelector('textarea[name="epic_generals"]');
      const epicCount = document.querySelector('input[name="epic_general_count"]');
      if (epicGenerals && epicCount) {
        autoCalculateCount(epicGenerals, epicCount);
      }

      const dynamicSkinDetails = document.querySelector('textarea[name="dynamic_skin_details"]');
      const dynamicSkinCount = document.querySelector('input[name="dynamic_skin_count"]');
      if (dynamicSkinDetails && dynamicSkinCount) {
        autoCalculateCount(dynamicSkinDetails, dynamicSkinCount);
      }

      // 编辑表单中的自动计算
      document.querySelectorAll('textarea[name="general_details"], textarea[name="skin_details"], textarea[name="epic_generals"], textarea[name="dynamic_skin_details"]').forEach(textarea => {
        const form = textarea.closest('form');
        if (form) {
          const name = textarea.name;
          let countInputName = '';
          if (name === 'general_details') countInputName = 'general_count';
          else if (name === 'skin_details') countInputName = 'skin_count';
          else if (name === 'epic_generals') countInputName = 'epic_general_count';
          else if (name === 'dynamic_skin_details') countInputName = 'dynamic_skin_count';

          const countInput = form.querySelector(`input[name="${countInputName}"]`);
          if (countInput) {
            autoCalculateCount(textarea, countInput);
          }
        }
      });
    });
  </script>
</body>
</html>

