@echo off
chcp 65001 >nul
title 🧹 项目文件快速清理工具

echo.
echo ========================================
echo 🧹 项目文件快速清理工具
echo ========================================
echo.
echo 此脚本将安全删除以下类型的文件：
echo ✓ 测试文件 (test_*.html, test_*.php)
echo ✓ 文档文件 (*.md)
echo ✓ 演示页面 (各种测试和演示页面)
echo ✓ 临时文件 (压缩包、备份文件等)
echo.
echo ⚠️  核心系统文件将被保护，不会删除
echo.

pause

echo.
echo 🚀 开始清理文件...
echo.

set /a deleted_count=0
set /a not_found_count=0

:: 删除测试文件
echo 📋 清理测试文件...
if exist "test_admin_login.html" (
    del "test_admin_login.html" && echo ✓ 删除: test_admin_login.html && set /a deleted_count+=1
) else (
    echo ❌ 不存在: test_admin_login.html && set /a not_found_count+=1
)

if exist "test_count_display.html" (
    del "test_count_display.html" && echo ✓ 删除: test_count_display.html && set /a deleted_count+=1
) else (
    echo ❌ 不存在: test_count_display.html && set /a not_found_count+=1
)

if exist "test_data_parsing.html" (
    del "test_data_parsing.html" && echo ✓ 删除: test_data_parsing.html && set /a deleted_count+=1
) else (
    echo ❌ 不存在: test_data_parsing.html && set /a not_found_count+=1
)

if exist "test_password_change.html" (
    del "test_password_change.html" && echo ✓ 删除: test_password_change.html && set /a deleted_count+=1
) else (
    echo ❌ 不存在: test_password_change.html && set /a not_found_count+=1
)

if exist "test_tag_styles.html" (
    del "test_tag_styles.html" && echo ✓ 删除: test_tag_styles.html && set /a deleted_count+=1
) else (
    echo ❌ 不存在: test_tag_styles.html && set /a not_found_count+=1
)

:: 删除文档文件
echo.
echo 📚 清理文档文件...
for %%f in (
    "mobile_copy_optimization_summary.md"
    "mobile_image2_effect_summary.md"
    "mobile_image_effect_summary.md"
    "mobile_issue_solution.md"
    "mobile_optimization_summary.md"
    "optimization_summary.md"
    "desktop_style_mobile_summary.md"
    "details_optimization_summary.md"
    "final_fix_summary.md"
    "password_change_fix_summary.md"
    "password_fix_update.md"
    "rental_terms_feature_summary.md"
    "ultra_compact_optimization_summary.md"
    "universal_copy_solution.md"
) do (
    if exist %%f (
        del %%f && echo ✓ 删除: %%f && set /a deleted_count+=1
    ) else (
        echo ❌ 不存在: %%f && set /a not_found_count+=1
    )
)

:: 删除演示页面
echo.
echo 🎭 清理演示页面...
for %%f in (
    "mobile_copy_test.html"
    "mobile_image_effect_test.html"
    "mobile_optimization_test.html"
    "mobile_ultra_compact_test.html"
    "ultra_compact_mobile_test.html"
    "desktop_style_mobile_test.html"
    "details_optimization_test.html"
    "optimization_test.html"
    "rental_terms_test.html"
    "universal_copy_test.html"
    "tag_comparison.html"
    "final_tag_test.html"
    "admin_input_guide.html"
) do (
    if exist %%f (
        del %%f && echo ✓ 删除: %%f && set /a deleted_count+=1
    ) else (
        echo ❌ 不存在: %%f && set /a not_found_count+=1
    )
)

:: 删除压缩包和备份文件
echo.
echo 📦 清理压缩包和备份文件...
if exist "sanguosha.lanjuan.xin_XYk8p.zip" (
    del "sanguosha.lanjuan.xin_XYk8p.zip" && echo ✓ 删除: sanguosha.lanjuan.xin_XYk8p.zip && set /a deleted_count+=1
) else (
    echo ❌ 不存在: sanguosha.lanjuan.xin_XYk8p.zip && set /a not_found_count+=1
)

:: 清理空的日志目录
echo.
echo 📁 检查空目录...
if exist "logs" (
    dir /b "logs" | findstr "." >nul
    if errorlevel 1 (
        rmdir "logs" && echo ✓ 删除空目录: logs && set /a deleted_count+=1
    ) else (
        echo ⚠️  logs 目录不为空，跳过删除
    )
) else (
    echo ❌ 不存在: logs 目录 && set /a not_found_count+=1
)

:: 显示清理结果
echo.
echo ========================================
echo 🎉 清理完成！
echo ========================================
echo.
echo 📊 清理统计：
echo ✅ 成功删除: %deleted_count% 个文件/目录
echo ❌ 文件不存在: %not_found_count% 个
echo.
echo 🛡️  所有核心系统文件已受到保护
echo 🚀 系统功能不会受到影响
echo.

if %deleted_count% gtr 0 (
    echo ✨ 项目已清理完成，可以正常使用！
) else (
    echo ℹ️  没有找到需要清理的文件，项目已经很干净了！
)

echo.
pause
